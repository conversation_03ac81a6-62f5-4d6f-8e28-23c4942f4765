# Ekvayu Theme System Documentation

## Overview
The Ekvayu app now includes a comprehensive dark mode and light mode theming system that provides users with three theme options:
- **Light Mode**: Traditional light theme with bright backgrounds
- **Dark Mode**: Dark theme optimized for low-light environments
- **System Default**: Automatically follows the device's system theme settings

## Architecture

### 1. ThemeManager (Singleton)
**Location**: `app/src/main/java/com/tech/ekvayu/BaseClass/ThemeManager.kt`

**Key Features**:
- Centralized theme management
- Persistent theme preferences using SharedPreferences
- System theme detection
- Theme switching functionality

**Main Methods**:
```kotlin
// Apply a specific theme
ThemeManager.applyTheme(ThemeManager.ThemeMode.DARK)

// Get current theme
val currentTheme = ThemeManager.getCurrentTheme()

// Check if dark mode is active
val isDark = ThemeManager.isDarkMode(context)

// Toggle between themes
ThemeManager.toggleTheme()

// Initialize theme on app startup
ThemeManager.initializeTheme()
```

### 2. Color System
**Light Theme Colors**: `app/src/main/res/values/colors.xml`
**Dark Theme Colors**: `app/src/main/res/values-night/colors.xml`

**Adaptive Color System**:
- Colors automatically switch based on theme
- Semantic color naming (background, surface, text_primary, etc.)
- Consistent color palette across themes

### 3. Theme Resources
**Light Theme**: `app/src/main/res/values/themes.xml`
**Dark Theme**: `app/src/main/res/values-night/themes.xml`

**Features**:
- Material Design 3 compliance
- Proper status bar and navigation bar theming
- Consistent elevation and styling

## User Interface

### Theme Settings Bottom Sheet
**Location**: `app/src/main/java/com/tech/ekvayu/BottomSheet/ThemeSettingsBottomSheetFragment.kt`
**Layout**: `app/src/main/res/layout/fragment_theme_settings_bottom_sheet.xml`

**Features**:
- Radio button selection for theme modes
- Real-time theme preview
- Current theme display
- Apply and close buttons

### Home Menu Integration
The theme settings are accessible through the main dashboard menu:
- **Menu Item**: "Theme"
- **Description**: "Switch between light and dark mode"
- **Icon**: Custom theme icon with half-moon design

## Implementation Details

### 1. Application Initialization
```kotlin
// In MyApplication.kt
override fun onCreate() {
    super.onCreate()
    SharedPrefManager.initInstance(applicationContext)
    ThemeManager.initializeTheme() // Initialize theme on app startup
}
```

### 2. Theme Switching
```kotlin
// In ThemeSettingsBottomSheetFragment.kt
private fun applyTheme(themeMode: ThemeManager.ThemeMode) {
    ThemeManager.applyTheme(themeMode)
    Toast.makeText(requireContext(), "Applied ${ThemeManager.getThemeDisplayName(themeMode)}", Toast.LENGTH_SHORT).show()
}
```

### 3. Adaptive Colors Usage
```xml
<!-- In layouts, use adaptive colors -->
<TextView
    android:textColor="@color/text_primary"
    android:background="@color/surface" />
```

## Color Palette

### Light Theme
- **Background**: `#FFFFFF` (Pure white)
- **Surface**: `#F8F9FA` (Light gray)
- **Card Background**: `#FFFFFF` (White)
- **Text Primary**: `#212529` (Dark gray)
- **Text Secondary**: `#6C757D` (Medium gray)
- **App Color**: `#000080` (Navy blue)

### Dark Theme
- **Background**: `#121212` (Material dark)
- **Surface**: `#1E1E1E` (Elevated dark)
- **Card Background**: `#2D2D2D` (Card dark)
- **Text Primary**: `#FFFFFF` (White)
- **Text Secondary**: `#B3B3B3` (Light gray)
- **App Color**: `#4040B0` (Lighter navy for dark mode)

## Best Practices

### 1. Using Adaptive Colors
Always use semantic color names instead of hardcoded colors:
```xml
<!-- Good -->
<TextView android:textColor="@color/text_primary" />

<!-- Avoid -->
<TextView android:textColor="#000000" />
```

### 2. Theme-Aware Components
When creating new UI components, ensure they use adaptive colors:
```xml
<LinearLayout
    android:background="@color/surface"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    
    <TextView
        android:textColor="@color/text_primary"
        android:text="Sample Text" />
        
</LinearLayout>
```

### 3. Status Bar and Navigation Bar
The theme system automatically handles:
- Status bar color and icon tinting
- Navigation bar color and button tinting
- Window background colors

## Testing

### Manual Testing Steps
1. **Light Mode Test**:
   - Open app → Home → Theme → Select "Light Mode"
   - Verify all screens use light colors
   - Check status bar and navigation bar

2. **Dark Mode Test**:
   - Select "Dark Mode" from theme settings
   - Verify all screens use dark colors
   - Check text readability and contrast

3. **System Default Test**:
   - Select "System Default"
   - Change device theme in system settings
   - Verify app follows system theme

### Automated Testing
Consider adding UI tests for theme switching:
```kotlin
@Test
fun testThemeSwitching() {
    // Test theme switching functionality
    ThemeManager.applyTheme(ThemeManager.ThemeMode.DARK)
    assertEquals(ThemeManager.ThemeMode.DARK, ThemeManager.getCurrentTheme())
}
```

## Future Enhancements

### Potential Improvements
1. **Custom Theme Colors**: Allow users to customize accent colors
2. **Scheduled Theme Switching**: Auto-switch based on time of day
3. **High Contrast Mode**: Enhanced accessibility option
4. **Theme Animations**: Smooth transitions between themes
5. **Per-Screen Themes**: Different themes for different app sections

### Performance Considerations
- Theme switching requires activity recreation
- SharedPreferences are used for persistence (lightweight)
- Color resources are cached by Android system

## Troubleshooting

### Common Issues
1. **Theme not applying**: Ensure `ThemeManager.initializeTheme()` is called in Application class
2. **Colors not switching**: Check if using adaptive color names in layouts
3. **Status bar issues**: Verify theme attributes in themes.xml

### Debug Tips
```kotlin
// Check current theme
Log.d("Theme", "Current theme: ${ThemeManager.getCurrentTheme()}")

// Check if dark mode is active
Log.d("Theme", "Is dark mode: ${ThemeManager.isDarkMode(context)}")
```

## Conclusion
The Ekvayu theme system provides a robust, user-friendly way to switch between light and dark modes while maintaining design consistency and following Material Design guidelines. The system is extensible and can be enhanced with additional theme options in the future.
