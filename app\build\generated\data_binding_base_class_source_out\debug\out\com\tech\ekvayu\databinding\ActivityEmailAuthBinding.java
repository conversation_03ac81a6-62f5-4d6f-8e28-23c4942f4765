// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEmailAuthBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnCheckStatus;

  @NonNull
  public final Button btnClearAuth;

  @NonNull
  public final Button btnGmailAuth;

  @NonNull
  public final Button btnOutlookAuth;

  @NonNull
  public final Button btnYahooAuth;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvAuthStatus;

  @NonNull
  public final TextView tvDescription;

  @NonNull
  public final TextView tvFeatures;

  @NonNull
  public final TextView tvFeaturesTitle;

  @NonNull
  public final TextView tvProgress;

  @NonNull
  public final TextView tvTitle;

  private ActivityEmailAuthBinding(@NonNull ScrollView rootView, @NonNull Button btnCheckStatus,
      @NonNull Button btnClearAuth, @NonNull Button btnGmailAuth, @NonNull Button btnOutlookAuth,
      @NonNull Button btnYahooAuth, @NonNull ProgressBar progressBar,
      @NonNull TextView tvAuthStatus, @NonNull TextView tvDescription, @NonNull TextView tvFeatures,
      @NonNull TextView tvFeaturesTitle, @NonNull TextView tvProgress, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnCheckStatus = btnCheckStatus;
    this.btnClearAuth = btnClearAuth;
    this.btnGmailAuth = btnGmailAuth;
    this.btnOutlookAuth = btnOutlookAuth;
    this.btnYahooAuth = btnYahooAuth;
    this.progressBar = progressBar;
    this.tvAuthStatus = tvAuthStatus;
    this.tvDescription = tvDescription;
    this.tvFeatures = tvFeatures;
    this.tvFeaturesTitle = tvFeaturesTitle;
    this.tvProgress = tvProgress;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEmailAuthBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEmailAuthBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_email_auth, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEmailAuthBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_check_status;
      Button btnCheckStatus = ViewBindings.findChildViewById(rootView, id);
      if (btnCheckStatus == null) {
        break missingId;
      }

      id = R.id.btn_clear_auth;
      Button btnClearAuth = ViewBindings.findChildViewById(rootView, id);
      if (btnClearAuth == null) {
        break missingId;
      }

      id = R.id.btn_gmail_auth;
      Button btnGmailAuth = ViewBindings.findChildViewById(rootView, id);
      if (btnGmailAuth == null) {
        break missingId;
      }

      id = R.id.btn_outlook_auth;
      Button btnOutlookAuth = ViewBindings.findChildViewById(rootView, id);
      if (btnOutlookAuth == null) {
        break missingId;
      }

      id = R.id.btn_yahoo_auth;
      Button btnYahooAuth = ViewBindings.findChildViewById(rootView, id);
      if (btnYahooAuth == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_auth_status;
      TextView tvAuthStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvAuthStatus == null) {
        break missingId;
      }

      id = R.id.tv_description;
      TextView tvDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvDescription == null) {
        break missingId;
      }

      id = R.id.tv_features;
      TextView tvFeatures = ViewBindings.findChildViewById(rootView, id);
      if (tvFeatures == null) {
        break missingId;
      }

      id = R.id.tv_features_title;
      TextView tvFeaturesTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvFeaturesTitle == null) {
        break missingId;
      }

      id = R.id.tv_progress;
      TextView tvProgress = ViewBindings.findChildViewById(rootView, id);
      if (tvProgress == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ActivityEmailAuthBinding((ScrollView) rootView, btnCheckStatus, btnClearAuth,
          btnGmailAuth, btnOutlookAuth, btnYahooAuth, progressBar, tvAuthStatus, tvDescription,
          tvFeatures, tvFeaturesTitle, tvProgress, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
