// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEmailDemoBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnAuthenticate;

  @NonNull
  public final Button btnClearEmails;

  @NonNull
  public final Button btnSyncAll;

  @NonNull
  public final Button btnSyncGmail;

  @NonNull
  public final Button btnSyncOutlook;

  @NonNull
  public final Button btnSyncYahoo;

  @NonNull
  public final LinearLayout layoutControls;

  @NonNull
  public final LinearLayout layoutProviderSync;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewEmails;

  @NonNull
  public final SwipeRefreshLayout swipeRefresh;

  @NonNull
  public final TextView tvAuthStatus;

  @NonNull
  public final TextView tvEmailCount;

  @NonNull
  public final TextView tvEmailDetails;

  @NonNull
  public final TextView tvProgress;

  @NonNull
  public final TextView tvTitle;

  private ActivityEmailDemoBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button btnAuthenticate, @NonNull Button btnClearEmails, @NonNull Button btnSyncAll,
      @NonNull Button btnSyncGmail, @NonNull Button btnSyncOutlook, @NonNull Button btnSyncYahoo,
      @NonNull LinearLayout layoutControls, @NonNull LinearLayout layoutProviderSync,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView recyclerViewEmails,
      @NonNull SwipeRefreshLayout swipeRefresh, @NonNull TextView tvAuthStatus,
      @NonNull TextView tvEmailCount, @NonNull TextView tvEmailDetails,
      @NonNull TextView tvProgress, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnAuthenticate = btnAuthenticate;
    this.btnClearEmails = btnClearEmails;
    this.btnSyncAll = btnSyncAll;
    this.btnSyncGmail = btnSyncGmail;
    this.btnSyncOutlook = btnSyncOutlook;
    this.btnSyncYahoo = btnSyncYahoo;
    this.layoutControls = layoutControls;
    this.layoutProviderSync = layoutProviderSync;
    this.progressBar = progressBar;
    this.recyclerViewEmails = recyclerViewEmails;
    this.swipeRefresh = swipeRefresh;
    this.tvAuthStatus = tvAuthStatus;
    this.tvEmailCount = tvEmailCount;
    this.tvEmailDetails = tvEmailDetails;
    this.tvProgress = tvProgress;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEmailDemoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEmailDemoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_email_demo, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEmailDemoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_authenticate;
      Button btnAuthenticate = ViewBindings.findChildViewById(rootView, id);
      if (btnAuthenticate == null) {
        break missingId;
      }

      id = R.id.btn_clear_emails;
      Button btnClearEmails = ViewBindings.findChildViewById(rootView, id);
      if (btnClearEmails == null) {
        break missingId;
      }

      id = R.id.btn_sync_all;
      Button btnSyncAll = ViewBindings.findChildViewById(rootView, id);
      if (btnSyncAll == null) {
        break missingId;
      }

      id = R.id.btn_sync_gmail;
      Button btnSyncGmail = ViewBindings.findChildViewById(rootView, id);
      if (btnSyncGmail == null) {
        break missingId;
      }

      id = R.id.btn_sync_outlook;
      Button btnSyncOutlook = ViewBindings.findChildViewById(rootView, id);
      if (btnSyncOutlook == null) {
        break missingId;
      }

      id = R.id.btn_sync_yahoo;
      Button btnSyncYahoo = ViewBindings.findChildViewById(rootView, id);
      if (btnSyncYahoo == null) {
        break missingId;
      }

      id = R.id.layout_controls;
      LinearLayout layoutControls = ViewBindings.findChildViewById(rootView, id);
      if (layoutControls == null) {
        break missingId;
      }

      id = R.id.layout_provider_sync;
      LinearLayout layoutProviderSync = ViewBindings.findChildViewById(rootView, id);
      if (layoutProviderSync == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recycler_view_emails;
      RecyclerView recyclerViewEmails = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewEmails == null) {
        break missingId;
      }

      id = R.id.swipe_refresh;
      SwipeRefreshLayout swipeRefresh = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefresh == null) {
        break missingId;
      }

      id = R.id.tv_auth_status;
      TextView tvAuthStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvAuthStatus == null) {
        break missingId;
      }

      id = R.id.tv_email_count;
      TextView tvEmailCount = ViewBindings.findChildViewById(rootView, id);
      if (tvEmailCount == null) {
        break missingId;
      }

      id = R.id.tv_email_details;
      TextView tvEmailDetails = ViewBindings.findChildViewById(rootView, id);
      if (tvEmailDetails == null) {
        break missingId;
      }

      id = R.id.tv_progress;
      TextView tvProgress = ViewBindings.findChildViewById(rootView, id);
      if (tvProgress == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ActivityEmailDemoBinding((ConstraintLayout) rootView, btnAuthenticate,
          btnClearEmails, btnSyncAll, btnSyncGmail, btnSyncOutlook, btnSyncYahoo, layoutControls,
          layoutProviderSync, progressBar, recyclerViewEmails, swipeRefresh, tvAuthStatus,
          tvEmailCount, tvEmailDetails, tvProgress, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
