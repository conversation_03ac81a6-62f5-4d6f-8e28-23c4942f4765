// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEmailDetailBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnBack;

  @NonNull
  public final Button btnDownloadRaw;

  @NonNull
  public final Button btnRefresh;

  @NonNull
  public final LinearLayout layoutContent;

  @NonNull
  public final LinearLayout layoutHeader;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvAttachments;

  @NonNull
  public final TextView tvBody;

  @NonNull
  public final TextView tvDate;

  @NonNull
  public final TextView tvFrom;

  @NonNull
  public final TextView tvHeaders;

  @NonNull
  public final TextView tvProgress;

  @NonNull
  public final TextView tvProvider;

  @NonNull
  public final TextView tvRawContent;

  @NonNull
  public final TextView tvRecipients;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvSubject;

  private ActivityEmailDetailBinding(@NonNull ScrollView rootView, @NonNull Button btnBack,
      @NonNull Button btnDownloadRaw, @NonNull Button btnRefresh,
      @NonNull LinearLayout layoutContent, @NonNull LinearLayout layoutHeader,
      @NonNull ProgressBar progressBar, @NonNull TextView tvAttachments, @NonNull TextView tvBody,
      @NonNull TextView tvDate, @NonNull TextView tvFrom, @NonNull TextView tvHeaders,
      @NonNull TextView tvProgress, @NonNull TextView tvProvider, @NonNull TextView tvRawContent,
      @NonNull TextView tvRecipients, @NonNull TextView tvStatus, @NonNull TextView tvSubject) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnDownloadRaw = btnDownloadRaw;
    this.btnRefresh = btnRefresh;
    this.layoutContent = layoutContent;
    this.layoutHeader = layoutHeader;
    this.progressBar = progressBar;
    this.tvAttachments = tvAttachments;
    this.tvBody = tvBody;
    this.tvDate = tvDate;
    this.tvFrom = tvFrom;
    this.tvHeaders = tvHeaders;
    this.tvProgress = tvProgress;
    this.tvProvider = tvProvider;
    this.tvRawContent = tvRawContent;
    this.tvRecipients = tvRecipients;
    this.tvStatus = tvStatus;
    this.tvSubject = tvSubject;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEmailDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEmailDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_email_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEmailDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      Button btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_download_raw;
      Button btnDownloadRaw = ViewBindings.findChildViewById(rootView, id);
      if (btnDownloadRaw == null) {
        break missingId;
      }

      id = R.id.btn_refresh;
      Button btnRefresh = ViewBindings.findChildViewById(rootView, id);
      if (btnRefresh == null) {
        break missingId;
      }

      id = R.id.layout_content;
      LinearLayout layoutContent = ViewBindings.findChildViewById(rootView, id);
      if (layoutContent == null) {
        break missingId;
      }

      id = R.id.layout_header;
      LinearLayout layoutHeader = ViewBindings.findChildViewById(rootView, id);
      if (layoutHeader == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_attachments;
      TextView tvAttachments = ViewBindings.findChildViewById(rootView, id);
      if (tvAttachments == null) {
        break missingId;
      }

      id = R.id.tv_body;
      TextView tvBody = ViewBindings.findChildViewById(rootView, id);
      if (tvBody == null) {
        break missingId;
      }

      id = R.id.tv_date;
      TextView tvDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDate == null) {
        break missingId;
      }

      id = R.id.tv_from;
      TextView tvFrom = ViewBindings.findChildViewById(rootView, id);
      if (tvFrom == null) {
        break missingId;
      }

      id = R.id.tv_headers;
      TextView tvHeaders = ViewBindings.findChildViewById(rootView, id);
      if (tvHeaders == null) {
        break missingId;
      }

      id = R.id.tv_progress;
      TextView tvProgress = ViewBindings.findChildViewById(rootView, id);
      if (tvProgress == null) {
        break missingId;
      }

      id = R.id.tv_provider;
      TextView tvProvider = ViewBindings.findChildViewById(rootView, id);
      if (tvProvider == null) {
        break missingId;
      }

      id = R.id.tv_raw_content;
      TextView tvRawContent = ViewBindings.findChildViewById(rootView, id);
      if (tvRawContent == null) {
        break missingId;
      }

      id = R.id.tv_recipients;
      TextView tvRecipients = ViewBindings.findChildViewById(rootView, id);
      if (tvRecipients == null) {
        break missingId;
      }

      id = R.id.tv_status;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tv_subject;
      TextView tvSubject = ViewBindings.findChildViewById(rootView, id);
      if (tvSubject == null) {
        break missingId;
      }

      return new ActivityEmailDetailBinding((ScrollView) rootView, btnBack, btnDownloadRaw,
          btnRefresh, layoutContent, layoutHeader, progressBar, tvAttachments, tvBody, tvDate,
          tvFrom, tvHeaders, tvProgress, tvProvider, tvRawContent, tvRecipients, tvStatus,
          tvSubject);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
