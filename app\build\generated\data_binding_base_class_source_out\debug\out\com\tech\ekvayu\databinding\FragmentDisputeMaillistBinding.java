// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDisputeMaillistBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AppCompatButton btRaiseDispute;

  @NonNull
  public final RecyclerView rvDisputelist;

  private FragmentDisputeMaillistBinding(@NonNull ConstraintLayout rootView,
      @NonNull AppCompatButton btRaiseDispute, @NonNull RecyclerView rvDisputelist) {
    this.rootView = rootView;
    this.btRaiseDispute = btRaiseDispute;
    this.rvDisputelist = rvDisputelist;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDisputeMaillistBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDisputeMaillistBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_dispute_maillist, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDisputeMaillistBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btRaiseDispute;
      AppCompatButton btRaiseDispute = ViewBindings.findChildViewById(rootView, id);
      if (btRaiseDispute == null) {
        break missingId;
      }

      id = R.id.rvDisputelist;
      RecyclerView rvDisputelist = ViewBindings.findChildViewById(rootView, id);
      if (rvDisputelist == null) {
        break missingId;
      }

      return new FragmentDisputeMaillistBinding((ConstraintLayout) rootView, btRaiseDispute,
          rvDisputelist);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
