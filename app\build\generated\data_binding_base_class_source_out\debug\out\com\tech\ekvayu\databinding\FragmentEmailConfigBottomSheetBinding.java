// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentEmailConfigBottomSheetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnSaveEmail;

  @NonNull
  public final Button btnShowStats;

  @NonNull
  public final TextInputEditText etPrimaryEmail;

  @NonNull
  public final TextInputLayout tilPrimaryEmail;

  @NonNull
  public final TextView tvCurrentEmail;

  @NonNull
  public final TextView tvStats;

  private FragmentEmailConfigBottomSheetBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnCancel, @NonNull Button btnSaveEmail, @NonNull Button btnShowStats,
      @NonNull TextInputEditText etPrimaryEmail, @NonNull TextInputLayout tilPrimaryEmail,
      @NonNull TextView tvCurrentEmail, @NonNull TextView tvStats) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnSaveEmail = btnSaveEmail;
    this.btnShowStats = btnShowStats;
    this.etPrimaryEmail = etPrimaryEmail;
    this.tilPrimaryEmail = tilPrimaryEmail;
    this.tvCurrentEmail = tvCurrentEmail;
    this.tvStats = tvStats;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentEmailConfigBottomSheetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentEmailConfigBottomSheetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_email_config_bottom_sheet, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentEmailConfigBottomSheetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnSaveEmail;
      Button btnSaveEmail = ViewBindings.findChildViewById(rootView, id);
      if (btnSaveEmail == null) {
        break missingId;
      }

      id = R.id.btnShowStats;
      Button btnShowStats = ViewBindings.findChildViewById(rootView, id);
      if (btnShowStats == null) {
        break missingId;
      }

      id = R.id.etPrimaryEmail;
      TextInputEditText etPrimaryEmail = ViewBindings.findChildViewById(rootView, id);
      if (etPrimaryEmail == null) {
        break missingId;
      }

      id = R.id.tilPrimaryEmail;
      TextInputLayout tilPrimaryEmail = ViewBindings.findChildViewById(rootView, id);
      if (tilPrimaryEmail == null) {
        break missingId;
      }

      id = R.id.tvCurrentEmail;
      TextView tvCurrentEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentEmail == null) {
        break missingId;
      }

      id = R.id.tvStats;
      TextView tvStats = ViewBindings.findChildViewById(rootView, id);
      if (tvStats == null) {
        break missingId;
      }

      return new FragmentEmailConfigBottomSheetBinding((LinearLayout) rootView, btnCancel,
          btnSaveEmail, btnShowStats, etPrimaryEmail, tilPrimaryEmail, tvCurrentEmail, tvStats);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
