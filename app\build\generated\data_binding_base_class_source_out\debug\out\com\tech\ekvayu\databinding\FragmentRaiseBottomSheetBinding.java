// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentRaiseBottomSheetBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final AppCompatButton btSubmit;

  @NonNull
  public final ConstraintLayout clMain;

  @NonNull
  public final CardView cvCounter;

  @NonNull
  public final CardView cvMessageId;

  @NonNull
  public final CardView cvReason;

  @NonNull
  public final CardView cvSenderEmail;

  @NonNull
  public final CardView cvStatus;

  @NonNull
  public final AppCompatEditText etCounter;

  @NonNull
  public final AppCompatEditText etMessageId;

  @NonNull
  public final AppCompatEditText etReason;

  @NonNull
  public final AppCompatEditText etSenderMail;

  @NonNull
  public final AppCompatEditText etStatus;

  @NonNull
  public final AppCompatImageView ivDisputeLogo;

  @NonNull
  public final LinearLayoutCompat llMainPending;

  @NonNull
  public final AppCompatTextView tvDisputeHeading;

  @NonNull
  public final AppCompatTextView tvMessageId;

  @NonNull
  public final AppCompatTextView tvStatusTitle;

  @NonNull
  public final AppCompatTextView tvTitleReason;

  @NonNull
  public final AppCompatTextView tvTitleSenderEmail;

  @NonNull
  public final AppCompatTextView tvTitleStatus;

  private FragmentRaiseBottomSheetBinding(@NonNull ScrollView rootView,
      @NonNull AppCompatButton btSubmit, @NonNull ConstraintLayout clMain,
      @NonNull CardView cvCounter, @NonNull CardView cvMessageId, @NonNull CardView cvReason,
      @NonNull CardView cvSenderEmail, @NonNull CardView cvStatus,
      @NonNull AppCompatEditText etCounter, @NonNull AppCompatEditText etMessageId,
      @NonNull AppCompatEditText etReason, @NonNull AppCompatEditText etSenderMail,
      @NonNull AppCompatEditText etStatus, @NonNull AppCompatImageView ivDisputeLogo,
      @NonNull LinearLayoutCompat llMainPending, @NonNull AppCompatTextView tvDisputeHeading,
      @NonNull AppCompatTextView tvMessageId, @NonNull AppCompatTextView tvStatusTitle,
      @NonNull AppCompatTextView tvTitleReason, @NonNull AppCompatTextView tvTitleSenderEmail,
      @NonNull AppCompatTextView tvTitleStatus) {
    this.rootView = rootView;
    this.btSubmit = btSubmit;
    this.clMain = clMain;
    this.cvCounter = cvCounter;
    this.cvMessageId = cvMessageId;
    this.cvReason = cvReason;
    this.cvSenderEmail = cvSenderEmail;
    this.cvStatus = cvStatus;
    this.etCounter = etCounter;
    this.etMessageId = etMessageId;
    this.etReason = etReason;
    this.etSenderMail = etSenderMail;
    this.etStatus = etStatus;
    this.ivDisputeLogo = ivDisputeLogo;
    this.llMainPending = llMainPending;
    this.tvDisputeHeading = tvDisputeHeading;
    this.tvMessageId = tvMessageId;
    this.tvStatusTitle = tvStatusTitle;
    this.tvTitleReason = tvTitleReason;
    this.tvTitleSenderEmail = tvTitleSenderEmail;
    this.tvTitleStatus = tvTitleStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentRaiseBottomSheetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentRaiseBottomSheetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_raise_bottom_sheet, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentRaiseBottomSheetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btSubmit;
      AppCompatButton btSubmit = ViewBindings.findChildViewById(rootView, id);
      if (btSubmit == null) {
        break missingId;
      }

      id = R.id.clMain;
      ConstraintLayout clMain = ViewBindings.findChildViewById(rootView, id);
      if (clMain == null) {
        break missingId;
      }

      id = R.id.cvCounter;
      CardView cvCounter = ViewBindings.findChildViewById(rootView, id);
      if (cvCounter == null) {
        break missingId;
      }

      id = R.id.cvMessageId;
      CardView cvMessageId = ViewBindings.findChildViewById(rootView, id);
      if (cvMessageId == null) {
        break missingId;
      }

      id = R.id.cvReason;
      CardView cvReason = ViewBindings.findChildViewById(rootView, id);
      if (cvReason == null) {
        break missingId;
      }

      id = R.id.cvSenderEmail;
      CardView cvSenderEmail = ViewBindings.findChildViewById(rootView, id);
      if (cvSenderEmail == null) {
        break missingId;
      }

      id = R.id.cvStatus;
      CardView cvStatus = ViewBindings.findChildViewById(rootView, id);
      if (cvStatus == null) {
        break missingId;
      }

      id = R.id.etCounter;
      AppCompatEditText etCounter = ViewBindings.findChildViewById(rootView, id);
      if (etCounter == null) {
        break missingId;
      }

      id = R.id.etMessageId;
      AppCompatEditText etMessageId = ViewBindings.findChildViewById(rootView, id);
      if (etMessageId == null) {
        break missingId;
      }

      id = R.id.etReason;
      AppCompatEditText etReason = ViewBindings.findChildViewById(rootView, id);
      if (etReason == null) {
        break missingId;
      }

      id = R.id.etSenderMail;
      AppCompatEditText etSenderMail = ViewBindings.findChildViewById(rootView, id);
      if (etSenderMail == null) {
        break missingId;
      }

      id = R.id.etStatus;
      AppCompatEditText etStatus = ViewBindings.findChildViewById(rootView, id);
      if (etStatus == null) {
        break missingId;
      }

      id = R.id.ivDisputeLogo;
      AppCompatImageView ivDisputeLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivDisputeLogo == null) {
        break missingId;
      }

      id = R.id.llMainPending;
      LinearLayoutCompat llMainPending = ViewBindings.findChildViewById(rootView, id);
      if (llMainPending == null) {
        break missingId;
      }

      id = R.id.tvDisputeHeading;
      AppCompatTextView tvDisputeHeading = ViewBindings.findChildViewById(rootView, id);
      if (tvDisputeHeading == null) {
        break missingId;
      }

      id = R.id.tvMessageId;
      AppCompatTextView tvMessageId = ViewBindings.findChildViewById(rootView, id);
      if (tvMessageId == null) {
        break missingId;
      }

      id = R.id.tvStatusTitle;
      AppCompatTextView tvStatusTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvStatusTitle == null) {
        break missingId;
      }

      id = R.id.tvTitleReason;
      AppCompatTextView tvTitleReason = ViewBindings.findChildViewById(rootView, id);
      if (tvTitleReason == null) {
        break missingId;
      }

      id = R.id.tvTitleSenderEmail;
      AppCompatTextView tvTitleSenderEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvTitleSenderEmail == null) {
        break missingId;
      }

      id = R.id.tvTitleStatus;
      AppCompatTextView tvTitleStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvTitleStatus == null) {
        break missingId;
      }

      return new FragmentRaiseBottomSheetBinding((ScrollView) rootView, btSubmit, clMain, cvCounter,
          cvMessageId, cvReason, cvSenderEmail, cvStatus, etCounter, etMessageId, etReason,
          etSenderMail, etStatus, ivDisputeLogo, llMainPending, tvDisputeHeading, tvMessageId,
          tvStatusTitle, tvTitleReason, tvTitleSenderEmail, tvTitleStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
