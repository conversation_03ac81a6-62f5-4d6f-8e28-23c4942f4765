// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWarningBottomSheetBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final AppCompatButton btPermission;

  @NonNull
  public final AppCompatImageView ivIcon;

  @NonNull
  public final AppCompatTextView tvMailAccess;

  @NonNull
  public final AppCompatTextView tvSuggetion;

  private FragmentWarningBottomSheetBinding(@NonNull ScrollView rootView,
      @NonNull AppCompatButton btPermission, @NonNull AppCompatImageView ivIcon,
      @NonNull AppCompatTextView tvMailAccess, @NonNull AppCompatTextView tvSuggetion) {
    this.rootView = rootView;
    this.btPermission = btPermission;
    this.ivIcon = ivIcon;
    this.tvMailAccess = tvMailAccess;
    this.tvSuggetion = tvSuggetion;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWarningBottomSheetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWarningBottomSheetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_warning_bottom_sheet, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWarningBottomSheetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btPermission;
      AppCompatButton btPermission = ViewBindings.findChildViewById(rootView, id);
      if (btPermission == null) {
        break missingId;
      }

      id = R.id.ivIcon;
      AppCompatImageView ivIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivIcon == null) {
        break missingId;
      }

      id = R.id.tvMailAccess;
      AppCompatTextView tvMailAccess = ViewBindings.findChildViewById(rootView, id);
      if (tvMailAccess == null) {
        break missingId;
      }

      id = R.id.tvSuggetion;
      AppCompatTextView tvSuggetion = ViewBindings.findChildViewById(rootView, id);
      if (tvSuggetion == null) {
        break missingId;
      }

      return new FragmentWarningBottomSheetBinding((ScrollView) rootView, btPermission, ivIcon,
          tvMailAccess, tvSuggetion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
