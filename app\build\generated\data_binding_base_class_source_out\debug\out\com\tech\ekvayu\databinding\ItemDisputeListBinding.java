// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDisputeListBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final AppCompatTextView tvReceiver;

  @NonNull
  public final AppCompatTextView tvSender;

  @NonNull
  public final AppCompatTextView tvStatus;

  private ItemDisputeListBinding(@NonNull CardView rootView, @NonNull AppCompatTextView tvReceiver,
      @NonNull AppCompatTextView tvSender, @NonNull AppCompatTextView tvStatus) {
    this.rootView = rootView;
    this.tvReceiver = tvReceiver;
    this.tvSender = tvSender;
    this.tvStatus = tvStatus;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDisputeListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDisputeListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_dispute_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDisputeListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tvReceiver;
      AppCompatTextView tvReceiver = ViewBindings.findChildViewById(rootView, id);
      if (tvReceiver == null) {
        break missingId;
      }

      id = R.id.tvSender;
      AppCompatTextView tvSender = ViewBindings.findChildViewById(rootView, id);
      if (tvSender == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      AppCompatTextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      return new ItemDisputeListBinding((CardView) rootView, tvReceiver, tvSender, tvStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
