// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutDisputeRaiseBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AppCompatButton btSubmit;

  @NonNull
  public final CardView cvCounter;

  @NonNull
  public final CardView cvMessageId;

  @NonNull
  public final CardView cvReason;

  @NonNull
  public final CardView cvSenderEmail;

  @NonNull
  public final CardView cvStatus;

  @NonNull
  public final AppCompatEditText etBotCounter;

  @NonNull
  public final AppCompatEditText etBotMail;

  @NonNull
  public final AppCompatEditText etBotReason;

  @NonNull
  public final AppCompatEditText etBotStatus;

  @NonNull
  public final AppCompatEditText etHashId;

  @NonNull
  public final AppCompatImageView ivDisputeLogo;

  @NonNull
  public final LinearLayoutCompat llMainPending;

  @NonNull
  public final AppCompatTextView tvDisputeHeading;

  @NonNull
  public final AppCompatTextView tvMessageId;

  @NonNull
  public final AppCompatTextView tvStatusTitle;

  @NonNull
  public final AppCompatTextView tvTitleReason;

  @NonNull
  public final AppCompatTextView tvTitleSenderEmail;

  @NonNull
  public final AppCompatTextView tvTitleStatus;

  private LayoutDisputeRaiseBinding(@NonNull ConstraintLayout rootView,
      @NonNull AppCompatButton btSubmit, @NonNull CardView cvCounter, @NonNull CardView cvMessageId,
      @NonNull CardView cvReason, @NonNull CardView cvSenderEmail, @NonNull CardView cvStatus,
      @NonNull AppCompatEditText etBotCounter, @NonNull AppCompatEditText etBotMail,
      @NonNull AppCompatEditText etBotReason, @NonNull AppCompatEditText etBotStatus,
      @NonNull AppCompatEditText etHashId, @NonNull AppCompatImageView ivDisputeLogo,
      @NonNull LinearLayoutCompat llMainPending, @NonNull AppCompatTextView tvDisputeHeading,
      @NonNull AppCompatTextView tvMessageId, @NonNull AppCompatTextView tvStatusTitle,
      @NonNull AppCompatTextView tvTitleReason, @NonNull AppCompatTextView tvTitleSenderEmail,
      @NonNull AppCompatTextView tvTitleStatus) {
    this.rootView = rootView;
    this.btSubmit = btSubmit;
    this.cvCounter = cvCounter;
    this.cvMessageId = cvMessageId;
    this.cvReason = cvReason;
    this.cvSenderEmail = cvSenderEmail;
    this.cvStatus = cvStatus;
    this.etBotCounter = etBotCounter;
    this.etBotMail = etBotMail;
    this.etBotReason = etBotReason;
    this.etBotStatus = etBotStatus;
    this.etHashId = etHashId;
    this.ivDisputeLogo = ivDisputeLogo;
    this.llMainPending = llMainPending;
    this.tvDisputeHeading = tvDisputeHeading;
    this.tvMessageId = tvMessageId;
    this.tvStatusTitle = tvStatusTitle;
    this.tvTitleReason = tvTitleReason;
    this.tvTitleSenderEmail = tvTitleSenderEmail;
    this.tvTitleStatus = tvTitleStatus;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutDisputeRaiseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutDisputeRaiseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_dispute_raise, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutDisputeRaiseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btSubmit;
      AppCompatButton btSubmit = ViewBindings.findChildViewById(rootView, id);
      if (btSubmit == null) {
        break missingId;
      }

      id = R.id.cvCounter;
      CardView cvCounter = ViewBindings.findChildViewById(rootView, id);
      if (cvCounter == null) {
        break missingId;
      }

      id = R.id.cvMessageId;
      CardView cvMessageId = ViewBindings.findChildViewById(rootView, id);
      if (cvMessageId == null) {
        break missingId;
      }

      id = R.id.cvReason;
      CardView cvReason = ViewBindings.findChildViewById(rootView, id);
      if (cvReason == null) {
        break missingId;
      }

      id = R.id.cvSenderEmail;
      CardView cvSenderEmail = ViewBindings.findChildViewById(rootView, id);
      if (cvSenderEmail == null) {
        break missingId;
      }

      id = R.id.cvStatus;
      CardView cvStatus = ViewBindings.findChildViewById(rootView, id);
      if (cvStatus == null) {
        break missingId;
      }

      id = R.id.etBotCounter;
      AppCompatEditText etBotCounter = ViewBindings.findChildViewById(rootView, id);
      if (etBotCounter == null) {
        break missingId;
      }

      id = R.id.etBotMail;
      AppCompatEditText etBotMail = ViewBindings.findChildViewById(rootView, id);
      if (etBotMail == null) {
        break missingId;
      }

      id = R.id.etBotReason;
      AppCompatEditText etBotReason = ViewBindings.findChildViewById(rootView, id);
      if (etBotReason == null) {
        break missingId;
      }

      id = R.id.etBotStatus;
      AppCompatEditText etBotStatus = ViewBindings.findChildViewById(rootView, id);
      if (etBotStatus == null) {
        break missingId;
      }

      id = R.id.etHashId;
      AppCompatEditText etHashId = ViewBindings.findChildViewById(rootView, id);
      if (etHashId == null) {
        break missingId;
      }

      id = R.id.ivDisputeLogo;
      AppCompatImageView ivDisputeLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivDisputeLogo == null) {
        break missingId;
      }

      id = R.id.llMainPending;
      LinearLayoutCompat llMainPending = ViewBindings.findChildViewById(rootView, id);
      if (llMainPending == null) {
        break missingId;
      }

      id = R.id.tvDisputeHeading;
      AppCompatTextView tvDisputeHeading = ViewBindings.findChildViewById(rootView, id);
      if (tvDisputeHeading == null) {
        break missingId;
      }

      id = R.id.tvMessageId;
      AppCompatTextView tvMessageId = ViewBindings.findChildViewById(rootView, id);
      if (tvMessageId == null) {
        break missingId;
      }

      id = R.id.tvStatusTitle;
      AppCompatTextView tvStatusTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvStatusTitle == null) {
        break missingId;
      }

      id = R.id.tvTitleReason;
      AppCompatTextView tvTitleReason = ViewBindings.findChildViewById(rootView, id);
      if (tvTitleReason == null) {
        break missingId;
      }

      id = R.id.tvTitleSenderEmail;
      AppCompatTextView tvTitleSenderEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvTitleSenderEmail == null) {
        break missingId;
      }

      id = R.id.tvTitleStatus;
      AppCompatTextView tvTitleStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvTitleStatus == null) {
        break missingId;
      }

      return new LayoutDisputeRaiseBinding((ConstraintLayout) rootView, btSubmit, cvCounter,
          cvMessageId, cvReason, cvSenderEmail, cvStatus, etBotCounter, etBotMail, etBotReason,
          etBotStatus, etHashId, ivDisputeLogo, llMainPending, tvDisputeHeading, tvMessageId,
          tvStatusTitle, tvTitleReason, tvTitleSenderEmail, tvTitleStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
