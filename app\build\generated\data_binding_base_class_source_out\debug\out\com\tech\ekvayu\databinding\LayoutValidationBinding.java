// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airbnb.lottie.LottieAnimationView;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutValidationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AppCompatButton btClose;

  @NonNull
  public final LottieAnimationView lvWarning;

  @NonNull
  public final AppCompatTextView tvMessage;

  private LayoutValidationBinding(@NonNull ConstraintLayout rootView,
      @NonNull AppCompatButton btClose, @NonNull LottieAnimationView lvWarning,
      @NonNull AppCompatTextView tvMessage) {
    this.rootView = rootView;
    this.btClose = btClose;
    this.lvWarning = lvWarning;
    this.tvMessage = tvMessage;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutValidationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutValidationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_validation, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutValidationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btClose;
      AppCompatButton btClose = ViewBindings.findChildViewById(rootView, id);
      if (btClose == null) {
        break missingId;
      }

      id = R.id.lvWarning;
      LottieAnimationView lvWarning = ViewBindings.findChildViewById(rootView, id);
      if (lvWarning == null) {
        break missingId;
      }

      id = R.id.tvMessage;
      AppCompatTextView tvMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvMessage == null) {
        break missingId;
      }

      return new LayoutValidationBinding((ConstraintLayout) rootView, btClose, lvWarning,
          tvMessage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
