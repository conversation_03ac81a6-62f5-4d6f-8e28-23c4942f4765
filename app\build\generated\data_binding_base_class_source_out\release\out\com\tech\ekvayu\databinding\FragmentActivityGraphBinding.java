// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.github.mikephil.charting.charts.PieChart;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentActivityGraphBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final ScrollView main;

  @NonNull
  public final PieChart pieChart;

  @NonNull
  public final LinearLayout summaryCards;

  @NonNull
  public final TextView tvDispute;

  @NonNull
  public final TextView tvHeader;

  @NonNull
  public final TextView tvProcessed;

  @NonNull
  public final TextView tvSpam;

  @NonNull
  public final TextView tvSubTitle;

  private FragmentActivityGraphBinding(@NonNull ScrollView rootView, @NonNull ScrollView main,
      @NonNull PieChart pieChart, @NonNull LinearLayout summaryCards, @NonNull TextView tvDispute,
      @NonNull TextView tvHeader, @NonNull TextView tvProcessed, @NonNull TextView tvSpam,
      @NonNull TextView tvSubTitle) {
    this.rootView = rootView;
    this.main = main;
    this.pieChart = pieChart;
    this.summaryCards = summaryCards;
    this.tvDispute = tvDispute;
    this.tvHeader = tvHeader;
    this.tvProcessed = tvProcessed;
    this.tvSpam = tvSpam;
    this.tvSubTitle = tvSubTitle;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentActivityGraphBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentActivityGraphBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_activity_graph, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentActivityGraphBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      ScrollView main = (ScrollView) rootView;

      id = R.id.pieChart;
      PieChart pieChart = ViewBindings.findChildViewById(rootView, id);
      if (pieChart == null) {
        break missingId;
      }

      id = R.id.summaryCards;
      LinearLayout summaryCards = ViewBindings.findChildViewById(rootView, id);
      if (summaryCards == null) {
        break missingId;
      }

      id = R.id.tvDispute;
      TextView tvDispute = ViewBindings.findChildViewById(rootView, id);
      if (tvDispute == null) {
        break missingId;
      }

      id = R.id.tvHeader;
      TextView tvHeader = ViewBindings.findChildViewById(rootView, id);
      if (tvHeader == null) {
        break missingId;
      }

      id = R.id.tvProcessed;
      TextView tvProcessed = ViewBindings.findChildViewById(rootView, id);
      if (tvProcessed == null) {
        break missingId;
      }

      id = R.id.tvSpam;
      TextView tvSpam = ViewBindings.findChildViewById(rootView, id);
      if (tvSpam == null) {
        break missingId;
      }

      id = R.id.tvSubTitle;
      TextView tvSubTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvSubTitle == null) {
        break missingId;
      }

      return new FragmentActivityGraphBinding((ScrollView) rootView, main, pieChart, summaryCards,
          tvDispute, tvHeader, tvProcessed, tvSpam, tvSubTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
