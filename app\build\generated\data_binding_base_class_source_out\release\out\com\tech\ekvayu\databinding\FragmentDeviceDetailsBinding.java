// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TableLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDeviceDetailsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final TableLayout tlAndroidInfo;

  @NonNull
  public final TableLayout tlAppInfo;

  @NonNull
  public final TableLayout tlDeviceInfo;

  @NonNull
  public final TableLayout tlScreenInfo;

  @NonNull
  public final TextView tvAndroidId;

  @NonNull
  public final TextView tvAndroidVersion;

  @NonNull
  public final TextView tvApiLevel;

  @NonNull
  public final AppCompatTextView tvAppInfo;

  @NonNull
  public final TextView tvBradName;

  @NonNull
  public final TextView tvBuildNumber;

  @NonNull
  public final TextView tvDensity;

  @NonNull
  public final TextView tvDensityFactor;

  @NonNull
  public final TextView tvDeviceName;

  @NonNull
  public final TextView tvManufacturer;

  @NonNull
  public final TextView tvMinSdk;

  @NonNull
  public final TextView tvModelName;

  @NonNull
  public final TextView tvPackage;

  @NonNull
  public final TextView tvProductName;

  @NonNull
  public final TextView tvResolution;

  @NonNull
  public final TextView tvScaleDensity;

  @NonNull
  public final AppCompatTextView tvScreenInfo;

  @NonNull
  public final TextView tvSerialNumber;

  @NonNull
  public final TextView tvTargetSdk;

  @NonNull
  public final AppCompatTextView tvTitle;

  @NonNull
  public final AppCompatTextView tvTitleAndroid;

  @NonNull
  public final TextView tvVersion;

  private FragmentDeviceDetailsBinding(@NonNull ScrollView rootView,
      @NonNull TableLayout tlAndroidInfo, @NonNull TableLayout tlAppInfo,
      @NonNull TableLayout tlDeviceInfo, @NonNull TableLayout tlScreenInfo,
      @NonNull TextView tvAndroidId, @NonNull TextView tvAndroidVersion,
      @NonNull TextView tvApiLevel, @NonNull AppCompatTextView tvAppInfo,
      @NonNull TextView tvBradName, @NonNull TextView tvBuildNumber, @NonNull TextView tvDensity,
      @NonNull TextView tvDensityFactor, @NonNull TextView tvDeviceName,
      @NonNull TextView tvManufacturer, @NonNull TextView tvMinSdk, @NonNull TextView tvModelName,
      @NonNull TextView tvPackage, @NonNull TextView tvProductName, @NonNull TextView tvResolution,
      @NonNull TextView tvScaleDensity, @NonNull AppCompatTextView tvScreenInfo,
      @NonNull TextView tvSerialNumber, @NonNull TextView tvTargetSdk,
      @NonNull AppCompatTextView tvTitle, @NonNull AppCompatTextView tvTitleAndroid,
      @NonNull TextView tvVersion) {
    this.rootView = rootView;
    this.tlAndroidInfo = tlAndroidInfo;
    this.tlAppInfo = tlAppInfo;
    this.tlDeviceInfo = tlDeviceInfo;
    this.tlScreenInfo = tlScreenInfo;
    this.tvAndroidId = tvAndroidId;
    this.tvAndroidVersion = tvAndroidVersion;
    this.tvApiLevel = tvApiLevel;
    this.tvAppInfo = tvAppInfo;
    this.tvBradName = tvBradName;
    this.tvBuildNumber = tvBuildNumber;
    this.tvDensity = tvDensity;
    this.tvDensityFactor = tvDensityFactor;
    this.tvDeviceName = tvDeviceName;
    this.tvManufacturer = tvManufacturer;
    this.tvMinSdk = tvMinSdk;
    this.tvModelName = tvModelName;
    this.tvPackage = tvPackage;
    this.tvProductName = tvProductName;
    this.tvResolution = tvResolution;
    this.tvScaleDensity = tvScaleDensity;
    this.tvScreenInfo = tvScreenInfo;
    this.tvSerialNumber = tvSerialNumber;
    this.tvTargetSdk = tvTargetSdk;
    this.tvTitle = tvTitle;
    this.tvTitleAndroid = tvTitleAndroid;
    this.tvVersion = tvVersion;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDeviceDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDeviceDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_device_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDeviceDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tlAndroidInfo;
      TableLayout tlAndroidInfo = ViewBindings.findChildViewById(rootView, id);
      if (tlAndroidInfo == null) {
        break missingId;
      }

      id = R.id.tlAppInfo;
      TableLayout tlAppInfo = ViewBindings.findChildViewById(rootView, id);
      if (tlAppInfo == null) {
        break missingId;
      }

      id = R.id.tlDeviceInfo;
      TableLayout tlDeviceInfo = ViewBindings.findChildViewById(rootView, id);
      if (tlDeviceInfo == null) {
        break missingId;
      }

      id = R.id.tlScreenInfo;
      TableLayout tlScreenInfo = ViewBindings.findChildViewById(rootView, id);
      if (tlScreenInfo == null) {
        break missingId;
      }

      id = R.id.tvAndroidId;
      TextView tvAndroidId = ViewBindings.findChildViewById(rootView, id);
      if (tvAndroidId == null) {
        break missingId;
      }

      id = R.id.tvAndroidVersion;
      TextView tvAndroidVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvAndroidVersion == null) {
        break missingId;
      }

      id = R.id.tvApiLevel;
      TextView tvApiLevel = ViewBindings.findChildViewById(rootView, id);
      if (tvApiLevel == null) {
        break missingId;
      }

      id = R.id.tvAppInfo;
      AppCompatTextView tvAppInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvAppInfo == null) {
        break missingId;
      }

      id = R.id.tvBradName;
      TextView tvBradName = ViewBindings.findChildViewById(rootView, id);
      if (tvBradName == null) {
        break missingId;
      }

      id = R.id.tvBuildNumber;
      TextView tvBuildNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvBuildNumber == null) {
        break missingId;
      }

      id = R.id.tvDensity;
      TextView tvDensity = ViewBindings.findChildViewById(rootView, id);
      if (tvDensity == null) {
        break missingId;
      }

      id = R.id.tvDensityFactor;
      TextView tvDensityFactor = ViewBindings.findChildViewById(rootView, id);
      if (tvDensityFactor == null) {
        break missingId;
      }

      id = R.id.tvDeviceName;
      TextView tvDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceName == null) {
        break missingId;
      }

      id = R.id.tvManufacturer;
      TextView tvManufacturer = ViewBindings.findChildViewById(rootView, id);
      if (tvManufacturer == null) {
        break missingId;
      }

      id = R.id.tvMinSdk;
      TextView tvMinSdk = ViewBindings.findChildViewById(rootView, id);
      if (tvMinSdk == null) {
        break missingId;
      }

      id = R.id.tvModelName;
      TextView tvModelName = ViewBindings.findChildViewById(rootView, id);
      if (tvModelName == null) {
        break missingId;
      }

      id = R.id.tvPackage;
      TextView tvPackage = ViewBindings.findChildViewById(rootView, id);
      if (tvPackage == null) {
        break missingId;
      }

      id = R.id.tvProductName;
      TextView tvProductName = ViewBindings.findChildViewById(rootView, id);
      if (tvProductName == null) {
        break missingId;
      }

      id = R.id.tvResolution;
      TextView tvResolution = ViewBindings.findChildViewById(rootView, id);
      if (tvResolution == null) {
        break missingId;
      }

      id = R.id.tvScaleDensity;
      TextView tvScaleDensity = ViewBindings.findChildViewById(rootView, id);
      if (tvScaleDensity == null) {
        break missingId;
      }

      id = R.id.tvScreenInfo;
      AppCompatTextView tvScreenInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvScreenInfo == null) {
        break missingId;
      }

      id = R.id.tvSerialNumber;
      TextView tvSerialNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvSerialNumber == null) {
        break missingId;
      }

      id = R.id.tvTargetSdk;
      TextView tvTargetSdk = ViewBindings.findChildViewById(rootView, id);
      if (tvTargetSdk == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      AppCompatTextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tvTitleAndroid;
      AppCompatTextView tvTitleAndroid = ViewBindings.findChildViewById(rootView, id);
      if (tvTitleAndroid == null) {
        break missingId;
      }

      id = R.id.tvVersion;
      TextView tvVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvVersion == null) {
        break missingId;
      }

      return new FragmentDeviceDetailsBinding((ScrollView) rootView, tlAndroidInfo, tlAppInfo,
          tlDeviceInfo, tlScreenInfo, tvAndroidId, tvAndroidVersion, tvApiLevel, tvAppInfo,
          tvBradName, tvBuildNumber, tvDensity, tvDensityFactor, tvDeviceName, tvManufacturer,
          tvMinSdk, tvModelName, tvPackage, tvProductName, tvResolution, tvScaleDensity,
          tvScreenInfo, tvSerialNumber, tvTargetSdk, tvTitle, tvTitleAndroid, tvVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
