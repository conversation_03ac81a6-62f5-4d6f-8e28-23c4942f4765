// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.github.mikephil.charting.charts.PieChart;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHomeBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AppCompatButton btPermission;

  @NonNull
  public final CardView crTop;

  @NonNull
  public final LinearLayoutCompat llMain;

  @NonNull
  public final PieChart pieChart;

  @NonNull
  public final RecyclerView rvMenu;

  @NonNull
  public final AppCompatTextView tvDispute;

  @NonNull
  public final AppCompatTextView tvMails;

  @NonNull
  public final AppCompatTextView tvProcess;

  @NonNull
  public final AppCompatTextView tvSpam;

  private FragmentHomeBinding(@NonNull ConstraintLayout rootView,
      @NonNull AppCompatButton btPermission, @NonNull CardView crTop,
      @NonNull LinearLayoutCompat llMain, @NonNull PieChart pieChart, @NonNull RecyclerView rvMenu,
      @NonNull AppCompatTextView tvDispute, @NonNull AppCompatTextView tvMails,
      @NonNull AppCompatTextView tvProcess, @NonNull AppCompatTextView tvSpam) {
    this.rootView = rootView;
    this.btPermission = btPermission;
    this.crTop = crTop;
    this.llMain = llMain;
    this.pieChart = pieChart;
    this.rvMenu = rvMenu;
    this.tvDispute = tvDispute;
    this.tvMails = tvMails;
    this.tvProcess = tvProcess;
    this.tvSpam = tvSpam;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_home, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHomeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btPermission;
      AppCompatButton btPermission = ViewBindings.findChildViewById(rootView, id);
      if (btPermission == null) {
        break missingId;
      }

      id = R.id.crTop;
      CardView crTop = ViewBindings.findChildViewById(rootView, id);
      if (crTop == null) {
        break missingId;
      }

      id = R.id.llMain;
      LinearLayoutCompat llMain = ViewBindings.findChildViewById(rootView, id);
      if (llMain == null) {
        break missingId;
      }

      id = R.id.pieChart;
      PieChart pieChart = ViewBindings.findChildViewById(rootView, id);
      if (pieChart == null) {
        break missingId;
      }

      id = R.id.rvMenu;
      RecyclerView rvMenu = ViewBindings.findChildViewById(rootView, id);
      if (rvMenu == null) {
        break missingId;
      }

      id = R.id.tvDispute;
      AppCompatTextView tvDispute = ViewBindings.findChildViewById(rootView, id);
      if (tvDispute == null) {
        break missingId;
      }

      id = R.id.tvMails;
      AppCompatTextView tvMails = ViewBindings.findChildViewById(rootView, id);
      if (tvMails == null) {
        break missingId;
      }

      id = R.id.tvProcess;
      AppCompatTextView tvProcess = ViewBindings.findChildViewById(rootView, id);
      if (tvProcess == null) {
        break missingId;
      }

      id = R.id.tvSpam;
      AppCompatTextView tvSpam = ViewBindings.findChildViewById(rootView, id);
      if (tvSpam == null) {
        break missingId;
      }

      return new FragmentHomeBinding((ConstraintLayout) rootView, btPermission, crTop, llMain,
          pieChart, rvMenu, tvDispute, tvMails, tvProcess, tvSpam);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
