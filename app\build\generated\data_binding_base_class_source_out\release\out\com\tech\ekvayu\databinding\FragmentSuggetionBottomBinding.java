// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSuggetionBottomBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AppCompatButton btGotoGmail;

  @NonNull
  public final AppCompatTextView tvSuggetion;

  private FragmentSuggetionBottomBinding(@NonNull ConstraintLayout rootView,
      @NonNull AppCompatButton btGotoGmail, @NonNull AppCompatTextView tvSuggetion) {
    this.rootView = rootView;
    this.btGotoGmail = btGotoGmail;
    this.tvSuggetion = tvSuggetion;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSuggetionBottomBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSuggetionBottomBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_suggetion_bottom, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSuggetionBottomBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btGotoGmail;
      AppCompatButton btGotoGmail = ViewBindings.findChildViewById(rootView, id);
      if (btGotoGmail == null) {
        break missingId;
      }

      id = R.id.tvSuggetion;
      AppCompatTextView tvSuggetion = ViewBindings.findChildViewById(rootView, id);
      if (tvSuggetion == null) {
        break missingId;
      }

      return new FragmentSuggetionBottomBinding((ConstraintLayout) rootView, btGotoGmail,
          tvSuggetion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
