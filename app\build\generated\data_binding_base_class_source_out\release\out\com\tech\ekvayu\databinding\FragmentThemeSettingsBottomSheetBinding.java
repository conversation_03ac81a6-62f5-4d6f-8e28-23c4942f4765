// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentThemeSettingsBottomSheetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnApply;

  @NonNull
  public final Button btnClose;

  @NonNull
  public final RadioButton rbDark;

  @NonNull
  public final RadioButton rbLight;

  @NonNull
  public final RadioButton rbSystem;

  @NonNull
  public final RadioGroup rgThemeOptions;

  @NonNull
  public final TextView tvCurrentTheme;

  @NonNull
  public final TextView tvThemePreview;

  private FragmentThemeSettingsBottomSheetBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnApply, @NonNull Button btnClose, @NonNull RadioButton rbDark,
      @NonNull RadioButton rbLight, @NonNull RadioButton rbSystem,
      @NonNull RadioGroup rgThemeOptions, @NonNull TextView tvCurrentTheme,
      @NonNull TextView tvThemePreview) {
    this.rootView = rootView;
    this.btnApply = btnApply;
    this.btnClose = btnClose;
    this.rbDark = rbDark;
    this.rbLight = rbLight;
    this.rbSystem = rbSystem;
    this.rgThemeOptions = rgThemeOptions;
    this.tvCurrentTheme = tvCurrentTheme;
    this.tvThemePreview = tvThemePreview;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentThemeSettingsBottomSheetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentThemeSettingsBottomSheetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_theme_settings_bottom_sheet, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentThemeSettingsBottomSheetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnApply;
      Button btnApply = ViewBindings.findChildViewById(rootView, id);
      if (btnApply == null) {
        break missingId;
      }

      id = R.id.btnClose;
      Button btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.rbDark;
      RadioButton rbDark = ViewBindings.findChildViewById(rootView, id);
      if (rbDark == null) {
        break missingId;
      }

      id = R.id.rbLight;
      RadioButton rbLight = ViewBindings.findChildViewById(rootView, id);
      if (rbLight == null) {
        break missingId;
      }

      id = R.id.rbSystem;
      RadioButton rbSystem = ViewBindings.findChildViewById(rootView, id);
      if (rbSystem == null) {
        break missingId;
      }

      id = R.id.rgThemeOptions;
      RadioGroup rgThemeOptions = ViewBindings.findChildViewById(rootView, id);
      if (rgThemeOptions == null) {
        break missingId;
      }

      id = R.id.tvCurrentTheme;
      TextView tvCurrentTheme = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentTheme == null) {
        break missingId;
      }

      id = R.id.tvThemePreview;
      TextView tvThemePreview = ViewBindings.findChildViewById(rootView, id);
      if (tvThemePreview == null) {
        break missingId;
      }

      return new FragmentThemeSettingsBottomSheetBinding((LinearLayout) rootView, btnApply,
          btnClose, rbDark, rbLight, rbSystem, rgThemeOptions, tvCurrentTheme, tvThemePreview);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
