// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class HeaderLayoutBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final CardView cvBack;

  @NonNull
  public final CardView cvTheme;

  @NonNull
  public final AppCompatImageView ivBack;

  @NonNull
  public final AppCompatImageView ivTheme;

  private HeaderLayoutBinding(@NonNull ConstraintLayout rootView, @NonNull CardView cvBack,
      @NonNull CardView cvTheme, @NonNull AppCompatImageView ivBack,
      @NonNull AppCompatImageView ivTheme) {
    this.rootView = rootView;
    this.cvBack = cvBack;
    this.cvTheme = cvTheme;
    this.ivBack = ivBack;
    this.ivTheme = ivTheme;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static HeaderLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static HeaderLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.header_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static HeaderLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cvBack;
      CardView cvBack = ViewBindings.findChildViewById(rootView, id);
      if (cvBack == null) {
        break missingId;
      }

      id = R.id.cvTheme;
      CardView cvTheme = ViewBindings.findChildViewById(rootView, id);
      if (cvTheme == null) {
        break missingId;
      }

      id = R.id.ivBack;
      AppCompatImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.ivTheme;
      AppCompatImageView ivTheme = ViewBindings.findChildViewById(rootView, id);
      if (ivTheme == null) {
        break missingId;
      }

      return new HeaderLayoutBinding((ConstraintLayout) rootView, cvBack, cvTheme, ivBack, ivTheme);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
