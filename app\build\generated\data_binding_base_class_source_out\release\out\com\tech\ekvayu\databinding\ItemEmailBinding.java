// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemEmailBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView ivAttachment;

  @NonNull
  public final ImageView ivStar;

  @NonNull
  public final TextView tvBodyPreview;

  @NonNull
  public final TextView tvDate;

  @NonNull
  public final TextView tvProvider;

  @NonNull
  public final TextView tvRecipientCount;

  @NonNull
  public final TextView tvSender;

  @NonNull
  public final TextView tvSenderEmail;

  @NonNull
  public final TextView tvSubject;

  private ItemEmailBinding(@NonNull CardView rootView, @NonNull ImageView ivAttachment,
      @NonNull ImageView ivStar, @NonNull TextView tvBodyPreview, @NonNull TextView tvDate,
      @NonNull TextView tvProvider, @NonNull TextView tvRecipientCount, @NonNull TextView tvSender,
      @NonNull TextView tvSenderEmail, @NonNull TextView tvSubject) {
    this.rootView = rootView;
    this.ivAttachment = ivAttachment;
    this.ivStar = ivStar;
    this.tvBodyPreview = tvBodyPreview;
    this.tvDate = tvDate;
    this.tvProvider = tvProvider;
    this.tvRecipientCount = tvRecipientCount;
    this.tvSender = tvSender;
    this.tvSenderEmail = tvSenderEmail;
    this.tvSubject = tvSubject;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemEmailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemEmailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_email, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemEmailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_attachment;
      ImageView ivAttachment = ViewBindings.findChildViewById(rootView, id);
      if (ivAttachment == null) {
        break missingId;
      }

      id = R.id.iv_star;
      ImageView ivStar = ViewBindings.findChildViewById(rootView, id);
      if (ivStar == null) {
        break missingId;
      }

      id = R.id.tv_body_preview;
      TextView tvBodyPreview = ViewBindings.findChildViewById(rootView, id);
      if (tvBodyPreview == null) {
        break missingId;
      }

      id = R.id.tv_date;
      TextView tvDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDate == null) {
        break missingId;
      }

      id = R.id.tv_provider;
      TextView tvProvider = ViewBindings.findChildViewById(rootView, id);
      if (tvProvider == null) {
        break missingId;
      }

      id = R.id.tv_recipient_count;
      TextView tvRecipientCount = ViewBindings.findChildViewById(rootView, id);
      if (tvRecipientCount == null) {
        break missingId;
      }

      id = R.id.tv_sender;
      TextView tvSender = ViewBindings.findChildViewById(rootView, id);
      if (tvSender == null) {
        break missingId;
      }

      id = R.id.tv_sender_email;
      TextView tvSenderEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvSenderEmail == null) {
        break missingId;
      }

      id = R.id.tv_subject;
      TextView tvSubject = ViewBindings.findChildViewById(rootView, id);
      if (tvSubject == null) {
        break missingId;
      }

      return new ItemEmailBinding((CardView) rootView, ivAttachment, ivStar, tvBodyPreview, tvDate,
          tvProvider, tvRecipientCount, tvSender, tvSenderEmail, tvSubject);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
