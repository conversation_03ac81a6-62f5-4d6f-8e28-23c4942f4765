// Generated by view binder compiler. Do not edit!
package com.tech.ekvayu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tech.ekvayu.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutSpamDetailBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final ConstraintLayout clContainer;

  @NonNull
  public final CardView cvMailItem;

  @NonNull
  public final TextView tvAttachment;

  @NonNull
  public final TextView tvBody;

  @NonNull
  public final TextView tvReceiver;

  @NonNull
  public final TextView tvSender;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvSubject;

  private LayoutSpamDetailBinding(@NonNull ScrollView rootView,
      @NonNull ConstraintLayout clContainer, @NonNull CardView cvMailItem,
      @NonNull TextView tvAttachment, @NonNull TextView tvBody, @NonNull TextView tvReceiver,
      @NonNull TextView tvSender, @NonNull TextView tvStatus, @NonNull TextView tvSubject) {
    this.rootView = rootView;
    this.clContainer = clContainer;
    this.cvMailItem = cvMailItem;
    this.tvAttachment = tvAttachment;
    this.tvBody = tvBody;
    this.tvReceiver = tvReceiver;
    this.tvSender = tvSender;
    this.tvStatus = tvStatus;
    this.tvSubject = tvSubject;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutSpamDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutSpamDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_spam_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutSpamDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.clContainer;
      ConstraintLayout clContainer = ViewBindings.findChildViewById(rootView, id);
      if (clContainer == null) {
        break missingId;
      }

      id = R.id.cvMailItem;
      CardView cvMailItem = ViewBindings.findChildViewById(rootView, id);
      if (cvMailItem == null) {
        break missingId;
      }

      id = R.id.tvAttachment;
      TextView tvAttachment = ViewBindings.findChildViewById(rootView, id);
      if (tvAttachment == null) {
        break missingId;
      }

      id = R.id.tvBody;
      TextView tvBody = ViewBindings.findChildViewById(rootView, id);
      if (tvBody == null) {
        break missingId;
      }

      id = R.id.tvReceiver;
      TextView tvReceiver = ViewBindings.findChildViewById(rootView, id);
      if (tvReceiver == null) {
        break missingId;
      }

      id = R.id.tvSender;
      TextView tvSender = ViewBindings.findChildViewById(rootView, id);
      if (tvSender == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tvSubject;
      TextView tvSubject = ViewBindings.findChildViewById(rootView, id);
      if (tvSubject == null) {
        break missingId;
      }

      return new LayoutSpamDetailBinding((ScrollView) rootView, clContainer, cvMailItem,
          tvAttachment, tvBody, tvReceiver, tvSender, tvStatus, tvSubject);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
