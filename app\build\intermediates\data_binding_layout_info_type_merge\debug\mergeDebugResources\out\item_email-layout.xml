<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_email" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\item_email.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_email_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="160" endOffset="35"/></Target><Target id="@+id/tv_provider" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="28" endOffset="55"/></Target><Target id="@+id/tv_sender" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="44" endOffset="35"/></Target><Target id="@+id/tv_sender_email" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="60" endOffset="47"/></Target><Target id="@+id/tv_date" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="72" endOffset="45"/></Target><Target id="@+id/tv_subject" view="TextView"><Expressions/><location startLine="75" startOffset="8" endLine="89" endOffset="53"/></Target><Target id="@+id/iv_star" view="ImageView"><Expressions/><location startLine="92" startOffset="8" endLine="101" endOffset="40"/></Target><Target id="@+id/tv_body_preview" view="TextView"><Expressions/><location startLine="104" startOffset="8" endLine="117" endOffset="125"/></Target><Target id="@+id/iv_attachment" view="ImageView"><Expressions/><location startLine="132" startOffset="12" endLine="139" endOffset="44"/></Target><Target id="@+id/tv_recipient_count" view="TextView"><Expressions/><location startLine="142" startOffset="12" endLine="154" endOffset="33"/></Target></Targets></Layout>