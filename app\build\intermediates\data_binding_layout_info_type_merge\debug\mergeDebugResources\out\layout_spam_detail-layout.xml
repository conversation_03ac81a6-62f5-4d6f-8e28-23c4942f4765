<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_spam_detail" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\layout_spam_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/layout_spam_detail_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="114" endOffset="12"/></Target><Target id="@+id/cvMailItem" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="10" startOffset="8" endLine="107" endOffset="43"/></Target><Target id="@+id/clContainer" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="23" startOffset="12" endLine="105" endOffset="63"/></Target><Target id="@+id/tvSender" view="TextView"><Expressions/><location startLine="30" startOffset="16" endLine="40" endOffset="62"/></Target><Target id="@+id/tvReceiver" view="TextView"><Expressions/><location startLine="43" startOffset="16" endLine="52" endOffset="62"/></Target><Target id="@+id/tvSubject" view="TextView"><Expressions/><location startLine="55" startOffset="16" endLine="66" endOffset="51"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="69" startOffset="16" endLine="78" endOffset="51"/></Target><Target id="@+id/tvAttachment" view="TextView"><Expressions/><location startLine="81" startOffset="16" endLine="89" endOffset="51"/></Target><Target id="@+id/tvBody" view="TextView"><Expressions/><location startLine="92" startOffset="16" endLine="102" endOffset="51"/></Target></Targets></Layout>