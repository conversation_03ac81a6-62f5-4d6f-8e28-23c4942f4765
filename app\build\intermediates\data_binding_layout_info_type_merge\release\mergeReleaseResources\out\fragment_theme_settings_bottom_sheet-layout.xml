<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_theme_settings_bottom_sheet" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_theme_settings_bottom_sheet_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="129" endOffset="14"/></Target><Target id="@+id/tvCurrentTheme" view="TextView"><Expressions/><location startLine="21" startOffset="4" endLine="29" endOffset="44"/></Target><Target id="@+id/tvThemePreview" view="TextView"><Expressions/><location startLine="32" startOffset="4" endLine="42" endOffset="44"/></Target><Target id="@+id/rgThemeOptions" view="RadioGroup"><Expressions/><location startLine="54" startOffset="4" endLine="89" endOffset="16"/></Target><Target id="@+id/rbLight" view="RadioButton"><Expressions/><location startLine="60" startOffset="8" endLine="68" endOffset="47"/></Target><Target id="@+id/rbDark" view="RadioButton"><Expressions/><location startLine="70" startOffset="8" endLine="78" endOffset="47"/></Target><Target id="@+id/rbSystem" view="RadioButton"><Expressions/><location startLine="80" startOffset="8" endLine="87" endOffset="36"/></Target><Target id="@+id/btnClose" view="Button"><Expressions/><location startLine="107" startOffset="8" endLine="115" endOffset="44"/></Target><Target id="@+id/btnApply" view="Button"><Expressions/><location startLine="117" startOffset="8" endLine="125" endOffset="46"/></Target></Targets></Layout>