<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_email_auth" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\activity_email_auth.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_email_auth_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="181" endOffset="12"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="15" startOffset="8" endLine="26" endOffset="55"/></Target><Target id="@+id/tv_description" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="39" endOffset="55"/></Target><Target id="@+id/tv_auth_status" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="55" endOffset="55"/></Target><Target id="@+id/btn_gmail_auth" view="Button"><Expressions/><location startLine="58" startOffset="8" endLine="70" endOffset="55"/></Target><Target id="@+id/btn_outlook_auth" view="Button"><Expressions/><location startLine="73" startOffset="8" endLine="85" endOffset="55"/></Target><Target id="@+id/btn_yahoo_auth" view="Button"><Expressions/><location startLine="88" startOffset="8" endLine="100" endOffset="55"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="103" startOffset="8" endLine="110" endOffset="55"/></Target><Target id="@+id/tv_progress" view="TextView"><Expressions/><location startLine="112" startOffset="8" endLine="124" endOffset="55"/></Target><Target id="@+id/btn_check_status" view="Button"><Expressions/><location startLine="127" startOffset="8" endLine="138" endOffset="69"/></Target><Target id="@+id/btn_clear_auth" view="Button"><Expressions/><location startLine="140" startOffset="8" endLine="151" endOffset="55"/></Target><Target id="@+id/tv_features_title" view="TextView"><Expressions/><location startLine="154" startOffset="8" endLine="165" endOffset="55"/></Target><Target id="@+id/tv_features" view="TextView"><Expressions/><location startLine="167" startOffset="8" endLine="177" endOffset="61"/></Target></Targets></Layout>