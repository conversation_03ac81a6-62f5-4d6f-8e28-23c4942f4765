<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_email_demo" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\activity_email_demo.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_email_demo_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="206" endOffset="51"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="22" endOffset="51"/></Target><Target id="@+id/tv_auth_status" view="TextView"><Expressions/><location startLine="25" startOffset="4" endLine="37" endOffset="51"/></Target><Target id="@+id/layout_controls" view="LinearLayout"><Expressions/><location startLine="40" startOffset="4" endLine="82" endOffset="18"/></Target><Target id="@+id/btn_authenticate" view="Button"><Expressions/><location startLine="50" startOffset="8" endLine="59" endOffset="44"/></Target><Target id="@+id/btn_sync_all" view="Button"><Expressions/><location startLine="61" startOffset="8" endLine="70" endOffset="51"/></Target><Target id="@+id/btn_clear_emails" view="Button"><Expressions/><location startLine="72" startOffset="8" endLine="80" endOffset="46"/></Target><Target id="@+id/layout_provider_sync" view="LinearLayout"><Expressions/><location startLine="85" startOffset="4" endLine="129" endOffset="18"/></Target><Target id="@+id/btn_sync_gmail" view="Button"><Expressions/><location startLine="96" startOffset="8" endLine="105" endOffset="37"/></Target><Target id="@+id/btn_sync_outlook" view="Button"><Expressions/><location startLine="107" startOffset="8" endLine="116" endOffset="37"/></Target><Target id="@+id/btn_sync_yahoo" view="Button"><Expressions/><location startLine="118" startOffset="8" endLine="127" endOffset="37"/></Target><Target id="@+id/tv_email_count" view="TextView"><Expressions/><location startLine="132" startOffset="4" endLine="143" endOffset="51"/></Target><Target id="@+id/tv_email_details" view="TextView"><Expressions/><location startLine="146" startOffset="4" endLine="158" endOffset="51"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="161" startOffset="4" endLine="168" endOffset="51"/></Target><Target id="@+id/tv_progress" view="TextView"><Expressions/><location startLine="170" startOffset="4" endLine="182" endOffset="51"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="185" startOffset="4" endLine="204" endOffset="59"/></Target><Target id="@+id/recycler_view_emails" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="196" startOffset="8" endLine="202" endOffset="49"/></Target></Targets></Layout>