<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_device_details" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\fragment_device_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_device_details_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="489" endOffset="12"/></Target><Target id="@+id/tvTitle" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="15" startOffset="4" endLine="26" endOffset="9"/></Target><Target id="@+id/tlDeviceInfo" view="TableLayout"><Expressions/><location startLine="28" startOffset="4" endLine="137" endOffset="17"/></Target><Target id="@+id/tvDeviceName" view="TextView"><Expressions/><location startLine="49" startOffset="12" endLine="55" endOffset="17"/></Target><Target id="@+id/tvManufacturer" view="TextView"><Expressions/><location startLine="69" startOffset="12" endLine="75" endOffset="17"/></Target><Target id="@+id/tvModelName" view="TextView"><Expressions/><location startLine="89" startOffset="12" endLine="95" endOffset="17"/></Target><Target id="@+id/tvBradName" view="TextView"><Expressions/><location startLine="108" startOffset="12" endLine="114" endOffset="17"/></Target><Target id="@+id/tvProductName" view="TextView"><Expressions/><location startLine="127" startOffset="12" endLine="133" endOffset="17"/></Target><Target id="@+id/tvTitleAndroid" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="139" startOffset="4" endLine="150" endOffset="9"/></Target><Target id="@+id/tlAndroidInfo" view="TableLayout"><Expressions/><location startLine="152" startOffset="4" endLine="274" endOffset="17"/></Target><Target id="@+id/tvAndroidVersion" view="TextView"><Expressions/><location startLine="175" startOffset="12" endLine="182" endOffset="17"/></Target><Target id="@+id/tvApiLevel" view="TextView"><Expressions/><location startLine="197" startOffset="12" endLine="204" endOffset="17"/></Target><Target id="@+id/tvBuildNumber" view="TextView"><Expressions/><location startLine="219" startOffset="12" endLine="227" endOffset="17"/></Target><Target id="@+id/tvSerialNumber" view="TextView"><Expressions/><location startLine="241" startOffset="12" endLine="248" endOffset="17"/></Target><Target id="@+id/tvAndroidId" view="TextView"><Expressions/><location startLine="262" startOffset="12" endLine="269" endOffset="17"/></Target><Target id="@+id/tvScreenInfo" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="276" startOffset="4" endLine="287" endOffset="9"/></Target><Target id="@+id/tlScreenInfo" view="TableLayout"><Expressions/><location startLine="289" startOffset="4" endLine="378" endOffset="17"/></Target><Target id="@+id/tvResolution" view="TextView"><Expressions/><location startLine="310" startOffset="12" endLine="316" endOffset="17"/></Target><Target id="@+id/tvDensity" view="TextView"><Expressions/><location startLine="330" startOffset="12" endLine="336" endOffset="17"/></Target><Target id="@+id/tvDensityFactor" view="TextView"><Expressions/><location startLine="350" startOffset="12" endLine="356" endOffset="17"/></Target><Target id="@+id/tvScaleDensity" view="TextView"><Expressions/><location startLine="369" startOffset="12" endLine="375" endOffset="17"/></Target><Target id="@+id/tvAppInfo" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="380" startOffset="4" endLine="391" endOffset="9"/></Target><Target id="@+id/tlAppInfo" view="TableLayout"><Expressions/><location startLine="393" startOffset="4" endLine="483" endOffset="17"/></Target><Target id="@+id/tvPackage" view="TextView"><Expressions/><location startLine="414" startOffset="12" endLine="420" endOffset="17"/></Target><Target id="@+id/tvVersion" view="TextView"><Expressions/><location startLine="434" startOffset="12" endLine="440" endOffset="17"/></Target><Target id="@+id/tvTargetSdk" view="TextView"><Expressions/><location startLine="454" startOffset="12" endLine="460" endOffset="17"/></Target><Target id="@+id/tvMinSdk" view="TextView"><Expressions/><location startLine="473" startOffset="12" endLine="479" endOffset="17"/></Target></Targets></Layout>