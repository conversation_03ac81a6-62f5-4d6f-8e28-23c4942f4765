<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_email_config_bottom_sheet" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\fragment_email_config_bottom_sheet.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_email_config_bottom_sheet_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="112" endOffset="14"/></Target><Target id="@+id/tvCurrentEmail" view="TextView"><Expressions/><location startLine="30" startOffset="4" endLine="37" endOffset="44"/></Target><Target id="@+id/tilPrimaryEmail" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="40" startOffset="4" endLine="56" endOffset="59"/></Target><Target id="@+id/etPrimaryEmail" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="49" startOffset="8" endLine="54" endOffset="34"/></Target><Target id="@+id/btnCancel" view="Button"><Expressions/><location startLine="65" startOffset="8" endLine="73" endOffset="44"/></Target><Target id="@+id/btnSaveEmail" view="Button"><Expressions/><location startLine="75" startOffset="8" endLine="84" endOffset="46"/></Target><Target id="@+id/btnShowStats" view="Button"><Expressions/><location startLine="89" startOffset="4" endLine="97" endOffset="43"/></Target><Target id="@+id/tvStats" view="TextView"><Expressions/><location startLine="100" startOffset="4" endLine="110" endOffset="40"/></Target></Targets></Layout>