<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="123" endOffset="51"/></Target><Target id="@+id/crTop" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="9" startOffset="4" endLine="70" endOffset="39"/></Target><Target id="@+id/llMain" view="androidx.appcompat.widget.LinearLayoutCompat"><Expressions/><location startLine="22" startOffset="8" endLine="58" endOffset="54"/></Target><Target id="@+id/tvDispute" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="30" startOffset="12" endLine="37" endOffset="50"/></Target><Target id="@+id/tvSpam" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="39" startOffset="12" endLine="46" endOffset="50"/></Target><Target id="@+id/tvProcess" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="48" startOffset="12" endLine="55" endOffset="50"/></Target><Target id="@+id/pieChart" view="com.github.mikephil.charting.charts.PieChart"><Expressions/><location startLine="61" startOffset="8" endLine="68" endOffset="13"/></Target><Target id="@+id/tvMails" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="72" startOffset="4" endLine="84" endOffset="9"/></Target><Target id="@+id/rvMenu" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="86" startOffset="4" endLine="96" endOffset="9"/></Target><Target id="@+id/btPermission" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="99" startOffset="4" endLine="119" endOffset="9"/></Target></Targets></Layout>