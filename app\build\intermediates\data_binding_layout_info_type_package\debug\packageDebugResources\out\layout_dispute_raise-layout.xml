<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_dispute_raise" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\layout_dispute_raise.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/layout_dispute_raise_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2" startOffset="4" endLine="318" endOffset="51"/></Target><Target id="@+id/ivDisputeLogo" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="12" startOffset="8" endLine="22" endOffset="13"/></Target><Target id="@+id/tvDisputeHeading" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="25" startOffset="8" endLine="35" endOffset="13"/></Target><Target id="@+id/cvMessageId" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="37" startOffset="8" endLine="84" endOffset="43"/></Target><Target id="@+id/tvMessageId" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="57" startOffset="16" endLine="65" endOffset="21"/></Target><Target id="@+id/etHashId" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="68" startOffset="16" endLine="78" endOffset="21"/></Target><Target id="@+id/cvSenderEmail" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="87" startOffset="8" endLine="134" endOffset="43"/></Target><Target id="@+id/tvTitleSenderEmail" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="107" startOffset="16" endLine="115" endOffset="21"/></Target><Target id="@+id/etBotMail" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="118" startOffset="16" endLine="128" endOffset="21"/></Target><Target id="@+id/llMainPending" view="androidx.appcompat.widget.LinearLayoutCompat"><Expressions/><location startLine="137" startOffset="4" endLine="240" endOffset="50"/></Target><Target id="@+id/cvStatus" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="145" startOffset="8" endLine="190" endOffset="43"/></Target><Target id="@+id/tvStatusTitle" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="164" startOffset="16" endLine="172" endOffset="21"/></Target><Target id="@+id/etBotStatus" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="175" startOffset="16" endLine="185" endOffset="21"/></Target><Target id="@+id/cvCounter" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="192" startOffset="8" endLine="237" endOffset="43"/></Target><Target id="@+id/tvTitleStatus" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="211" startOffset="16" endLine="219" endOffset="21"/></Target><Target id="@+id/etBotCounter" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="222" startOffset="16" endLine="232" endOffset="21"/></Target><Target id="@+id/cvReason" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="243" startOffset="4" endLine="289" endOffset="39"/></Target><Target id="@+id/tvTitleReason" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="263" startOffset="12" endLine="271" endOffset="17"/></Target><Target id="@+id/etBotReason" view="androidx.appcompat.widget.AppCompatEditText"><Expressions/><location startLine="274" startOffset="12" endLine="284" endOffset="17"/></Target><Target id="@+id/btSubmit" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="292" startOffset="4" endLine="312" endOffset="9"/></Target></Targets></Layout>