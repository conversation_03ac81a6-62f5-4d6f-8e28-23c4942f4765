<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="124" endOffset="51"/></Target><Target id="@+id/crTop" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="10" startOffset="4" endLine="71" endOffset="39"/></Target><Target id="@+id/llMain" view="androidx.appcompat.widget.LinearLayoutCompat"><Expressions/><location startLine="23" startOffset="8" endLine="59" endOffset="54"/></Target><Target id="@+id/tvDispute" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="31" startOffset="12" endLine="38" endOffset="50"/></Target><Target id="@+id/tvSpam" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="40" startOffset="12" endLine="47" endOffset="50"/></Target><Target id="@+id/tvProcess" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="49" startOffset="12" endLine="56" endOffset="50"/></Target><Target id="@+id/pieChart" view="com.github.mikephil.charting.charts.PieChart"><Expressions/><location startLine="62" startOffset="8" endLine="69" endOffset="13"/></Target><Target id="@+id/tvMails" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="73" startOffset="4" endLine="85" endOffset="9"/></Target><Target id="@+id/rvMenu" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="87" startOffset="4" endLine="97" endOffset="9"/></Target><Target id="@+id/btPermission" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="100" startOffset="4" endLine="120" endOffset="9"/></Target></Targets></Layout>