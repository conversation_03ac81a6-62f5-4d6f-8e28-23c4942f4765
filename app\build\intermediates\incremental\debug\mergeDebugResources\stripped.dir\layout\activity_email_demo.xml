<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.EmailDemoActivity">

    <!-- Toolbar -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Email Provider Demo"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textAlignment="center"
        android:padding="16dp"
        android:background="@drawable/bg_button_app_color"
        android:textColor="@android:color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Authentication Status -->
    <TextView
        android:id="@+id/tv_auth_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="No providers authenticated"
        android:textSize="12sp"
        android:textAlignment="center"
        android:padding="8dp"
        android:background="@drawable/bg_button_white"
        android:layout_margin="8dp"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Control Buttons -->
    <LinearLayout
        android:id="@+id/layout_controls"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_margin="8dp"
        app:layout_constraintTop_toBottomOf="@id/tv_auth_status"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/btn_authenticate"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:text="Auth"
            android:textSize="12sp"
            android:background="@drawable/bg_button_app_color"
            android:textColor="@android:color/white"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btn_sync_all"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:text="Sync All"
            android:textSize="12sp"
            android:background="@drawable/bg_button_app_color"
            android:textColor="@android:color/white"
            android:layout_marginHorizontal="2dp" />

        <Button
            android:id="@+id/btn_clear_emails"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:text="Clear"
            android:textSize="12sp"
            android:background="@drawable/bg_button_white"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <!-- Provider Sync Buttons -->
    <LinearLayout
        android:id="@+id/layout_provider_sync"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginHorizontal="8dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintTop_toBottomOf="@id/layout_controls"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/btn_sync_gmail"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:text="Gmail"
            android:textSize="11sp"
            android:background="@drawable/bg_button_white"
            android:layout_marginEnd="4dp"
            android:enabled="false" />

        <Button
            android:id="@+id/btn_sync_outlook"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:text="Outlook"
            android:textSize="11sp"
            android:background="@drawable/bg_button_white"
            android:layout_marginHorizontal="2dp"
            android:enabled="false" />

        <Button
            android:id="@+id/btn_sync_yahoo"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_weight="1"
            android:text="Yahoo"
            android:textSize="11sp"
            android:background="@drawable/bg_button_white"
            android:layout_marginStart="4dp"
            android:enabled="false" />

    </LinearLayout>

    <!-- Email Count -->
    <TextView
        android:id="@+id/tv_email_count"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Total emails: 0"
        android:textSize="14sp"
        android:textStyle="bold"
        android:padding="8dp"
        android:layout_marginHorizontal="8dp"
        app:layout_constraintTop_toBottomOf="@id/layout_provider_sync"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Email Details -->
    <TextView
        android:id="@+id/tv_email_details"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="No emails"
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:paddingHorizontal="8dp"
        android:paddingBottom="8dp"
        android:layout_marginHorizontal="8dp"
        app:layout_constraintTop_toBottomOf="@id/tv_email_count"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Progress -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_email_details"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_progress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Loading..."
        android:textSize="12sp"
        android:textAlignment="center"
        android:visibility="gone"
        android:layout_marginTop="8dp"
        android:layout_marginHorizontal="16dp"
        app:layout_constraintTop_toBottomOf="@id/progress_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Swipe Refresh Layout -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/tv_progress"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Email List -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_emails"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            tools:listitem="@layout/item_email" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
