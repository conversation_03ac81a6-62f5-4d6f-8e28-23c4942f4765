<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lvWarning"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="@dimen/_40sdp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:lottie_rawRes="@raw/ai_loading"
        app:lottie_autoPlay="true"
        android:scaleType="fitCenter"
        app:lottie_loop="true"
        />




</androidx.constraintlayout.widget.ConstraintLayout>