<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".activities.EmailDetailActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Header -->
        <LinearLayout
            android:id="@+id/layout_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@drawable/bg_button_app_color"
            android:padding="16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <Button
                android:id="@+id/btn_back"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="← Back"
                android:textColor="@android:color/white"
                android:background="@android:color/transparent"
                android:textSize="14sp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btn_refresh"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="Refresh"
                android:textColor="@android:color/white"
                android:background="@android:color/transparent"
                android:textSize="12sp"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_download_raw"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="Raw"
                android:textColor="@android:color/white"
                android:background="@android:color/transparent"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- Progress -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:visibility="gone"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/layout_header"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Loading..."
            android:textSize="12sp"
            android:textAlignment="center"
            android:visibility="gone"
            android:layout_marginTop="8dp"
            android:layout_marginHorizontal="16dp"
            app:layout_constraintTop_toBottomOf="@id/progress_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Email Content -->
        <LinearLayout
            android:id="@+id/layout_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintTop_toBottomOf="@id/tv_progress"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Subject -->
            <TextView
                android:id="@+id/tv_subject"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Email Subject"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="16dp"
                tools:text="Important Meeting Tomorrow" />

            <!-- From -->
            <TextView
                android:id="@+id/tv_from"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="From: <EMAIL>"
                android:textColor="@android:color/black"
                android:textSize="14sp"
                tools:text="From: John Doe &lt;<EMAIL>&gt;" />

            <!-- Date -->
            <TextView
                android:id="@+id/tv_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Date: December 25, 2023 at 10:30"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="8dp"
                tools:text="Monday, December 25, 2023 at 10:30" />

            <!-- Recipients -->
            <TextView
                android:id="@+id/tv_recipients"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="To: <EMAIL>"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="To: <EMAIL>\nCC: <EMAIL>" />

            <!-- Provider -->
            <TextView
                android:id="@+id/tv_provider"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Provider: Gmail"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="8dp"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="Provider: Gmail" />

            <!-- Status -->
            <TextView
                android:id="@+id/tv_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Read: Yes | Starred: No"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="Read: Yes | Starred: No | Labels: Important, Work" />

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@android:color/darker_gray"
                android:layout_marginVertical="16dp" />

            <!-- Body -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Email Body:"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tv_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Email body content will appear here..."
                android:textSize="14sp"
                android:textColor="@android:color/black"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bg_button_white"
                android:padding="12dp"
                tools:text="Hi team,\n\nI wanted to remind everyone about our important meeting scheduled for tomorrow at 2 PM in the conference room.\n\nPlease bring your project updates and any questions you might have.\n\nBest regards,\nJohn" />

            <!-- Attachments -->
            <TextView
                android:id="@+id/tv_attachments"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Attachments:\ndocument.pdf (1.2 MB)\nimage.jpg (500 KB)"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bg_button_white"
                android:padding="12dp"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="Attachments:\ndocument.pdf (1.2 MB) - application/pdf\nimage.jpg (500 KB) - image/jpeg" />

            <!-- Headers -->
            <TextView
                android:id="@+id/tv_headers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Headers:\nMessage-ID: &lt;<EMAIL>&gt;"
                android:textSize="10sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bg_button_white"
                android:padding="12dp"
                android:visibility="gone"
                android:fontFamily="monospace"
                tools:visibility="visible"
                tools:text="Headers:\nMessage-ID: &lt;<EMAIL>&gt;\nIn-Reply-To: &lt;<EMAIL>&gt;" />

            <!-- Raw Content -->
            <TextView
                android:id="@+id/tv_raw_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Raw email content will appear here after downloading..."
                android:textSize="10sp"
                android:textColor="@android:color/darker_gray"
                android:background="@drawable/bg_button_white"
                android:padding="12dp"
                android:visibility="gone"
                android:fontFamily="monospace"
                android:scrollbars="vertical"
                tools:text="Raw Email Content (.eml format):\n\nReturn-Path: &lt;<EMAIL>&gt;\nReceived: from mail.example.com..." />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
