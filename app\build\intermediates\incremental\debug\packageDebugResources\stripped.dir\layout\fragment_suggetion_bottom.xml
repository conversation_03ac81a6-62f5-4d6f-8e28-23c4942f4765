<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".BottomSheet.SuggetionBottomFragment">
    
    
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSuggetion"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/_10sdp"
        android:padding="@dimen/_20sdp"
        android:gravity="center"
        android:fontFamily="@font/roboto_semi_regular"
        android:textColor="@color/black"
        android:text="@string/i_am_facing_difficulties_retrieving_the_recipient_s_email_id_could_you_kindly_enable_the_service_and_assist_in_verifying_the_mail_for_a_timely_resolution"

        />



    <androidx.appcompat.widget.AppCompatButton
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/btGotoGmail"
        app:layout_constraintTop_toBottomOf="@+id/tvSuggetion"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:layout_margin="@dimen/_12sdp"
        android:elevation="@dimen/_10sdp"
        android:paddingStart="@dimen/_15sdp"
        android:paddingEnd="@dimen/_15sdp"
        android:textAllCaps="false"
        android:visibility="gone"
        android:background="@drawable/bg_button_app_color"
        android:fontFamily="@font/roboto_semi_medium"
        android:textSize="@dimen/_10sdp"
        android:textColor="@color/white"
        android:text="Go To Gmail"
        />



</androidx.constraintlayout.widget.ConstraintLayout>