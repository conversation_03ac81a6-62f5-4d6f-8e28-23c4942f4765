<variant
    name="debug"
    useSupportLibraryVectorDrawables="true"
    package="com.tech.ekvayu"
    minSdkVersion="24"
    targetSdkVersion="35"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    partialResultsDir="build\intermediates\android_test_lint_partial_results\debug\lintAnalyzeDebugAndroidTest\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da7d1f52c6e73cdf64de3d2d6d533e46\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      viewBinding="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\androidTest\AndroidManifest.xml"
        javaDirectories="src\androidTest\java;src\androidTestDebug\java;src\androidTest\kotlin;src\androidTestDebug\kotlin"
        resDirectories="src\androidTest\res;src\androidTestDebug\res"
        assetsDirectories="src\androidTest\assets;src\androidTestDebug\assets"
        androidTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="appAuthRedirectScheme"
        value="com.tech.ekvayu" />
  </manifestPlaceholders>
  <artifact
      type="INSTRUMENTATION_TEST"
      applicationId="com.tech.ekvayu.test"
      generatedResourceFolders="build\generated\res\resValues\androidTest\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da7d1f52c6e73cdf64de3d2d6d533e46\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
