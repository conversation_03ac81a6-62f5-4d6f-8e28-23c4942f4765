<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/surface">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Theme Settings"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <!-- Current Theme Display -->
    <TextView
        android:id="@+id/tvCurrentTheme"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Current: System Default"
        android:textSize="14sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Theme Preview -->
    <TextView
        android:id="@+id/tvThemePreview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="☀️ Light Mode Active"
        android:textSize="16sp"
        android:textColor="@color/app_color"
        android:gravity="center"
        android:padding="12dp"
        android:background="@color/card_background"
        android:layout_marginBottom="20dp" />

    <!-- Theme Options -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Choose Theme:"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="12dp" />

    <RadioGroup
        android:id="@+id/rgThemeOptions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp">

        <RadioButton
            android:id="@+id/rbLight"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="☀️ Light Mode"
            android:textSize="16sp"
            android:textColor="@color/text_primary"
            android:padding="12dp"
            android:layout_marginBottom="8dp" />

        <RadioButton
            android:id="@+id/rbDark"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🌙 Dark Mode"
            android:textSize="16sp"
            android:textColor="@color/text_primary"
            android:padding="12dp"
            android:layout_marginBottom="8dp" />

        <RadioButton
            android:id="@+id/rbSystem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🔄 System Default"
            android:textSize="16sp"
            android:textColor="@color/text_primary"
            android:padding="12dp" />

    </RadioGroup>

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="System Default follows your device's theme settings automatically."
        android:textSize="12sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:layout_marginBottom="20dp" />

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnClose"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Close"
            android:textColor="@color/text_secondary"
            android:background="@android:color/transparent"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btnApply"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Apply Theme"
            android:textColor="@color/white"
            android:background="@color/app_color"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
