<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    xmlns:app="http://schemas.android.com/apk/res-auto">



    <androidx.cardview.widget.CardView
        android:id="@+id/cvBack"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardBackgroundColor="@color/app_color"
        android:layout_margin="@dimen/_13sdp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivBack"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="@dimen/_24sdp"
            android:layout_height="@dimen/_24sdp"
            android:layout_gravity="center"
            android:src="@drawable/back"
            android:layout_margin="@dimen/_20sdp"
            />

    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Ekvayu Tech Pvt Ltd"
        android:textColor="@color/app_color"
        app:layout_constraintStart_toEndOf="@+id/cvBack"
        app:layout_constraintEnd_toStartOf="@+id/ivLogo"
        app:layout_constraintTop_toTopOf="@+id/cvBack"
        app:layout_constraintBottom_toBottomOf="@+id/cvBack"
        app:layout_constraintEnd_toEndOf="parent"
        android:fontFamily="@font/roboto_semi_bold"
        android:textSize="@dimen/_14sdp"
        />


    <androidx.cardview.widget.CardView
        android:id="@+id/cvTheme"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/cvBack"
        app:layout_constraintBottom_toBottomOf="@+id/cvBack"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardBackgroundColor="@color/surface"
        android:layout_margin="@dimen/_13sdp"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackground">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivTheme"
            android:layout_width="@dimen/_20sdp"
            android:layout_height="@dimen/_20sdp"
            android:layout_gravity="center"
            android:src="@drawable/theme_icon"
            android:tint="@color/app_color" />

    </androidx.cardview.widget.CardView>


    <!-- <androidx.appcompat.widget.Toolbar
         app:layout_constraintTop_toTopOf="parent"
         android:id="@+id/defaultToolbar"
         android:layout_width="match_parent"
         android:layout_height="?attr/actionBarSize"
         android:background="?attr/colorPrimary"
         android:titleTextColor="@android:color/white"
         android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
         android:paddingStart="16dp"
         android:paddingEnd="16dp">


         <androidx.cardview.widget.CardView
             android:layout_gravity="end"
             android:layout_width="wrap_content"
             android:layout_height="wrap_content"
             app:cardCornerRadius="@dimen/_20sdp"
             >

             <androidx.appcompat.widget.AppCompatImageView
                 android:id="@+id/themeIcon"
                 android:layout_width="@dimen/_30sdp"
                 android:layout_height="@dimen/_30sdp"
                 android:layout_gravity="center"
                 android:src="@mipmap/ic_launcher"
                 android:background="@color/white"
                 />

         </androidx.cardview.widget.CardView>



     </androidx.appcompat.widget.Toolbar>
 -->

  <!--  <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLogo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/ic_launcher"
        android:scaleType="fitCenter"
        />

    <androidx.appcompat.widget.AppCompatTextView
        app:layout_constraintStart_toEndOf="@+id/ivLogo"
        app:layout_constraintTop_toTopOf="@+id/ivLogo"
        app:layout_constraintBottom_toBottomOf="@+id/ivLogo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Ekvayu Tech Pvt Ltd"
        android:fontFamily="@font/roboto_semi_medium"
        android:textColor="@color/app_color"
        android:layout_marginStart="@dimen/_10sdp"
        />
-->



</androidx.constraintlayout.widget.ConstraintLayout>