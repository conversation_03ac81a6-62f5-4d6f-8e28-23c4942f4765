<?xml version='1.0' encoding='UTF-8'?>
<com.google.mlkit>
  <acceleration versions="16.0.0-beta1,16.0.0-beta2"/>
  <barcode-scanning versions="16.0.0,16.0.1,16.0.2,16.0.3,16.1.0,16.1.1,16.1.2,16.2.0,17.0.0,17.0.1,17.0.2,17.0.3,17.1.0,17.2.0,17.3.0"/>
  <barcode-scanning-common versions="16.0.0,17.0.0"/>
  <camera versions="16.0.0-beta1,16.0.0-beta2,16.0.0-beta3"/>
  <common versions="16.0.0,16.0.1,16.1.0,17.0.0,17.1.0,17.1.1,17.2.0,17.3.0,17.4.0,17.5.0,18.0.0,18.1.0,18.2.0,18.3.0,18.4.0,18.5.0,18.6.0,18.7.0,18.8.0,18.9.0,18.10.0,18.11.0"/>
  <digital-ink-recognition versions="16.0.0,16.1.0,16.2.0,17.0.0,17.0.1,18.0.0,18.1.0"/>
  <entity-extraction versions="16.0.0-beta1,16.0.0-beta2,16.0.0-beta3,16.0.0-beta4,16.0.0-beta5,16.0.0-beta6"/>
  <face-detection versions="16.0.0,16.0.1,16.0.2,16.0.3,16.0.4,16.0.5,16.0.6,16.0.7,16.1.0,16.1.1,16.1.2,16.1.3,16.1.4,16.1.5,16.1.6,16.1.7"/>
  <face-mesh-detection versions="16.0.0-beta1,16.0.0-beta2,16.0.0-beta3"/>
  <genai-common versions="1.0.0-beta1"/>
  <genai-image-description versions="1.0.0-beta1"/>
  <genai-proofreading versions="1.0.0-beta1"/>
  <genai-rewriting versions="1.0.0-beta1"/>
  <genai-summarization versions="1.0.0-beta1"/>
  <image-labeling versions="16.0.0,16.1.0,16.2.0,17.0.0,17.0.1,17.0.2,17.0.3,17.0.4,17.0.5,17.0.6,17.0.7,17.0.8,17.0.9"/>
  <image-labeling-automl versions="16.0.0,16.1.0,16.2.0,16.2.1"/>
  <image-labeling-common versions="16.0.0,16.1.0,16.2.0,16.2.1,17.0.0,17.1.0,17.2.0,18.0.0,18.1.0"/>
  <image-labeling-custom versions="16.0.0,16.1.0,16.2.0,16.2.1,16.3.0,16.3.1,17.0.0,17.0.1,17.0.2,17.0.3"/>
  <image-labeling-custom-common versions="16.0.0,16.0.1,17.0.0"/>
  <image-labeling-default-common versions="16.0.0,16.0.1,16.0.2,17.0.0"/>
  <language-id versions="16.0.0,16.1.0,16.1.1,17.0.0,17.0.1,17.0.2,17.0.3,17.0.4,17.0.5,17.0.6"/>
  <language-id-common versions="16.0.0,16.1.0"/>
  <linkfirebase versions="16.0.0,16.0.1,16.1.0,16.1.1,17.0.0"/>
  <mediapipe-internal versions="16.0.0,16.0.1-beta1,16.1.0-beta1,17.0.0-beta1,17.0.0-beta2,17.0.0-beta3,17.0.0-beta4,17.0.0-beta5,17.0.0-beta6,17.0.0-beta7,17.0.0-beta8,17.0.0-beta9,17.0.0-beta10"/>
  <object-detection versions="16.0.0,16.1.0,16.2.0,16.2.1,16.2.2,16.2.3,16.2.4,16.2.5,16.2.6,16.2.7,16.2.8,17.0.0,17.0.1,17.0.2"/>
  <object-detection-common versions="16.0.0,16.1.0,16.2.0,16.2.1,17.0.0,17.0.1,17.1.0,17.2.0,18.0.0"/>
  <object-detection-custom versions="16.0.0,16.1.0,16.2.0,16.2.1,16.3.0,16.3.1,16.3.2,16.3.3,16.3.4,17.0.0,17.0.1,17.0.2"/>
  <playstore-dynamic-feature-support versions="16.0.0-beta1,16.0.0-beta2"/>
  <pose-detection versions="16.0.0,17.0.0,17.0.1-beta1,17.0.1-beta2,17.0.1-beta3,17.0.1-beta4,17.0.1-beta5,17.0.1-beta6,17.0.1-beta7,18.0.0-beta1,18.0.0-beta2,18.0.0-beta3,18.0.0-beta4,18.0.0-beta5"/>
  <pose-detection-accurate versions="17.0.0,17.0.1-beta1,17.0.1-beta2,17.0.1-beta3,17.0.1-beta4,17.0.1-beta5,17.0.1-beta6,17.0.1-beta7,18.0.0-beta1,18.0.0-beta2,18.0.0-beta3,18.0.0-beta4,18.0.0-beta5"/>
  <pose-detection-common versions="17.0.0,17.0.1-beta1,17.1.0-beta1,17.1.0-beta2,17.1.0-beta3,17.1.0-beta4,17.1.0-beta5,17.1.0-beta6,18.0.0-beta1,18.0.0-beta2,18.0.0-beta3,18.0.0-beta4,18.0.0-beta5"/>
  <segmentation-selfie versions="16.0.0-beta1,16.0.0-beta2,16.0.0-beta3,16.0.0-beta4,16.0.0-beta5,16.0.0-beta6"/>
  <smart-reply versions="16.0.0,16.1.0,16.1.1,16.2.0,17.0.0,17.0.1,17.0.2,17.0.3,17.0.4"/>
  <smart-reply-common versions="16.0.0,16.1.0"/>
  <text-recognition versions="16.0.0-beta1,16.0.0-beta2,16.0.0-beta3,16.0.0-beta4,16.0.0-beta5,16.0.0-beta6,16.0.0,16.0.1"/>
  <text-recognition-bundled-common versions="16.0.0-beta1,16.0.0-beta2,16.0.0-beta3,16.0.0-beta4,16.0.0-beta5,16.0.0-beta6,16.0.0,17.0.0"/>
  <text-recognition-chinese versions="16.0.0-beta1,16.0.0-beta2,16.0.0-beta3,16.0.0-beta4,16.0.0-beta5,16.0.0-beta6,16.0.0,16.0.1"/>
  <text-recognition-devanagari versions="16.0.0-beta1,16.0.0-beta2,16.0.0-beta3,16.0.0-beta4,16.0.0-beta5,16.0.0-beta6,16.0.0,16.0.1"/>
  <text-recognition-japanese versions="16.0.0-beta1,16.0.0-beta2,16.0.0-beta3,16.0.0-beta4,16.0.0-beta5,16.0.0-beta6,16.0.0,16.0.1"/>
  <text-recognition-korean versions="16.0.0-beta1,16.0.0-beta2,16.0.0-beta3,16.0.0-beta4,16.0.0-beta5,16.0.0-beta6,16.0.0,16.0.1"/>
  <translate versions="16.0.0,16.1.0,16.1.1,16.1.2,17.0.0,17.0.1,17.0.2,17.0.3"/>
  <vision-common versions="16.0.0,16.0.1,16.1.0,16.2.0,16.3.0,16.4.0,16.5.0,16.6.0,16.7.0,17.0.0,17.1.0,17.2.0,17.2.1,17.3.0"/>
  <vision-interfaces versions="16.0.0,16.1.0,16.2.0,16.3.0"/>
  <vision-internal-vkp versions="16.0.0,17.0.0,17.0.1,18.0.0,18.1.0,18.1.1,18.2.0,18.2.1,18.2.2,18.2.3"/>
</com.google.mlkit>
