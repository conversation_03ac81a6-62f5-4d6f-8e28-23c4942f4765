D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DisputeListAdapter.kt:3: Warning: Don't include android.R here; use a fully qualified name for each usage instead [SuspiciousImport]
import android.R
~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\SpamMailAdapter.kt:3: Warning: Don't include android.R here; use a fully qualified name for each usage instead [SuspiciousImport]
import android.R
~~~~~~~~~~~~~~~~

   Explanation for issues of type "SuspiciousImport":
   Importing android.R is usually not intentional; it sometimes happens when
   you use an IDE and ask it to automatically add imports at a time when your
   project's R class it not present.

   Once the import is there you might get a lot of "confusing" error messages
   because of course the fields available on android.R are not the ones you'd
   expect from just looking at your own R class.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DashboardMenuAdapter.kt:25: Error: Do not treat position as fixed; only use immediately and call holder.getAdapterPosition() to look it up later [RecyclerView]
    override fun onBindViewHolder(holder: MenuViewHolder, position: Int) {
                                                          ~~~~~~~~~~~~~

   Explanation for issues of type "RecyclerView":
   RecyclerView will not call onBindViewHolder again when the position of the
   item changes in the data set unless the item itself is invalidated or the
   new position cannot be determined.

   For this reason, you should only use the position parameter while acquiring
   the related data item inside this method, and should not keep a copy of
   it.

   If you need the position of an item later on (e.g. in a click listener),
   use getAdapterPosition() which will have the updated adapter position.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18: Warning: WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the MediaStore.createWriteRequest intent. [ScopedStorage]
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19: Warning: READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: READ_MEDIA_IMAGES, READ_MEDIA_VIDEO or READ_MEDIA_AUDIO. [ScopedStorage]
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\header_layout.xml:40: Error: The id "ivLogo" is not defined anywhere. [UnknownId]
        app:layout_constraintEnd_toStartOf="@+id/ivLogo"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnknownId":
   The @+id/ syntax refers to an existing id, or creates a new one if it has
   not already been defined elsewhere. However, this means that if you have a
   typo in your reference, or if the referred view no longer exists, you do
   not get a warning since the id will be created on demand. This check
   catches errors where you have renamed an id without updating all of the
   references to it.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:19: Warning: This androidx.constraintlayout.widget.ConstraintLayout should use android:layout_height="wrap_content" [ScrollViewSize]
        android:layout_height="match_parent"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_device_details.xml:12: Warning: This androidx.constraintlayout.widget.ConstraintLayout should use android:layout_height="wrap_content" [ScrollViewSize]
        android:layout_height="match_parent"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_raise_bottom_sheet.xml:12: Warning: This androidx.constraintlayout.widget.ConstraintLayout should use android:layout_height="wrap_content" [ScrollViewSize]
        android:layout_height="match_parent"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_warning_bottom_sheet.xml:12: Warning: This androidx.cardview.widget.CardView should use android:layout_height="wrap_content" [ScrollViewSize]
        android:layout_height="match_parent"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_spam_detail.xml:9: Warning: This androidx.constraintlayout.widget.ConstraintLayout should use android:layout_height="wrap_content" [ScrollViewSize]
        android:layout_height="match_parent">
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScrollViewSize":
   ScrollView children must set their layout_width or layout_height attributes
   to wrap_content rather than fill_parent or match_parent in the scrolling
   dimension.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values-night\themes.xml:26: Error: android:windowLightNavigationBar requires API level 27 (current min is 24) [NewApi]
        <item name="android:windowLightNavigationBar">false</item>
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\themes.xml:26: Error: android:windowLightNavigationBar requires API level 27 (current min is 24) [NewApi]
        <item name="android:windowLightNavigationBar">true</item>
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NewApi":
   This check scans through all the Android API calls in the application and
   warns about any calls that are not available on all versions targeted by
   this application (according to its minimum SDK attribute in the manifest).

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

   If you are deliberately setting android: attributes in style definitions,
   make sure you place this in a values-vNN folder in order to avoid running
   into runtime conflicts on certain devices where manufacturers have added
   custom attributes whose ids conflict with the new ones on later platforms.

   Similarly, you can use tools:targetApi="11" in an XML file to indicate that
   the element will only be inflated in an adequate context.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\mail_raised.xml:2: Warning: Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more [VectorRaster]
    android:width="570dp"
                   ~~~~~

   Explanation for issues of type "VectorRaster":
   Vector icons require API 21 or API 24 depending on used features, but when
   minSdkVersion is less than 21 or 24 and Android Gradle plugin 1.4 or higher
   is used, a vector drawable placed in the drawable folder is automatically
   moved to drawable-anydpi-v21 or drawable-anydpi-v24 and bitmap images are
   generated for different screen resolutions for backwards compatibility.

   However, there are some limitations to this raster image generation, and
   this lint check flags elements and attributes that are not fully supported.
   You should manually check whether the generated output is acceptable for
   those older devices.

D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.8.0 is available: 8.11.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.11.0 is difficult: 8.8.2) [AndroidGradlePluginVersion]
agp = "8.8.0"
      ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.8.0 is available: 8.11.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.11.0 is difficult: 8.8.2) [AndroidGradlePluginVersion]
agp = "8.8.0"
      ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.8.0 is available: 8.11.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.11.0 is difficult: 8.8.2) [AndroidGradlePluginVersion]
agp = "8.8.0"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:80: Warning: A newer version of com.google.firebase:firebase-auth than 22.3.1 is available: 23.2.1 [GradleDependency]
    implementation("com.google.firebase:firebase-auth:22.3.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:81: Warning: A newer version of com.google.firebase:firebase-bom than 32.7.0 is available: 33.16.0 [GradleDependency]
    implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0 [GradleDependency]
material = "1.10.0"
           ~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0 [GradleDependency]
material = "1.10.0"
           ~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0 [GradleDependency]
material = "1.10.0"
           ~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:10: Warning: A newer version of androidx.activity:activity than 1.8.0 is available: 1.10.1 [GradleDependency]
activity = "1.8.0"
           ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:10: Warning: A newer version of androidx.activity:activity than 1.8.0 is available: 1.10.1 [GradleDependency]
activity = "1.8.0"
           ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:10: Warning: A newer version of androidx.activity:activity than 1.8.0 is available: 1.10.1 [GradleDependency]
activity = "1.8.0"
           ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:11: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:11: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\gradle\libs.versions.toml:11: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\EkService\GmailAccessibilityService.kt:70: Warning: Switch statement on an int with known associated constant missing case AccessibilityEvent.TYPE_ANNOUNCEMENT, AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT, AccessibilityEvent.TYPE_GESTURE_DETECTION_END, AccessibilityEvent.TYPE_GESTURE_DETECTION_START, AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED, AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE, AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END, AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START, AccessibilityEvent.TYPE_TOUCH_INTERACTION_END, AccessibilityEvent.TYPE_TOUCH_INTERACTION_START, AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED, AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED, AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED, AccessibilityEvent.TYPE_VIEW_FOCUSED, AccessibilityEvent.TYPE_VIEW_HOVER_ENTER, AccessibilityEvent.TYPE_VIEW_HOVER_EXIT, AccessibilityEvent.TYPE_VIEW_LONG_CLICKED, AccessibilityEvent.TYPE_VIEW_SCROLLED, AccessibilityEvent.TYPE_VIEW_SELECTED, AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL, AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED, AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED, AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY, AccessibilityEvent.TYPE_WINDOWS_CHANGED [SwitchIntDef]
        when (event.eventType) {
        ~~~~

   Explanation for issues of type "SwitchIntDef":
   This check warns if a switch statement does not explicitly include all the
   values declared by the typedef @IntDef declaration.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\EkService\GmailAccessibilityService.kt:298: Warning: Using getString to get device identifiers is not recommended [HardwareIds]
        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\EkService\NewGmailAccessibilityService.kt:179: Warning: Using getString to get device identifiers is not recommended [HardwareIds]
        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardwareIds":
   Using these device identifiers is not recommended other than for high value
   fraud prevention and advanced telephony use-cases. For advertising
   use-cases, use AdvertisingIdClient$Info#getId and for analytics, use
   InstanceId#getId.

   https://developer.android.com/training/articles/user-data-ids.html

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\BaseClass\CommonUtil.kt:57: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\BaseClass\DeviceInfoHelper.kt:162: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        val isConnected = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\BaseClass\DeviceInfoHelper.kt:175: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        val connectionType = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\BaseClass\DeviceInfoHelper.kt:300: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            minSdkVersion = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\EkService\GmailAccessibilityService.kt:763: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\EkService\GmailAccessibilityService.kt:771: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\EkService\NewGmailAccessibilityService.kt:570: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\EkService\NewGmailAccessibilityService.kt:578: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:121: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
        <LinearLayout
         ~~~~~~~~~~~~

   Explanation for issues of type "UseCompoundDrawables":
   A LinearLayout which contains an ImageView and a TextView can be more
   efficiently handled as a compound drawable (a single TextView, using the
   drawableTop, drawableLeft, drawableRight and/or drawableBottom attributes
   to draw one or more images adjacent to the text).

   If the two widgets are offset from each other with margins, this can be
   replaced with a drawablePadding attribute.

   There's a lint quickfix to perform this conversion in the Eclipse plugin.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\argument.xml:3: Warning: Very long vector path (3574 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#DF4444" android:pathData="M163.2,21.86C164.02,21.86 164.85,21.85 165.69,21.85C168.45,21.84 171.2,21.84 173.95,21.84C175.93,21.83 177.9,21.83 179.88,21.82C185.24,21.81 190.6,21.8 195.97,21.8C199.32,21.8 202.67,21.79 206.03,21.79C216.52,21.77 227.02,21.76 237.51,21.76C249.62,21.76 261.72,21.74 273.83,21.71C283.19,21.69 292.55,21.68 301.92,21.68C307.51,21.68 313.1,21.67 318.69,21.65C323.95,21.64 329.21,21.63 334.47,21.64C336.39,21.64 338.32,21.64 340.25,21.63C355.86,21.55 369.25,23.19 381.31,34.16C392.43,46.28 394.61,60.1 394.45,75.88C394.45,77.54 394.45,79.21 394.46,80.88C394.46,85.37 394.43,89.85 394.41,94.34C394.38,99.05 394.38,103.75 394.37,108.46C394.36,117.35 394.33,126.24 394.29,135.13C394.24,145.26 394.22,155.4 394.2,165.53C394.16,186.35 394.09,207.18 394,228C395.1,228.15 396.2,228.31 397.34,228.46C400.33,228.9 403.13,229.46 406.04,230.23C406.97,230.47 407.9,230.72 408.85,230.97C409.84,231.23 410.82,231.49 411.83,231.77C412.86,232.04 413.9,232.32 414.96,232.6C417.14,233.18 419.31,233.77 421.49,234.35C424.79,235.24 428.09,236.12 431.4,236.99C433.52,237.56 435.65,238.13 437.77,238.7C438.75,238.96 439.72,239.21 440.73,239.48C454.46,243.24 466.22,250.12 473.75,262.59C483.69,281.75 478.36,301.16 472.9,320.82C472.27,323.18 471.63,325.53 471,327.89C469.34,334.06 467.66,340.22 465.96,346.38C464.27,352.57 462.6,358.78 460.92,364.98C457.89,376.23 454.84,387.48 451.73,398.71C450.53,403.05 449.36,407.41 448.22,411.77C447.62,413.98 447.03,416.2 446.44,418.41C446.18,419.42 445.92,420.42 445.66,421.45C441.95,435.12 435.09,445.69 423.06,453.38C416,457.28 410.02,459.07 401.9,458.94C398.36,458.93 395.47,459.14 392,460C388.89,463.59 387.63,467.59 386,472C380.42,480.81 369.85,487 359.88,489.54C356.18,490.13 352.53,490.14 348.8,490.14C347.98,490.14 347.15,490.15 346.31,490.15C343.55,490.16 340.8,490.16 338.05,490.16C336.07,490.17 334.1,490.17 332.12,490.18C326.76,490.19 321.4,490.2 316.03,490.2C312.68,490.2 309.33,490.21 305.97,490.21C295.48,490.23 284.98,490.24 274.49,490.24C262.38,490.24 250.28,490.26 238.17,490.29C228.81,490.31 219.45,490.32 210.08,490.32C204.49,490.32 198.9,490.33 193.31,490.35C188.05,490.36 182.79,490.37 177.53,490.36C175.61,490.36 173.68,490.36 171.75,490.37C156.14,490.45 142.75,488.81 130.69,477.84C120.97,467.24 117.36,454.64 117.55,440.53C117.55,439.35 117.54,438.17 117.54,436.95C117.55,433.09 117.58,429.22 117.61,425.36C117.62,422.66 117.62,419.97 117.63,417.27C117.64,410.93 117.67,404.59 117.71,398.24C117.76,391.02 117.78,383.79 117.8,376.56C117.84,361.71 117.91,346.85 118,332C116.59,332.03 116.59,332.03 115.16,332.06C113.92,332.07 112.67,332.09 111.39,332.1C109.56,332.13 109.56,332.13 107.68,332.16C95.71,331.64 85.04,325.13 77.02,316.5C68.09,305.43 65.17,291.04 61.69,277.59C61.12,275.42 60.54,273.25 59.96,271.08C58.45,265.38 56.96,259.67 55.47,253.97C53.98,248.26 52.47,242.56 50.96,236.86C49.21,230.24 47.46,223.62 45.73,217C43.33,207.81 40.92,198.63 38.4,189.47C38.01,188.07 37.63,186.67 37.25,185.28C36.58,182.84 35.91,180.41 35.22,177.98C31.59,164.72 30.52,150.61 37,138C46.28,122.63 58.23,115.88 75.25,111.45C76.38,111.15 77.51,110.84 78.68,110.53C81.05,109.89 83.43,109.27 85.81,108.64C89.45,107.68 93.1,106.71 96.74,105.73C99.05,105.12 101.37,104.5 103.68,103.89C104.77,103.6 105.86,103.31 106.99,103.01C108,102.74 109.01,102.48 110.04,102.21C110.93,101.97 111.82,101.74 112.73,101.5C115,101 115,101 118,101C117.97,99.19 117.97,99.19 117.94,97.33C117.86,92.8 117.82,88.26 117.78,83.71C117.76,81.76 117.73,79.8 117.7,77.85C117.45,63.33 118.12,51.13 127,39C127.74,37.97 128.49,36.94 129.25,35.88C138.51,26.2 150.03,21.84 163.2,21.86Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\argument.xml:5: Warning: Very long vector path (1569 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#0068C0" android:pathData="M229,83C229.86,83.42 230.72,83.85 231.61,84.29C242.75,90.34 250.21,100.48 254.13,112.46C255.16,116.25 256.1,120.05 257,123.88C257.51,125.95 258.02,128.02 258.54,130.1C258.88,131.47 259.22,132.85 259.55,134.22C260.96,139.92 262.53,145.56 264.19,151.19C266.34,158.57 268.27,165.98 270.1,173.45C272.29,182.35 274.84,191.14 277.42,199.94C279.79,208.33 279.79,208.33 279,213C276.94,214.98 274.73,216.54 272.39,218.19C264.01,224.55 260.18,234.38 258.08,244.32C255.38,256.65 255.38,256.65 251.8,260.14C248.85,261.21 246.11,261.6 243,262C240.97,262.52 238.97,263.12 236.97,263.72C230.15,265.74 223.23,267.46 216.34,269.23C193.28,274.2 193.28,274.2 174.77,287.68C170.37,292.76 165.75,297.61 161.13,302.48C158,305.79 154.89,309.09 151.9,312.52C141.65,324.28 130.46,331.08 114.77,332.24C100.29,332.55 89.58,328.09 78.88,318.5C68.49,307.32 65.4,291.92 61.69,277.59C61.12,275.42 60.54,273.25 59.96,271.08C58.45,265.38 56.96,259.67 55.47,253.97C53.98,248.26 52.47,242.56 50.96,236.86C49.21,230.24 47.46,223.62 45.73,217C43.33,207.81 40.92,198.63 38.4,189.47C38.01,188.07 37.63,186.67 37.25,185.28C36.58,182.84 35.91,180.41 35.22,177.98C31.59,164.72 30.52,150.61 37,138C46.14,122.86 57.93,115.96 74.72,111.59C75.82,111.3 76.91,111 78.04,110.7C80.36,110.08 82.67,109.47 84.98,108.86C88.47,107.95 91.94,107.02 95.42,106.08C105.97,103.27 116.53,100.58 127.2,98.28C132.67,97.1 138.02,95.68 143.38,94.06C150.3,92.03 157.24,90.15 164.25,88.44C170.62,86.89 176.91,85.19 183.19,83.31C184.59,82.9 185.99,82.48 187.39,82.07C189.4,81.47 191.42,80.87 193.42,80.24C204.83,77.1 218.47,77.61 229,83Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\argument.xml:7: Warning: Very long vector path (2237 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#DEDEDE" android:pathData="M254,259C251.11,270.27 248.18,281.52 244.94,292.69C242.68,300.48 240.6,308.3 238.69,316.18C237.68,320.38 236.54,324.46 235.19,328.56C231.81,340.95 232.82,354.74 239,366C240.49,368.25 242.02,370.35 243.77,372.41C245,374 245,374 246,377C247.84,378.07 247.84,378.07 250,379C250.63,379.43 251.26,379.86 251.91,380.3C263.36,388.02 277.52,390.7 290.75,393.94C299.05,395.97 307.3,398.11 315.5,400.5C316.44,400.76 317.37,401.02 318.34,401.29C326.5,403.73 332.48,407.81 338,414.31C342.89,419.93 347.99,425.33 353.12,430.74C358.97,436.92 358.97,436.92 361.64,440.07C370.13,450.07 379.37,454.77 392,458C389.37,468.04 383.89,476.8 375.21,482.72C366.4,487.81 358.96,490.15 348.8,490.14C347.98,490.14 347.15,490.15 346.31,490.15C343.55,490.16 340.8,490.16 338.05,490.16C336.07,490.17 334.1,490.17 332.12,490.18C326.76,490.19 321.4,490.2 316.03,490.2C312.68,490.2 309.33,490.21 305.97,490.21C295.48,490.23 284.98,490.24 274.49,490.24C262.38,490.24 250.28,490.26 238.17,490.29C228.81,490.31 219.45,490.32 210.08,490.32C204.49,490.32 198.9,490.33 193.31,490.35C188.05,490.36 182.79,490.37 177.53,490.36C175.61,490.36 173.68,490.36 171.75,490.37C156.14,490.45 142.75,488.81 130.69,477.84C120.94,467.21 117.36,454.58 117.55,440.44C117.55,439.25 117.54,438.06 117.54,436.83C117.55,432.93 117.58,429.04 117.61,425.14C117.62,422.42 117.62,419.7 117.63,416.99C117.64,410.59 117.67,404.19 117.71,397.8C117.76,390.51 117.78,383.22 117.8,375.93C117.84,360.95 117.91,345.98 118,331C118.81,330.81 119.61,330.62 120.44,330.42C121.51,330.16 122.58,329.9 123.68,329.64C124.73,329.38 125.78,329.13 126.87,328.87C138.61,325.61 146.62,318.04 154.32,308.93C155.97,307.03 157.7,305.26 159.5,303.5C161.88,301.16 164.13,298.77 166.31,296.25C166.89,295.59 167.46,294.93 168.05,294.25C169.5,292.58 170.94,290.9 172.38,289.22C173.23,288.26 174.07,287.3 174.94,286.31C175.71,285.42 176.48,284.53 177.28,283.61C184.22,276.95 191.44,274.74 200.69,272.56C203.1,271.96 205.52,271.36 207.94,270.76C209.13,270.47 210.32,270.17 211.55,269.87C216.46,268.63 221.33,267.28 226.19,265.88C227.45,265.51 227.45,265.51 228.74,265.14C232.77,263.98 236.78,262.76 240.78,261.48C241.9,261.12 241.9,261.12 243.04,260.76C244.43,260.31 245.82,259.85 247.21,259.38C251.78,257.89 251.78,257.89 254,259Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\argument.xml:9: Warning: Very long vector path (1627 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#DEDEDE" android:pathData="M163.2,21.86C164.02,21.86 164.85,21.85 165.69,21.85C168.45,21.84 171.2,21.84 173.95,21.84C175.93,21.83 177.9,21.83 179.88,21.82C185.24,21.81 190.6,21.8 195.97,21.8C199.32,21.8 202.67,21.79 206.03,21.79C216.52,21.77 227.02,21.76 237.51,21.76C249.62,21.76 261.72,21.74 273.83,21.71C283.19,21.69 292.55,21.68 301.92,21.68C307.51,21.68 313.1,21.67 318.69,21.65C323.95,21.64 329.21,21.63 334.47,21.64C336.39,21.64 338.32,21.64 340.25,21.63C355.86,21.55 369.25,23.19 381.31,34.16C392.43,46.28 394.61,60.1 394.45,75.88C394.45,77.54 394.45,79.21 394.46,80.88C394.46,85.37 394.43,89.85 394.41,94.34C394.38,99.05 394.38,103.75 394.37,108.46C394.36,117.35 394.33,126.24 394.29,135.13C394.24,145.26 394.22,155.4 394.2,165.53C394.16,186.35 394.09,207.18 394,228C392.56,227.68 391.12,227.36 389.68,227.04C388.48,226.78 388.48,226.78 387.26,226.51C375.73,223.92 364.24,221.04 352.94,217.63C318.75,205.94 318.75,205.94 284,210C283.09,210.36 282.18,210.73 281.24,211.1C280.5,211.4 279.76,211.69 279,212C278.72,210.77 278.44,209.55 278.15,208.29C275.6,197.28 272.76,186.4 269.66,175.53C267.59,168.22 265.68,160.88 263.88,153.5C262.02,145.92 260.03,138.42 257.75,130.96C256.52,126.9 255.47,122.83 254.54,118.69C251.55,105.67 245.81,94.09 234.25,86.75C232.52,85.8 230.77,84.88 229,84C228.19,83.58 227.38,83.17 226.55,82.74C207.08,74.2 184.52,83.34 165.52,89.1C157.33,91.56 149.03,93.53 140.73,95.57C134.67,97.07 128.65,98.68 122.63,100.34C120,101 120,101 118,101C117.9,95.24 117.83,89.48 117.78,83.71C117.76,81.76 117.73,79.8 117.7,77.85C117.45,63.33 118.12,51.13 127,39C127.74,37.97 128.49,36.94 129.25,35.88C138.51,26.2 150.03,21.84 163.2,21.86Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\argument.xml:13: Warning: Very long vector path (1477 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#F67E7E" android:pathData="M346.4,275.95C350.27,279 352.23,282.94 354.63,287.19C355.57,288.85 356.52,290.51 357.48,292.17C358.1,293.27 358.1,293.27 358.73,294.4C359.15,294.92 359.57,295.45 360,296C363.58,296 365.18,294.72 368.09,292.8C378.94,285.66 378.94,285.66 385.91,286.36C389.53,287.47 391.43,289.23 394,292C395.2,294.39 395.27,296.08 395.38,298.75C395.42,299.59 395.47,300.43 395.52,301.3C394.63,305.91 391.84,308.73 388.09,311.32C385.98,312.57 383.85,313.74 381.69,314.88C380.95,315.28 380.21,315.68 379.44,316.09C377.63,317.07 375.82,318.04 374,319C375.76,323.91 378.44,328.2 381.25,332.56C384.08,337.25 384.51,340.53 384,346C381.78,350.36 379.55,353.1 375,355C370.87,355.53 368.7,355.39 365.04,353.38C361.64,350.72 359.96,348.28 357.81,344.56C357.16,343.45 356.51,342.34 355.83,341.19C355.23,340.14 354.62,339.09 354,338C353,336.33 352,334.66 351,333C349.45,333.83 349.45,333.83 347.86,334.68C346.49,335.41 345.12,336.14 343.75,336.88C343.07,337.24 342.39,337.6 341.68,337.98C340.36,338.69 339.04,339.39 337.72,340.09C336.39,340.79 335.07,341.51 333.76,342.23C330.59,343.61 327.35,343.53 324,343C319.93,340.89 317.71,339.22 316,335C315.63,332.85 315.63,332.85 315.56,330.63C315.52,329.9 315.48,329.17 315.44,328.41C316.46,324.02 319.3,321.17 322.91,318.68C325.02,317.43 327.15,316.26 329.31,315.13C330.05,314.72 330.79,314.32 331.56,313.91C333.37,312.93 335.18,311.96 337,311C336.27,307.61 334.94,305.09 333.12,302.13C325.96,290.49 325.96,290.49 327,284C330.91,275.87 337.95,272.61 346.4,275.95Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\argument.xml:15: Warning: Very long vector path (1077 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#329AE1" android:pathData="M186,145C190.23,146.63 192.6,148.18 195,152C196.96,160.35 193.07,166.69 188.88,173.63C188.25,174.7 187.62,175.77 186.98,176.88C185.67,179.13 184.34,181.38 183.01,183.61C181.04,186.93 179.1,190.27 177.16,193.61C174.32,198.53 171.46,203.44 168.57,208.34C167.5,210.16 166.43,211.99 165.36,213.82C164.84,214.71 164.32,215.61 163.78,216.53C163.08,217.72 163.08,217.72 162.37,218.95C160.34,221.99 158.32,223.51 155,225C148.23,225.87 144.02,223.3 138.38,220C137.5,219.5 136.62,219 135.72,218.48C133.96,217.47 132.2,216.46 130.45,215.43C128.29,214.17 126.12,212.93 123.95,211.7C118.19,208.34 114.35,205.92 111,200C110.39,195.7 110.68,193.62 112.69,189.75C116.11,185.67 118.27,184.18 123.62,183.62C128.91,183.69 132.55,186.19 136.88,189C138.37,189.94 139.87,190.88 141.37,191.81C142.02,192.23 142.67,192.65 143.34,193.08C144.98,194.16 144.98,194.16 147,194C147.43,193.24 147.86,192.48 148.31,191.7C152.16,184.93 156.03,178.18 159.9,171.42C161.98,167.79 164.04,164.15 166.08,160.5C166.93,159 167.78,157.5 168.63,156C169.17,155 169.17,155 169.73,153.98C173.6,147.23 178.16,144.02 186,145Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\argument.xml:17: Warning: Very long vector path (1010 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#BEBEBE" android:pathData="M185,401.6C185.79,401.59 186.58,401.58 187.4,401.58C190,401.56 192.61,401.56 195.21,401.57C197.03,401.56 198.85,401.56 200.67,401.55C204.47,401.54 208.28,401.54 212.08,401.55C216.95,401.57 221.82,401.55 226.69,401.52C230.44,401.5 234.19,401.5 237.95,401.5C239.74,401.5 241.54,401.5 243.33,401.49C245.85,401.47 248.36,401.48 250.87,401.5C251.61,401.49 252.35,401.48 253.11,401.47C259.13,401.55 262.51,403.76 266.88,407.69C268.68,411.41 268.63,414.95 268,419C266.34,422.38 265.2,424.36 261.86,426.14C258.22,427.24 254.76,427.3 251,427.29C249.81,427.3 249.81,427.3 248.6,427.31C246,427.33 243.39,427.33 240.79,427.34C238.97,427.34 237.15,427.35 235.33,427.36C231.53,427.37 227.72,427.37 223.92,427.37C219.05,427.37 214.18,427.4 209.31,427.43C205.56,427.45 201.81,427.46 198.05,427.46C196.26,427.46 194.46,427.47 192.67,427.48C190.15,427.5 187.64,427.5 185.13,427.49C184.39,427.5 183.65,427.51 182.89,427.52C177.8,427.46 174.91,426.22 171,423C168.16,418.74 168.49,415 169,410C171.98,402.63 177.66,401.55 185,401.6Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\back.xml:7: Warning: Very long vector path (2297 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M344.44,79.91C349.78,83.05 353.68,87.18 356.31,92.81C358.02,100.75 357.31,107.05 353,114C349.18,118.5 344.99,122.59 340.77,126.72C339.5,127.98 338.24,129.23 336.97,130.49C334.26,133.19 331.54,135.88 328.81,138.57C324.49,142.82 320.2,147.08 315.9,151.35C306.01,161.19 296.09,171 286.17,180.8C277.08,189.8 267.99,198.8 258.92,207.81C254.65,212.06 250.37,216.28 246.09,220.5C243.42,223.13 240.76,225.78 238.11,228.42C236.88,229.64 235.64,230.86 234.4,232.07C223.78,242.27 223.78,242.27 219.5,256C220.29,267.3 228.64,274.3 236.23,281.72C237.5,282.98 238.76,284.23 240.03,285.49C242.74,288.19 245.46,290.88 248.19,293.57C252.51,297.82 256.8,302.08 261.1,306.35C270.22,315.42 279.36,324.46 288.5,333.5C298.37,343.26 308.24,353.03 318.08,362.81C322.35,367.06 326.63,371.28 330.91,375.5C333.58,378.13 336.24,380.78 338.89,383.42C340.12,384.64 341.36,385.86 342.6,387.07C356.79,400.96 356.79,400.96 357.44,411.56C357.04,418.51 355.28,423.17 350.06,427.88C345.31,431.81 342,433.3 335.75,433.38C334.67,433.4 333.59,433.43 332.48,433.46C324.66,432.43 319.37,427.96 313.98,422.49C313.33,421.84 312.67,421.19 311.99,420.52C309.8,418.34 307.63,416.16 305.45,413.97C303.88,412.4 302.31,410.84 300.73,409.27C296.47,405.03 292.22,400.77 287.98,396.52C283.53,392.06 279.07,387.61 274.62,383.16C266.66,375.21 258.71,367.25 250.77,359.29C243.57,352.07 236.36,344.87 229.15,337.67C220.76,329.3 212.37,320.93 204,312.54C199.57,308.11 195.14,303.67 190.7,299.25C186.53,295.09 182.37,290.92 178.22,286.74C176.69,285.21 175.16,283.68 173.63,282.16C171.54,280.08 169.46,277.99 167.38,275.89C166.77,275.29 166.16,274.69 165.54,274.07C160.36,268.8 157.57,263.67 157.56,256.06C157.53,254.63 157.53,254.63 157.5,253.16C158.7,245.53 163.15,240.26 168.51,234.98C169.16,234.33 169.81,233.67 170.48,232.99C172.66,230.8 174.84,228.63 177.03,226.45C178.6,224.88 180.16,223.31 181.73,221.73C185.97,217.47 190.23,213.22 194.48,208.98C198.94,204.53 203.39,200.07 207.84,195.62C215.79,187.66 223.75,179.71 231.71,171.77C238.93,164.57 246.13,157.36 253.33,150.15C261.7,141.76 270.07,133.37 278.46,125C282.89,120.57 287.33,116.14 291.75,111.7C295.91,107.53 300.08,103.37 304.26,99.22C305.79,97.69 307.32,96.16 308.84,94.63C310.92,92.54 313.01,90.46 315.11,88.38C315.71,87.77 316.31,87.16 316.93,86.54C324.35,79.25 334.28,76.11 344.44,79.91Z"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:3: Warning: Very long vector path (4948 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#E5EAF5" android:pathData="M198.06,-0.53C199.42,-0.53 200.78,-0.54 202.14,-0.54C204.98,-0.55 207.82,-0.54 210.65,-0.52C214.27,-0.5 217.89,-0.51 221.51,-0.54C224.31,-0.55 227.11,-0.55 229.92,-0.54C231.89,-0.53 233.87,-0.54 235.85,-0.56C243.99,-0.47 249.64,0.31 256.02,5.71C256.47,6.26 256.92,6.81 257.38,7.38C258.07,8.19 258.07,8.19 258.77,9.02C261.83,13.96 262.23,18.67 262.13,24.38C262.11,25.62 262.09,26.87 262.07,28.15C262.05,29.09 262.02,30.03 262,31C262.59,30.99 263.18,30.99 263.79,30.98C269.94,30.91 276.08,30.87 282.23,30.84C284.53,30.82 286.82,30.8 289.11,30.77C292.41,30.74 295.71,30.72 299.01,30.71C300.04,30.69 301.06,30.68 302.12,30.66C311.03,30.66 311.03,30.66 315,34C317.35,37.25 317.06,40.09 317,44C317.86,43.99 318.72,43.99 319.61,43.98C327.75,43.92 335.88,43.87 344.02,43.84C348.2,43.83 352.38,43.81 356.56,43.77C360.6,43.74 364.64,43.72 368.68,43.72C370.22,43.71 371.76,43.7 373.3,43.68C375.46,43.66 377.62,43.66 379.78,43.66C381.01,43.65 382.24,43.65 383.5,43.64C390.26,44.34 395.2,47.31 400,52C403.33,57.63 404.13,62.02 404.09,68.5C404.09,69.15 404.09,69.8 404.08,70.47C404.08,72.65 404.06,74.83 404.05,77.01C404.04,78.58 404.03,80.14 404.03,81.7C404.02,85.07 404,88.44 403.99,91.81C403.96,97.15 403.96,102.49 403.96,107.83C403.96,108.73 403.96,109.63 403.96,110.56C403.95,114.28 403.96,118.01 403.96,121.73C403.95,132.28 403.94,142.83 403.87,153.38C403.82,161.76 403.81,170.15 403.83,178.53C403.84,182.96 403.84,187.39 403.79,191.83C403.74,196 403.74,200.18 403.78,204.36C403.79,205.88 403.77,207.4 403.74,208.93C403.51,221.24 407.43,227.81 415,237C428.66,258.22 432.34,285.48 427.88,310.06C425.7,319.16 422.69,328.73 417.75,336.72C415.77,339.68 415.77,339.68 416.23,342.03C417.56,345.43 420.35,347.7 422.93,350.2C423.54,350.81 424.16,351.43 424.8,352.06C426.14,353.38 427.49,354.71 428.84,356.03C430.97,358.13 433.1,360.23 435.23,362.34C441.28,368.33 447.34,374.3 453.41,380.27C457.12,383.93 460.82,387.59 464.52,391.26C466.61,393.33 468.71,395.38 470.81,397.44C472.11,398.72 473.4,400 474.69,401.28C475.27,401.84 475.85,402.41 476.45,402.99C481.24,407.78 484.49,412.8 485.57,419.56C485.56,429.25 483.94,433.97 477.25,440.81C470.88,445.1 465.28,446.44 457.65,445.81C449.11,443.2 443.12,436.52 437.03,430.32C435.65,428.94 435.65,428.94 434.24,427.52C431.81,425.08 429.4,422.64 426.98,420.19C424.51,417.68 422.02,415.18 419.54,412.69C414.68,407.8 409.84,402.9 405,398C404.99,399.2 404.99,400.39 404.98,401.63C404.92,412.93 404.85,424.23 404.76,435.53C404.72,441.34 404.68,447.14 404.65,452.95C404.63,458.56 404.59,464.17 404.54,469.78C404.52,471.92 404.51,474.05 404.5,476.19C404.49,479.19 404.46,482.19 404.43,485.19C404.43,486.07 404.44,486.94 404.44,487.85C404.33,494.76 403.23,500.43 398.56,505.81C393.33,510.28 390.21,512.12 383.22,512.13C382,512.14 380.79,512.14 379.54,512.15C378.18,512.15 376.83,512.15 375.47,512.15C374.04,512.15 372.61,512.15 371.18,512.16C367.23,512.17 363.28,512.17 359.33,512.17C355.08,512.18 350.83,512.19 346.57,512.2C336.3,512.22 326.02,512.23 315.74,512.24C310.9,512.24 306.07,512.25 301.23,512.25C285.15,512.27 269.07,512.28 253,512.29C248.82,512.29 244.65,512.29 240.48,512.3C239.44,512.3 238.4,512.3 237.33,512.3C220.53,512.3 203.72,512.33 186.91,512.36C169.66,512.4 152.41,512.41 135.16,512.42C125.47,512.42 115.78,512.43 106.1,512.45C97.85,512.48 89.6,512.48 81.35,512.47C77.14,512.47 72.93,512.47 68.72,512.49C64.87,512.51 61.01,512.51 57.16,512.49C55.76,512.49 54.37,512.49 52.98,512.51C44.24,512.57 38.62,512.28 32.04,506.18C31.57,505.61 31.11,505.03 30.63,504.44C30.14,503.86 29.66,503.28 29.16,502.68C26.59,498.96 26.86,495.13 26.87,490.8C26.86,490.05 26.86,489.31 26.86,488.55C26.85,486.04 26.85,483.53 26.85,481.03C26.85,479.22 26.85,477.41 26.84,475.6C26.83,470.62 26.83,465.64 26.83,460.65C26.82,455.29 26.81,449.92 26.8,444.55C26.78,431.58 26.77,418.61 26.76,405.63C26.76,399.53 26.75,393.42 26.75,387.32C26.73,367.03 26.72,346.74 26.71,326.45C26.71,321.19 26.71,315.92 26.7,310.65C26.7,309.34 26.7,308.03 26.7,306.69C26.7,285.47 26.67,264.26 26.64,243.04C26.6,221.27 26.59,199.5 26.58,177.74C26.58,165.51 26.57,153.28 26.55,141.06C26.52,130.64 26.52,120.23 26.53,109.82C26.53,104.51 26.53,99.19 26.51,93.88C26.49,89.02 26.49,84.15 26.51,79.29C26.51,77.53 26.51,75.77 26.49,74.01C26.38,56.02 26.38,56.02 32.38,49.5C37.69,44.61 43.2,43.75 50.2,43.77C50.89,43.77 51.59,43.77 52.3,43.77C54.57,43.77 56.84,43.79 59.12,43.8C60.7,43.81 62.28,43.81 63.87,43.81C68.02,43.82 72.17,43.84 76.32,43.86C80.57,43.88 84.81,43.89 89.05,43.9C97.37,43.92 105.68,43.96 114,44C113.88,42.78 113.75,41.57 113.63,40.31C113.66,36.69 113.94,36.08 116.31,33.03C120.8,29.9 125.81,30.38 131.09,30.51C132.67,30.52 132.67,30.52 134.29,30.53C137.65,30.56 141.01,30.62 144.38,30.69C146.66,30.71 148.95,30.74 151.23,30.76C156.82,30.81 162.41,30.89 168,31C167.99,30.19 167.97,29.38 167.96,28.55C167.95,27.48 167.94,26.41 167.93,25.3C167.91,24.25 167.9,23.19 167.88,22.1C168.16,14.79 170.68,9.08 176,4C183.2,-0.66 189.64,-0.58 198.06,-0.53Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:5: Warning: Very long vector path (9478 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#E18086" android:pathData="M198.06,-0.53C199.42,-0.53 200.78,-0.54 202.14,-0.54C204.98,-0.55 207.82,-0.54 210.65,-0.52C214.27,-0.5 217.89,-0.51 221.51,-0.54C224.31,-0.55 227.11,-0.55 229.92,-0.54C231.89,-0.53 233.87,-0.54 235.85,-0.56C243.99,-0.47 249.64,0.31 256.02,5.71C256.47,6.26 256.92,6.81 257.38,7.38C258.07,8.19 258.07,8.19 258.77,9.02C261.83,13.96 262.23,18.67 262.13,24.38C262.11,25.62 262.09,26.87 262.07,28.15C262.05,29.09 262.02,30.03 262,31C262.59,30.99 263.18,30.99 263.79,30.98C269.94,30.91 276.08,30.87 282.23,30.84C284.53,30.82 286.82,30.8 289.11,30.77C292.41,30.74 295.71,30.72 299.01,30.71C300.04,30.69 301.06,30.68 302.12,30.66C311.03,30.66 311.03,30.66 315,34C317.35,37.25 317.06,40.09 317,44C317.86,43.99 318.72,43.99 319.61,43.98C327.75,43.92 335.88,43.87 344.02,43.84C348.2,43.83 352.38,43.81 356.56,43.77C360.6,43.74 364.64,43.72 368.68,43.72C370.22,43.71 371.76,43.7 373.3,43.68C375.46,43.66 377.62,43.66 379.78,43.66C381.01,43.65 382.24,43.65 383.5,43.64C390.26,44.34 395.2,47.31 400,52C403.33,57.63 404.13,62.02 404.09,68.5C404.09,69.15 404.09,69.8 404.08,70.47C404.08,72.65 404.06,74.83 404.05,77.01C404.04,78.58 404.03,80.14 404.03,81.7C404.02,85.07 404,88.44 403.99,91.81C403.96,97.15 403.96,102.49 403.96,107.83C403.96,108.73 403.96,109.63 403.96,110.56C403.95,114.28 403.96,118.01 403.96,121.73C403.95,132.28 403.94,142.83 403.87,153.38C403.82,161.76 403.81,170.15 403.83,178.53C403.84,182.96 403.84,187.39 403.79,191.83C403.74,196 403.74,200.18 403.78,204.36C403.79,205.88 403.77,207.4 403.74,208.93C403.51,221.24 407.43,227.81 415,237C428.66,258.22 432.34,285.48 427.88,310.06C425.7,319.16 422.69,328.73 417.75,336.72C415.77,339.68 415.77,339.68 416.23,342.03C417.56,345.43 420.35,347.7 422.93,350.2C423.54,350.81 424.16,351.43 424.8,352.06C426.14,353.38 427.49,354.71 428.84,356.03C430.97,358.13 433.1,360.23 435.23,362.34C441.28,368.33 447.34,374.3 453.41,380.27C457.12,383.93 460.82,387.59 464.52,391.26C466.61,393.33 468.71,395.38 470.81,397.44C472.11,398.72 473.4,400 474.69,401.28C475.27,401.84 475.85,402.41 476.45,402.99C481.24,407.78 484.49,412.8 485.57,419.56C485.56,429.25 483.94,433.97 477.25,440.81C470.88,445.1 465.28,446.44 457.65,445.81C449.11,443.2 443.12,436.52 437.03,430.32C435.65,428.94 435.65,428.94 434.24,427.52C431.81,425.08 429.4,422.64 426.98,420.19C424.51,417.68 422.02,415.18 419.54,412.69C414.68,407.8 409.84,402.9 405,398C404.99,399.2 404.99,400.39 404.98,401.63C404.92,412.93 404.85,424.23 404.76,435.53C404.72,441.34 404.68,447.14 404.65,452.95C404.63,458.56 404.59,464.17 404.54,469.78C404.52,471.92 404.51,474.05 404.5,476.19C404.49,479.19 404.46,482.19 404.43,485.19C404.43,486.07 404.44,486.94 404.44,487.85C404.33,494.76 403.23,500.43 398.56,505.81C393.33,510.28 390.21,512.12 383.22,512.13C382,512.14 380.79,512.14 379.54,512.15C378.18,512.15 376.83,512.15 375.47,512.15C374.04,512.15 372.61,512.15 371.18,512.16C367.23,512.17 363.28,512.17 359.33,512.17C355.08,512.18 350.83,512.19 346.57,512.2C336.3,512.22 326.02,512.23 315.74,512.24C310.9,512.24 306.07,512.25 301.23,512.25C285.15,512.27 269.07,512.28 253,512.29C248.82,512.29 244.65,512.29 240.48,512.3C239.44,512.3 238.4,512.3 237.33,512.3C220.53,512.3 203.72,512.33 186.91,512.36C169.66,512.4 152.41,512.41 135.16,512.42C125.47,512.42 115.78,512.43 106.1,512.45C97.85,512.48 89.6,512.48 81.35,512.47C77.14,512.47 72.93,512.47 68.72,512.49C64.87,512.51 61.01,512.51 57.16,512.49C55.76,512.49 54.37,512.49 52.98,512.51C44.24,512.57 38.62,512.28 32.04,506.18C31.57,505.61 31.11,505.03 30.63,504.44C30.14,503.86 29.66,503.28 29.16,502.68C26.59,498.96 26.86,495.13 26.87,490.8C26.86,490.05 26.86,489.31 26.86,488.55C26.85,486.04 26.85,483.53 26.85,481.03C26.85,479.22 26.85,477.41 26.84,475.6C26.83,470.62 26.83,465.64 26.83,460.65C26.82,455.29 26.81,449.92 26.8,444.55C26.78,431.58 26.77,418.61 26.76,405.63C26.76,399.53 26.75,393.42 26.75,387.32C26.73,367.03 26.72,346.74 26.71,326.45C26.71,321.19 26.71,315.92 26.7,310.65C26.7,309.34 26.7,308.03 26.7,306.69C26.7,285.47 26.67,264.26 26.64,243.04C26.6,221.27 26.59,199.5 26.58,177.74C26.58,165.51 26.57,153.28 26.55,141.06C26.52,130.64 26.52,120.23 26.53,109.82C26.53,104.51 26.53,99.19 26.51,93.88C26.49,89.02 26.49,84.15 26.51,79.29C26.51,77.53 26.51,75.77 26.49,74.01C26.38,56.02 26.38,56.02 32.38,49.5C37.69,44.61 43.2,43.75 50.2,43.77C50.89,43.77 51.59,43.77 52.3,43.77C54.57,43.77 56.84,43.79 59.12,43.8C60.7,43.81 62.28,43.81 63.87,43.81C68.02,43.82 72.17,43.84 76.32,43.86C80.57,43.88 84.81,43.89 89.05,43.9C97.37,43.92 105.68,43.96 114,44C113.88,42.78 113.75,41.57 113.63,40.31C113.66,36.69 113.94,36.08 116.31,33.03C120.8,29.9 125.81,30.38 131.09,30.51C132.67,30.52 132.67,30.52 134.29,30.53C137.65,30.56 141.01,30.62 144.38,30.69C146.66,30.71 148.95,30.74 151.23,30.76C156.82,30.81 162.41,30.89 168,31C167.99,30.19 167.97,29.38 167.96,28.55C167.95,27.48 167.94,26.41 167.93,25.3C167.91,24.25 167.9,23.19 167.88,22.1C168.16,14.79 170.68,9.08 176,4C183.2,-0.66 189.64,-0.58 198.06,-0.53ZM59.38,76.11C57.33,78.92 57.62,81.16 57.62,84.61C57.62,85.62 57.62,85.62 57.61,86.66C57.6,88.95 57.61,91.24 57.62,93.53C57.62,95.18 57.61,96.83 57.6,98.48C57.59,103.03 57.6,107.57 57.61,112.12C57.61,117.02 57.6,121.92 57.6,126.82C57.58,136.42 57.59,146.03 57.59,155.63C57.6,163.44 57.6,171.24 57.6,179.04C57.6,180.15 57.6,181.26 57.6,182.4C57.6,184.65 57.6,186.91 57.59,189.16C57.59,210.3 57.6,231.44 57.61,252.58C57.63,270.73 57.62,288.89 57.61,307.04C57.59,328.1 57.59,349.16 57.6,370.23C57.6,372.47 57.6,374.72 57.6,376.96C57.6,378.06 57.6,379.17 57.6,380.31C57.6,388.1 57.6,395.9 57.59,403.7C57.58,413.19 57.58,422.69 57.6,432.19C57.61,437.03 57.61,441.88 57.6,446.73C57.59,451.17 57.6,455.6 57.61,460.03C57.62,461.64 57.62,463.25 57.61,464.86C57.6,467.04 57.61,469.21 57.62,471.39C57.62,472.6 57.62,473.82 57.62,475.07C57.78,477.95 57.78,477.95 59.2,479.89C61.64,481.39 63.33,481.37 66.19,481.37C67.27,481.38 68.35,481.39 69.47,481.4C70.66,481.39 71.85,481.38 73.08,481.37C74.97,481.38 74.97,481.38 76.91,481.39C80.42,481.4 83.93,481.39 87.44,481.38C91.23,481.37 95.01,481.38 98.8,481.38C106.22,481.39 113.63,481.39 121.05,481.37C129.68,481.36 138.32,481.36 146.95,481.36C162.34,481.37 177.74,481.36 193.14,481.34C208.09,481.32 223.04,481.31 237.99,481.32C254.26,481.32 270.53,481.32 286.8,481.31C288.54,481.31 290.27,481.31 292,481.31C292.86,481.31 293.71,481.31 294.59,481.3C300.61,481.3 306.63,481.3 312.65,481.3C319.99,481.31 327.32,481.3 334.66,481.29C338.4,481.28 342.15,481.27 345.89,481.28C349.95,481.28 354,481.27 358.05,481.26C359.24,481.26 360.44,481.27 361.67,481.27C362.75,481.27 363.83,481.26 364.94,481.25C365.87,481.25 366.81,481.25 367.77,481.25C370.2,481.21 370.2,481.21 372,479C372.3,477.14 372.3,477.14 372.24,475C372.24,474.17 372.25,473.33 372.25,472.47C372.24,471.55 372.23,470.63 372.23,469.69C372.23,468.24 372.23,468.24 372.23,466.76C372.23,463.55 372.21,460.35 372.2,457.15C372.19,454.93 372.19,452.71 372.19,450.5C372.18,444.66 372.16,438.81 372.14,432.97C372.12,427.01 372.11,421.05 372.1,415.09C372.08,403.4 372.04,391.7 372,380C371.32,380.2 370.65,380.41 369.95,380.62C369.06,380.88 368.17,381.15 367.25,381.43C366.37,381.69 365.49,381.96 364.58,382.23C361.17,383.25 357.75,384.2 354.31,385.13C353.09,385.46 351.87,385.79 350.61,386.13C325.15,392.24 298.27,386.55 276.18,373.06C271.48,370.01 267.18,366.73 263,363C262.18,362.27 261.36,361.55 260.52,360.8C242.38,343.76 231.95,319.11 230.73,294.4C229.94,266.9 238.53,242.66 256.63,222C275.26,202.47 300.48,191.59 327.33,190.61C341.34,190.35 353.99,193.58 367.3,197.59C368.19,197.85 369.07,198.12 369.98,198.39C370.65,198.59 371.31,198.79 372,199C372.07,183.28 372.12,167.55 372.16,151.83C372.17,144.53 372.19,137.22 372.23,129.92C372.26,123.56 372.28,117.2 372.28,110.84C372.29,107.47 372.3,104.1 372.32,100.73C372.34,96.97 372.34,93.21 372.34,89.45C372.35,88.33 372.36,87.21 372.37,86.05C372.37,84.52 372.37,84.52 372.36,82.95C372.36,82.06 372.37,81.18 372.37,80.26C371.92,77.52 371.2,76.64 369,75C366.56,74.57 366.56,74.57 363.79,74.66C362.2,74.66 362.2,74.66 360.58,74.66C359.44,74.68 358.29,74.69 357.11,74.71C355.36,74.71 355.36,74.71 353.56,74.72C349.81,74.74 346.06,74.77 342.31,74.81C339.77,74.83 337.23,74.84 334.69,74.85C328.46,74.89 322.23,74.94 316,75C315.99,76.38 315.99,76.38 315.97,77.78C315.93,81.2 315.87,84.61 315.79,88.02C315.76,89.5 315.74,90.98 315.73,92.45C315.71,94.58 315.66,96.7 315.61,98.83C315.58,100.74 315.58,100.74 315.55,102.7C314.89,106.68 314.2,107.61 311,110C308.28,110.39 305.97,110.54 303.25,110.5C302.07,110.51 302.07,110.51 300.87,110.52C298.24,110.53 295.6,110.52 292.97,110.5C291.08,110.51 289.19,110.51 287.31,110.52C282.18,110.53 277.05,110.52 271.92,110.5C266.56,110.49 261.2,110.5 255.84,110.5C246.84,110.5 237.83,110.49 228.83,110.47C218.42,110.44 208,110.44 197.58,110.45C187.57,110.46 177.56,110.45 167.55,110.44C163.28,110.43 159.02,110.43 154.75,110.44C149.73,110.44 144.71,110.43 139.7,110.41C137.85,110.41 136,110.41 134.16,110.41C131.65,110.42 129.14,110.4 126.62,110.39C125.89,110.39 125.15,110.4 124.39,110.41C119.49,110.34 119.49,110.34 117.09,108.66C114.72,105.05 114.81,101.56 114.68,97.36C114.64,96.06 114.6,94.77 114.56,93.43C114.52,92.06 114.48,90.68 114.44,89.31C114.39,87.93 114.35,86.55 114.31,85.16C114.2,81.77 114.1,78.39 114,75C106.51,74.92 99.01,74.87 91.52,74.84C88.97,74.82 86.42,74.8 83.87,74.77C80.21,74.74 76.55,74.72 72.89,74.71C71.17,74.68 71.17,74.68 69.42,74.66C68.36,74.66 67.3,74.66 66.21,74.66C65.27,74.65 64.34,74.65 63.37,74.64C61.01,74.82 61.01,74.82 59.38,76.11Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:7: Warning: Very long vector path (4644 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#407093" android:pathData="M50.2,43.77C50.89,43.77 51.59,43.77 52.3,43.77C54.57,43.77 56.84,43.79 59.12,43.8C60.7,43.81 62.28,43.81 63.87,43.81C68.02,43.82 72.17,43.84 76.32,43.86C81.32,43.89 86.31,43.9 91.3,43.91C98.86,43.93 106.43,43.97 114,44C114,54.23 114,64.46 114,75C110.2,75.05 106.39,75.1 102.48,75.15C98.81,75.2 95.14,75.26 91.47,75.32C88.91,75.36 86.35,75.39 83.79,75.42C80.13,75.47 76.46,75.53 72.79,75.59C71.64,75.6 70.49,75.61 69.31,75.62C68.25,75.64 67.19,75.66 66.09,75.68C64.69,75.7 64.69,75.7 63.25,75.72C60.8,75.79 60.8,75.79 59,78C58.7,80.97 58.7,80.97 58.75,84.52C58.75,85.21 58.75,85.89 58.75,86.59C58.74,88.89 58.75,91.2 58.76,93.5C58.76,95.16 58.76,96.82 58.75,98.48C58.75,103.06 58.76,107.64 58.77,112.22C58.78,117.15 58.77,122.08 58.77,127.01C58.77,135.56 58.78,144.11 58.79,152.66C58.8,165.02 58.81,177.38 58.81,189.74C58.82,209.8 58.83,229.85 58.85,249.9C58.87,269.39 58.88,288.87 58.89,308.36C58.89,309.56 58.89,310.76 58.89,311.99C58.89,318.02 58.9,324.04 58.9,330.06C58.92,380.04 58.96,430.02 59,480C98.6,480.02 138.21,480.04 177.81,480.05C182.48,480.05 187.15,480.05 191.82,480.05C192.75,480.06 193.68,480.06 194.64,480.06C209.71,480.06 224.79,480.07 239.86,480.08C255.32,480.09 270.78,480.09 286.24,480.09C295.78,480.1 305.33,480.1 314.87,480.11C321.41,480.11 327.95,480.11 334.49,480.11C338.26,480.11 342.04,480.11 345.82,480.12C349.9,480.12 353.99,480.12 358.07,480.12C359.88,480.12 359.88,480.12 361.73,480.13C363.36,480.13 363.36,480.13 365.02,480.12C365.96,480.12 366.91,480.12 367.88,480.12C369.99,480.24 369.99,480.24 371,479C371.13,475.9 371.19,472.81 371.21,469.71C371.22,468.73 371.23,467.76 371.24,466.75C371.27,463.51 371.29,460.27 371.32,457.03C371.34,454.79 371.36,452.55 371.38,450.31C371.43,444.4 371.48,438.49 371.53,432.58C371.58,426.55 371.64,420.52 371.69,414.5C371.8,402.67 371.9,390.83 372,379C372.75,378.6 373.5,378.19 374.28,377.77C375.27,377.24 376.26,376.7 377.27,376.15C378.25,375.63 379.23,375.1 380.24,374.55C394.24,366.68 404.77,354.9 414,342C418.6,345.85 422.99,349.83 427.24,354.06C427.82,354.64 428.4,355.21 429,355.8C430.89,357.67 432.78,359.55 434.67,361.43C435.32,362.08 435.97,362.72 436.64,363.39C440.08,366.81 443.51,370.23 446.94,373.65C450.48,377.17 454.02,380.68 457.57,384.18C460.3,386.9 463.03,389.62 465.76,392.34C467.06,393.64 468.37,394.94 469.68,396.23C471.51,398.03 473.33,399.86 475.15,401.68C475.95,402.46 475.95,402.46 476.77,403.26C482.36,408.93 485.57,414.91 485.56,423C485.31,431.21 482.83,435.1 477.25,440.81C470.88,445.1 465.28,446.44 457.65,445.81C449.11,443.2 443.12,436.52 437.03,430.32C435.65,428.94 435.65,428.94 434.24,427.52C431.81,425.08 429.4,422.64 426.98,420.19C424.51,417.68 422.02,415.18 419.54,412.69C414.68,407.8 409.84,402.9 405,398C404.99,399.2 404.99,400.39 404.98,401.63C404.92,412.93 404.85,424.23 404.76,435.53C404.72,441.34 404.68,447.14 404.65,452.95C404.63,458.56 404.59,464.17 404.54,469.78C404.52,471.92 404.51,474.05 404.5,476.19C404.49,479.19 404.46,482.19 404.43,485.19C404.43,486.07 404.44,486.94 404.44,487.85C404.33,494.76 403.23,500.43 398.56,505.81C393.33,510.28 390.21,512.12 383.22,512.13C381.39,512.14 381.39,512.14 379.54,512.15C378.18,512.15 376.83,512.15 375.47,512.15C374.04,512.15 372.61,512.15 371.18,512.16C367.23,512.17 363.28,512.17 359.33,512.17C355.08,512.18 350.83,512.19 346.57,512.2C336.3,512.22 326.02,512.23 315.74,512.24C310.9,512.24 306.07,512.25 301.23,512.25C285.15,512.27 269.07,512.28 253,512.29C248.82,512.29 244.65,512.29 240.48,512.3C239.44,512.3 238.4,512.3 237.33,512.3C220.53,512.3 203.72,512.33 186.91,512.36C169.66,512.4 152.41,512.41 135.16,512.42C125.47,512.42 115.78,512.43 106.1,512.45C97.85,512.48 89.6,512.48 81.35,512.47C77.14,512.47 72.93,512.47 68.72,512.49C64.87,512.51 61.01,512.51 57.16,512.49C55.76,512.49 54.37,512.49 52.98,512.51C44.24,512.57 38.62,512.28 32.04,506.18C31.57,505.61 31.11,505.03 30.63,504.44C30.14,503.86 29.66,503.28 29.16,502.68C26.59,498.96 26.86,495.13 26.87,490.8C26.86,490.05 26.86,489.31 26.86,488.55C26.85,486.04 26.85,483.53 26.85,481.03C26.85,479.22 26.85,477.41 26.84,475.6C26.83,470.62 26.83,465.64 26.83,460.65C26.82,455.29 26.81,449.92 26.8,444.55C26.78,431.58 26.77,418.61 26.76,405.63C26.76,399.53 26.75,393.42 26.75,387.32C26.73,367.03 26.72,346.74 26.71,326.45C26.71,321.19 26.71,315.92 26.7,310.65C26.7,309.34 26.7,308.03 26.7,306.69C26.7,285.47 26.67,264.26 26.64,243.04C26.6,221.27 26.59,199.5 26.58,177.74C26.58,165.51 26.57,153.28 26.55,141.06C26.52,130.64 26.52,120.23 26.53,109.82C26.53,104.51 26.53,99.19 26.51,93.88C26.49,89.02 26.49,84.15 26.51,79.29C26.51,77.53 26.51,75.77 26.49,74.01C26.38,56.02 26.38,56.02 32.38,49.5C37.69,44.61 43.2,43.75 50.2,43.77Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:9: Warning: Very long vector path (2448 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#EBBD6A" android:pathData="M198.06,-0.53C199.42,-0.53 200.78,-0.54 202.14,-0.54C204.98,-0.55 207.82,-0.54 210.65,-0.52C214.27,-0.5 217.89,-0.51 221.51,-0.54C224.31,-0.55 227.11,-0.55 229.92,-0.54C231.89,-0.53 233.87,-0.54 235.85,-0.56C243.99,-0.47 249.64,0.31 256.02,5.71C256.47,6.26 256.92,6.81 257.38,7.38C258.07,8.19 258.07,8.19 258.77,9.02C261.83,13.96 262.23,18.67 262.13,24.38C262.11,25.62 262.09,26.87 262.07,28.15C262.05,29.09 262.02,30.03 262,31C262.59,30.99 263.18,30.99 263.79,30.98C269.94,30.91 276.08,30.87 282.23,30.84C284.53,30.82 286.82,30.8 289.11,30.77C292.41,30.74 295.71,30.72 299.01,30.71C300.04,30.69 301.06,30.68 302.12,30.66C311.26,30.66 311.26,30.66 315,34C317.29,37.5 317.24,40.5 317.21,44.62C317.21,45.27 317.21,45.93 317.21,46.6C317.2,48.77 317.18,50.93 317.15,53.09C317.14,54.6 317.13,56.1 317.11,57.61C317.08,60.77 317.04,63.92 317,67.08C316.94,71.12 316.92,75.16 316.91,79.2C316.89,82.31 316.85,85.42 316.8,88.53C316.79,90.02 316.78,91.51 316.77,93C316.77,95.09 316.73,97.17 316.69,99.25C316.68,100.44 316.67,101.62 316.65,102.84C315.78,107.04 314.34,108.35 311,111C308.18,111.49 305.95,111.67 303.14,111.63C302.35,111.64 301.57,111.64 300.76,111.65C298.13,111.67 295.5,111.65 292.87,111.63C290.98,111.64 289.1,111.64 287.21,111.65C282.09,111.67 276.97,111.66 271.85,111.64C266.5,111.62 261.14,111.63 255.79,111.64C246.8,111.64 237.8,111.62 228.81,111.6C218.41,111.56 208.01,111.56 197.61,111.58C187.61,111.6 177.6,111.59 167.6,111.57C163.35,111.57 159.09,111.57 154.83,111.58C149.81,111.58 144.8,111.57 139.79,111.54C137.95,111.54 136.11,111.54 134.27,111.54C131.76,111.55 129.25,111.54 126.74,111.51C125.64,111.53 125.64,111.53 124.52,111.54C121.13,111.48 119.31,111.24 116.58,109.16C113.79,105.35 113.85,102.08 113.79,97.5C113.78,96.84 113.77,96.19 113.76,95.51C113.73,93.34 113.71,91.18 113.69,89.01C113.68,88.27 113.68,87.53 113.67,86.76C113.64,82.84 113.61,78.92 113.59,75C113.58,71.75 113.54,68.51 113.49,65.27C113.43,61.35 113.4,57.43 113.39,53.51C113.38,52.01 113.36,50.52 113.34,49.03C113.13,37.8 113.13,37.8 114.99,33.96C117.84,31.05 121.23,30.69 125.09,30.43C127.09,30.43 129.09,30.46 131.09,30.51C132.67,30.52 132.67,30.52 134.29,30.53C137.65,30.56 141.01,30.62 144.38,30.69C146.66,30.71 148.95,30.74 151.23,30.76C156.82,30.81 162.41,30.89 168,31C167.98,29.79 167.98,29.79 167.96,28.55C167.95,27.48 167.94,26.41 167.93,25.3C167.91,23.72 167.91,23.72 167.88,22.1C168.16,14.79 170.68,9.08 176,4C183.2,-0.66 189.64,-0.58 198.06,-0.53Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:13: Warning: Very long vector path (1663 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#375F7E" android:pathData="M316,44C333.44,43.88 333.44,43.88 340.9,43.85C345.98,43.84 351.06,43.81 356.14,43.77C360.24,43.74 364.33,43.72 368.43,43.72C369.99,43.71 371.55,43.7 373.11,43.68C375.3,43.66 377.5,43.66 379.69,43.66C380.93,43.65 382.18,43.65 383.46,43.64C390.24,44.33 395.18,47.29 400,52C403.35,57.66 404.13,62.08 404.12,68.59C404.12,69.25 404.12,69.91 404.12,70.6C404.12,72.81 404.12,75.02 404.11,77.23C404.11,78.82 404.11,80.41 404.11,81.99C404.11,86.3 404.11,90.61 404.1,94.92C404.1,99.42 404.09,103.92 404.09,108.42C404.09,116.95 404.08,125.47 404.07,133.99C404.06,143.7 404.06,153.4 404.05,163.11C404.04,183.07 404.02,203.04 404,223C402.89,222.05 401.77,221.09 400.66,220.14C399.73,219.35 399.73,219.35 398.78,218.53C397.32,217.27 395.86,216 394.4,214.73C393.18,213.69 393.18,213.69 391.94,212.63C390.62,211.5 390.62,211.5 389.27,210.34C386.89,208.48 385.4,207.79 382.41,206.76C378.07,205.19 374.76,203.8 372,200C369.45,191.94 370.24,182.96 370.44,174.62C370.51,171.23 370.5,167.84 370.49,164.44C370.49,158.03 370.56,151.62 370.66,145.2C370.8,135.77 370.83,126.34 370.86,116.91C370.87,115.52 370.87,114.13 370.88,112.74C370.88,111.72 370.88,111.72 370.89,110.68C370.91,106 370.96,101.31 371.02,96.62C371.04,94.76 371.04,94.76 371.06,92.87C371.08,90.55 371.12,88.22 371.16,85.9C371.17,84.38 371.17,84.38 371.18,82.82C371.2,81.49 371.2,81.49 371.22,80.13C371.2,77.78 371.2,77.78 369,76C366.66,75.67 366.66,75.67 363.91,75.68C362.85,75.66 361.79,75.64 360.69,75.62C358.97,75.6 358.97,75.6 357.21,75.59C356.04,75.57 354.86,75.55 353.65,75.53C349.89,75.47 346.14,75.42 342.38,75.38C339.83,75.34 337.29,75.3 334.74,75.26C328.49,75.16 322.25,75.08 316,75C316,64.77 316,54.54 316,44Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:15: Warning: Very long vector path (1027 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#9CC5FA" android:pathData="M414,342C418.6,345.85 422.99,349.83 427.24,354.06C427.82,354.64 428.4,355.21 429,355.8C430.89,357.67 432.78,359.55 434.67,361.43C435.32,362.08 435.97,362.72 436.64,363.39C440.08,366.81 443.51,370.23 446.94,373.65C450.48,377.17 454.02,380.68 457.57,384.18C460.3,386.9 463.03,389.62 465.76,392.34C467.06,393.64 468.37,394.94 469.68,396.23C471.51,398.03 473.33,399.86 475.15,401.68C475.68,402.2 476.22,402.72 476.77,403.26C482.36,408.93 485.57,414.91 485.56,423C485.31,431.21 482.83,435.1 477.25,440.81C470.88,445.1 465.28,446.44 457.65,445.81C449.04,443.18 442.99,436.4 436.84,430.17C435.88,429.21 434.92,428.25 433.96,427.29C431.97,425.28 429.97,423.28 427.98,421.26C425.45,418.7 422.91,416.16 420.37,413.61C417.92,411.15 415.47,408.69 413.03,406.23C412.13,405.33 411.22,404.42 410.3,403.49C405.89,399.03 401.59,394.52 397.52,389.74C394.85,386.68 391.96,383.85 389.06,381C388.47,380.42 387.89,379.83 387.28,379.23C385.86,377.82 384.43,376.41 383,375C384.26,371.21 385.66,370.64 388.94,368.44C399.22,361.13 406.75,352.19 414,342Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:17: Warning: Very long vector path (1380 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#375F7E" android:pathData="M383.17,374.74C385.46,376.32 387.33,378.09 389.29,380.06C390.49,381.26 390.49,381.26 391.7,382.48C392.52,383.31 393.34,384.14 394.19,385C395.02,385.83 395.85,386.67 396.71,387.52C402.9,393.76 404.15,396.32 404.16,405.19C404.17,406.62 404.17,406.62 404.18,408.08C404.19,411.24 404.2,414.41 404.2,417.57C404.21,419.77 404.21,421.97 404.22,424.16C404.23,428.78 404.24,433.39 404.24,438.01C404.25,443.91 404.27,449.82 404.3,455.72C404.32,460.27 404.32,464.81 404.32,469.35C404.33,471.53 404.33,473.7 404.35,475.88C404.36,478.93 404.36,481.98 404.36,485.04C404.37,486.38 404.37,486.38 404.38,487.74C404.34,494.71 403.27,500.38 398.56,505.81C392.83,510.71 389.27,512.15 381.71,512.1C380.65,512.1 380.65,512.1 379.57,512.09C377.32,512.09 375.06,512.08 372.81,512.06C371.28,512.06 369.76,512.05 368.23,512.05C364.49,512.04 360.74,512.02 357,512C359,510 359,510 362.38,508.25C367.04,505.65 369.32,502.03 371,497C371.1,495.39 371.15,493.77 371.16,492.16C371.17,491.16 371.18,490.17 371.19,489.14C371.19,488.05 371.2,486.95 371.21,485.83C371.22,484.68 371.23,483.52 371.24,482.34C371.27,478.52 371.29,474.71 371.32,470.89C371.34,468.25 371.36,465.61 371.38,462.97C371.43,456.72 371.47,450.48 371.52,444.23C371.57,437.12 371.62,430 371.68,422.89C371.79,408.26 371.9,393.63 372,379C373.4,378.11 374.82,377.24 376.25,376.38C377.04,375.89 377.83,375.4 378.64,374.9C381,374 381,374 383.17,374.74Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:19: Warning: Very long vector path (935 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#DC636E" android:pathData="M327,191C353.78,189.74 376.17,197.73 396.31,215.63C397.88,217.08 399.44,218.53 401,220C401.87,220.8 402.73,221.61 403.63,222.44C420.62,240.77 429.45,264.26 429.31,289.25C429.31,290.35 429.3,291.45 429.3,292.58C428.9,319.39 417.58,341.99 399.25,361.19C385.47,374.43 369.53,382.57 351,387C350.1,387.23 349.2,387.45 348.28,387.68C339.93,389.51 331.5,389.18 323,389C323.49,388.01 323.49,388.01 324,387C326.02,386.51 328.07,386.11 330.13,385.75C346.62,382.41 361.2,374.88 374,364C374.98,363.23 375.95,362.45 376.96,361.66C392.94,348.31 404.22,325.4 406.76,304.91C407.1,299.4 407.21,293.9 407.25,288.38C407.26,286.8 407.26,286.8 407.28,285.2C407.24,275.28 405.73,266.02 402.38,256.69C402.12,255.97 401.86,255.25 401.59,254.51C397.16,242.64 390.51,231.14 381.27,222.36C380,221 380,221 380,219C379.13,218.62 379.13,218.62 378.24,218.23C375.78,216.88 374.13,215.34 372.13,213.38C359.5,201.96 343.18,196.28 327,192C327,191.67 327,191.34 327,191Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:21: Warning: Very long vector path (1373 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#78C2A4" android:pathData="M111.03,146.49C111.96,146.49 112.88,146.48 113.83,146.47C116.93,146.46 120.04,146.47 123.14,146.48C125.36,146.48 127.58,146.47 129.8,146.46C135.84,146.44 141.87,146.45 147.91,146.46C154.22,146.47 160.53,146.46 166.84,146.46C178.12,146.45 189.4,146.46 200.68,146.48C210.91,146.5 221.13,146.5 231.36,146.48C243.24,146.46 255.11,146.45 266.98,146.46C273.27,146.47 279.56,146.47 285.85,146.45C291.75,146.44 297.66,146.45 303.57,146.47C305.75,146.48 307.92,146.48 310.09,146.47C313.05,146.46 316.01,146.47 318.97,146.49C319.83,146.48 320.7,146.47 321.59,146.47C325.4,146.52 327.37,146.55 330.53,148.81C332.69,152.03 332.54,154.22 332,158C330.37,160.43 329.09,161.79 326.15,162.36C323.75,162.52 321.37,162.53 318.97,162.51C318.04,162.51 317.12,162.52 316.17,162.53C313.07,162.54 309.96,162.53 306.86,162.52C304.64,162.52 302.42,162.53 300.2,162.54C294.16,162.56 288.13,162.55 282.09,162.54C275.78,162.53 269.47,162.54 263.16,162.54C251.88,162.55 240.6,162.54 229.32,162.52C219.09,162.5 208.87,162.5 198.64,162.52C186.76,162.54 174.89,162.55 163.02,162.54C156.73,162.53 150.44,162.53 144.15,162.55C138.25,162.56 132.34,162.55 126.43,162.53C124.25,162.52 122.08,162.52 119.91,162.53C116.95,162.54 113.99,162.53 111.03,162.51C110.17,162.52 109.3,162.53 108.41,162.53C104.6,162.48 102.63,162.45 99.47,160.19C97.31,156.97 97.46,154.78 98,151C101.39,145.94 105.47,146.43 111.03,146.49Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:27: Warning: Very long vector path (1359 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#78C2A4" android:pathData="M108.64,409.49C109.31,409.49 109.97,409.48 110.66,409.47C112.89,409.46 115.12,409.47 117.35,409.48C118.95,409.48 120.55,409.47 122.15,409.46C126.5,409.44 130.84,409.45 135.19,409.46C139.73,409.47 144.28,409.46 148.82,409.46C156.46,409.45 164.09,409.46 171.72,409.48C180.55,409.5 189.38,409.49 198.21,409.47C205.78,409.45 213.36,409.45 220.93,409.46C225.46,409.47 229.99,409.47 234.51,409.45C238.77,409.44 243.02,409.45 247.28,409.47C248.84,409.48 250.4,409.48 251.96,409.47C254.1,409.46 256.23,409.47 258.36,409.49C260.15,409.49 260.15,409.49 261.97,409.5C265.62,410.1 266.8,411.08 269,414C269.56,416.94 269.56,416.94 269,420C266.44,423.21 264.94,424.81 260.87,425.63C259.68,425.63 258.5,425.63 257.28,425.63C256.61,425.64 255.95,425.64 255.27,425.65C253.05,425.67 250.84,425.65 248.62,425.63C247.03,425.64 245.44,425.64 243.85,425.65C239.54,425.67 235.23,425.66 230.91,425.64C226.4,425.62 221.88,425.63 217.37,425.64C209.79,425.64 202.21,425.62 194.63,425.6C185.86,425.56 177.1,425.56 168.33,425.58C159.9,425.6 151.47,425.59 143.04,425.57C139.45,425.57 135.86,425.57 132.27,425.58C128.05,425.58 123.82,425.57 119.6,425.54C118.05,425.54 116.5,425.54 114.94,425.54C112.83,425.55 110.71,425.54 108.6,425.51C107.41,425.51 106.23,425.51 105.01,425.51C101.37,424.89 100.18,423.94 98,421C97.45,417.14 97.26,415 99.62,411.81C102.78,409.41 104.69,409.5 108.64,409.49Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:29: Warning: Very long vector path (1253 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#EAB14D" android:pathData="M282,31C286.56,30.91 291.12,30.86 295.69,30.81C296.98,30.79 298.27,30.76 299.61,30.74C300.85,30.73 302.1,30.72 303.39,30.71C304.53,30.69 305.68,30.68 306.86,30.66C310.4,31.04 312.21,31.81 315,34C317.31,37.48 317.24,40.51 317.21,44.62C317.21,45.27 317.21,45.93 317.21,46.6C317.2,48.77 317.18,50.93 317.15,53.09C317.14,54.6 317.13,56.1 317.11,57.61C317.08,60.77 317.04,63.92 317,67.08C316.94,71.12 316.92,75.16 316.91,79.2C316.89,82.31 316.85,85.42 316.8,88.53C316.79,90.02 316.78,91.51 316.77,93C316.77,95.09 316.73,97.17 316.69,99.25C316.68,100.44 316.67,101.62 316.65,102.84C315.81,106.92 314.21,108.43 311,111C307.95,111.57 307.95,111.57 304.61,111.49C303.4,111.47 302.2,111.46 300.96,111.44C299.72,111.4 298.47,111.36 297.19,111.31C295.92,111.29 294.66,111.27 293.35,111.24C290.23,111.19 287.12,111.1 284,111C284.81,110.32 285.63,109.64 286.47,108.94C290.81,103.68 289.65,96.36 289.56,89.9C289.55,88.41 289.55,86.93 289.55,85.45C289.55,82.34 289.52,79.24 289.49,76.14C289.44,72.17 289.43,68.2 289.42,64.22C289.42,61.16 289.4,58.1 289.38,55.04C289.38,53.58 289.37,52.11 289.36,50.65C289.36,48.6 289.33,46.56 289.31,44.51C289.3,43.35 289.29,42.18 289.27,40.98C289.01,38.07 288.64,36.4 287,34C284.46,32.72 284.46,32.72 282,32C282,31.67 282,31.34 282,31Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:31: Warning: Very long vector path (1104 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#81B4FA" android:pathData="M414,342C418.6,345.85 422.99,349.83 427.24,354.06C427.82,354.64 428.4,355.21 429,355.8C430.89,357.67 432.78,359.55 434.67,361.43C435.64,362.4 435.64,362.4 436.64,363.39C440.08,366.81 443.51,370.23 446.94,373.65C450.48,377.17 454.02,380.68 457.57,384.18C460.3,386.9 463.03,389.62 465.76,392.34C467.06,393.64 468.37,394.94 469.68,396.23C471.51,398.03 473.33,399.86 475.15,401.68C475.68,402.2 476.22,402.72 476.77,403.26C482.36,408.93 485.57,414.91 485.56,423C485.31,431.21 482.83,435.1 477.25,440.81C470.59,445.3 465.01,446.47 457.07,445.42C455,445 455,445 453,444C454,441 454,441 456.88,438.75C460.23,436.01 462.1,433.26 463,429C463.76,420.58 463.5,414.45 458.12,407.6C452.33,401.33 446.35,395.29 440.24,389.32C438.73,387.83 437.22,386.34 435.71,384.85C432.04,381.22 428.35,377.59 424.66,373.98C422.06,371.44 419.48,368.89 416.9,366.34C415.92,365.37 414.94,364.41 413.95,363.45C412.58,362.12 411.23,360.77 409.87,359.43C409.1,358.67 408.33,357.91 407.53,357.12C406,355 406,355 406.16,352.87C407.29,350.35 408.82,348.4 410.56,346.25C411.2,345.45 411.84,344.65 412.5,343.83C413,343.22 413.49,342.62 414,342Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:35: Warning: Very long vector path (1044 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#7BC3A6" android:pathData="M111.93,277.47C113.27,277.45 113.27,277.45 114.63,277.44C117.57,277.42 120.5,277.42 123.44,277.43C125.48,277.43 127.52,277.42 129.57,277.41C133.85,277.4 138.13,277.41 142.41,277.42C147.89,277.44 153.38,277.42 158.86,277.38C163.08,277.36 167.3,277.36 171.52,277.37C173.54,277.37 175.56,277.36 177.59,277.35C180.41,277.33 183.24,277.35 186.07,277.37C186.9,277.36 187.74,277.35 188.6,277.33C192.88,277.4 194.52,277.63 198.02,280.35C200,283 200,283 200.63,286C199.77,290.13 198.31,291.42 195,294C192.02,294.72 189.12,294.68 186.07,294.65C185.18,294.66 184.29,294.66 183.37,294.67C180.43,294.7 177.5,294.68 174.56,294.66C172.52,294.67 170.48,294.67 168.43,294.68C164.15,294.69 159.87,294.68 155.59,294.65C150.11,294.62 144.62,294.64 139.14,294.67C134.92,294.69 130.7,294.68 126.48,294.67C124.46,294.67 122.44,294.67 120.41,294.68C117.59,294.69 114.76,294.67 111.93,294.65C110.68,294.66 110.68,294.66 109.4,294.68C105.15,294.6 103.47,294.36 99.98,291.68C98,289 98,289 97.38,285.44C98,282 98,282 99.98,279.82C104.07,277.36 107.25,277.43 111.93,277.47Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:37: Warning: Very long vector path (1036 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#78C2A4" android:pathData="M111.02,212.48C111.93,212.48 112.84,212.47 113.78,212.46C116.79,212.44 119.8,212.46 122.81,212.47C124.9,212.47 127,212.46 129.09,212.46C133.47,212.45 137.86,212.46 142.24,212.48C147.87,212.5 153.49,212.49 159.11,212.46C163.43,212.45 167.75,212.45 172.07,212.46C174.14,212.47 176.22,212.46 178.29,212.45C181.19,212.44 184.08,212.46 186.98,212.48C187.84,212.47 188.7,212.47 189.58,212.46C195.37,212.54 195.37,212.54 198.53,214.85C200.57,217.84 200.65,219.45 200,223C198.01,225.67 196.41,227.66 193.07,228.46C190.68,228.64 188.35,228.66 185.96,228.63C185.06,228.64 184.16,228.65 183.23,228.65C180.25,228.67 177.28,228.65 174.31,228.63C172.24,228.63 170.17,228.63 168.1,228.64C163.77,228.64 159.44,228.62 155.11,228.6C149.55,228.56 144,228.57 138.45,228.59C134.18,228.6 129.91,228.59 125.64,228.57C123.59,228.57 121.54,228.57 119.49,228.58C116.63,228.58 113.77,228.56 110.91,228.53C110.06,228.54 109.22,228.55 108.34,228.55C104.56,228.49 102.61,228.44 99.47,226.19C97.31,222.97 97.46,220.79 98,217C101.39,211.95 105.47,212.43 111.02,212.48Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml:39: Warning: Very long vector path (1021 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#78C2A4" android:pathData="M110.91,343.48C111.81,343.48 112.71,343.47 113.64,343.46C116.61,343.44 119.58,343.46 122.56,343.47C124.63,343.47 126.69,343.46 128.76,343.46C133.09,343.45 137.43,343.46 141.76,343.48C147.31,343.5 152.87,343.49 158.42,343.46C162.69,343.45 166.96,343.45 171.23,343.46C173.28,343.47 175.32,343.46 177.37,343.45C180.23,343.44 183.1,343.46 185.96,343.48C187.23,343.47 187.23,343.47 188.52,343.46C192.49,343.51 194.55,343.73 198.03,345.81C200,348 200,348 200.56,351.5C200,355 200,355 198.53,357.19C194.78,359.87 191.49,359.56 186.98,359.52C186.07,359.52 185.16,359.53 184.22,359.54C181.21,359.56 178.2,359.54 175.19,359.53C173.1,359.53 171,359.54 168.91,359.54C164.53,359.55 160.14,359.54 155.76,359.52C150.13,359.5 144.51,359.51 138.89,359.54C134.57,359.55 130.25,359.55 125.93,359.54C123.86,359.53 121.78,359.54 119.71,359.55C116.81,359.56 113.92,359.54 111.02,359.52C110.16,359.53 109.3,359.53 108.42,359.54C104.61,359.49 102.63,359.45 99.47,357.19C97.31,353.97 97.46,351.78 98,348C101.39,342.98 105.38,343.43 110.91,343.48Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\dispute_.xml:3: Warning: Very long vector path (4473 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#000000" android:pathData="M172.69,-0.52C173.43,-0.53 174.18,-0.54 174.94,-0.54C177.41,-0.57 179.89,-0.56 182.36,-0.55C184.14,-0.56 185.92,-0.57 187.7,-0.58C192.52,-0.61 197.34,-0.61 202.16,-0.6C206.2,-0.6 210.23,-0.61 214.26,-0.62C223.77,-0.64 233.28,-0.64 242.79,-0.63C252.59,-0.62 262.4,-0.64 272.2,-0.69C280.62,-0.72 289.05,-0.73 297.47,-0.73C302.5,-0.72 307.53,-0.73 312.56,-0.76C317.29,-0.78 322.02,-0.78 326.75,-0.75C328.48,-0.75 330.21,-0.75 331.94,-0.77C341.42,-0.85 348.81,-0.87 356,6C356.5,6.46 356.99,6.92 357.5,7.4C362.08,11.67 366.5,16.09 370.91,20.52C372.4,22.01 373.9,23.5 375.39,24.98C379.42,29.01 383.45,33.04 387.47,37.07C391.69,41.3 395.92,45.52 400.14,49.74C407.24,56.82 414.32,63.91 421.4,71.01C429.58,79.21 437.77,87.39 445.97,95.58C453.02,102.61 460.06,109.65 467.1,116.69C471.3,120.9 475.5,125.1 479.7,129.29C483.65,133.24 487.6,137.19 491.54,141.14C492.98,142.59 494.43,144.04 495.88,145.49C497.87,147.46 499.84,149.44 501.81,151.43C502.38,152 502.96,152.56 503.55,153.15C508.59,158.26 512.41,163.09 512.54,170.63C512.54,171.31 512.53,171.99 512.52,172.69C512.53,173.43 512.54,174.18 512.54,174.94C512.57,177.41 512.56,179.89 512.55,182.36C512.56,184.14 512.57,185.92 512.58,187.7C512.61,192.52 512.61,197.34 512.6,202.16C512.6,206.2 512.61,210.23 512.62,214.26C512.64,223.77 512.64,233.28 512.63,242.79C512.62,252.59 512.64,262.4 512.69,272.2C512.72,280.62 512.73,289.05 512.73,297.47C512.72,302.5 512.73,307.53 512.76,312.56C512.78,317.29 512.78,322.02 512.75,326.75C512.75,328.48 512.75,330.21 512.77,331.94C512.85,341.42 512.87,348.81 506,356C505.31,356.74 505.31,356.74 504.6,357.5C500.33,362.08 495.91,366.5 491.48,370.91C489.99,372.4 488.5,373.9 487.02,375.39C482.99,379.42 478.96,383.45 474.93,387.47C470.7,391.69 466.48,395.92 462.26,400.14C455.18,407.24 448.09,414.32 440.99,421.4C432.79,429.58 424.61,437.77 416.42,445.97C409.39,453.02 402.35,460.06 395.31,467.1C391.1,471.3 386.9,475.5 382.71,479.7C378.76,483.65 374.81,487.6 370.86,491.54C369.41,492.98 367.96,494.43 366.51,495.88C364.54,497.87 362.56,499.84 360.57,501.81C359.72,502.67 359.72,502.67 358.85,503.55C353.74,508.59 348.91,512.41 341.37,512.54C340.69,512.54 340.01,512.53 339.31,512.52C338.57,512.53 337.82,512.54 337.06,512.54C334.59,512.57 332.11,512.56 329.64,512.55C327.86,512.56 326.08,512.57 324.3,512.58C319.48,512.61 314.66,512.61 309.84,512.6C305.8,512.6 301.77,512.61 297.74,512.62C288.23,512.64 278.72,512.64 269.21,512.63C259.41,512.62 249.6,512.64 239.8,512.69C231.38,512.72 222.95,512.73 214.53,512.73C209.5,512.72 204.47,512.73 199.44,512.76C194.71,512.78 189.98,512.78 185.25,512.75C183.52,512.75 181.79,512.75 180.06,512.77C170.58,512.85 163.19,512.87 156,506C155.5,505.54 155.01,505.08 154.5,504.6C149.92,500.33 145.5,495.91 141.09,491.48C139.6,489.99 138.1,488.5 136.61,487.02C132.58,482.99 128.55,478.96 124.53,474.93C120.31,470.7 116.08,466.48 111.86,462.26C104.76,455.18 97.68,448.09 90.6,440.99C82.42,432.79 74.23,424.61 66.03,416.42C58.98,409.39 51.94,402.35 44.9,395.31C40.7,391.1 36.5,386.9 32.3,382.71C28.35,378.76 24.4,374.81 20.46,370.86C19.02,369.41 17.57,367.96 16.12,366.51C14.13,364.54 12.16,362.56 10.19,360.57C9.62,360 9.04,359.44 8.45,358.85C3.41,353.74 -0.41,348.91 -0.54,341.37C-0.54,340.69 -0.53,340.01 -0.52,339.31C-0.53,338.57 -0.54,337.82 -0.54,337.06C-0.57,334.59 -0.56,332.11 -0.55,329.64C-0.56,327.86 -0.57,326.08 -0.58,324.3C-0.61,319.48 -0.61,314.66 -0.6,309.84C-0.6,305.8 -0.61,301.77 -0.62,297.74C-0.64,288.23 -0.64,278.72 -0.63,269.21C-0.62,259.41 -0.64,249.6 -0.69,239.8C-0.72,231.38 -0.73,222.95 -0.73,214.53C-0.72,209.5 -0.73,204.47 -0.76,199.44C-0.78,194.71 -0.78,189.98 -0.75,185.25C-0.75,183.52 -0.75,181.79 -0.77,180.06C-0.85,170.58 -0.87,163.19 6,156C6.46,155.5 6.92,155.01 7.4,154.5C11.67,149.92 16.09,145.5 20.52,141.09C22.01,139.6 23.5,138.1 24.98,136.61C29.01,132.58 33.04,128.55 37.07,124.53C41.3,120.31 45.52,116.08 49.74,111.86C56.82,104.76 63.91,97.68 71.01,90.6C79.21,82.42 87.39,74.23 95.58,66.03C102.61,58.98 109.65,51.94 116.69,44.9C120.9,40.7 125.1,36.5 129.29,32.3C133.24,28.35 137.19,24.4 141.14,20.46C142.59,19.02 144.04,17.57 145.49,16.12C147.46,14.13 149.44,12.16 151.43,10.19C152,9.62 152.56,9.04 153.15,8.45C158.99,2.7 164.2,-0.62 172.69,-0.52ZM30.1,204.82C16.96,246.29 20.45,292.15 40,331C73.29,394 137.92,460.7 207.16,482.64C248.92,495.15 294.92,491.06 333.49,470.69C395.92,436.38 462.43,372.3 483.34,302.5C495.24,260.32 490.42,214.44 469.3,176C434.17,114.15 370.41,47.82 300.17,28C204.67,2.67 122.15,68.8 30.1,204.82Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\dispute_.xml:5: Warning: Very long vector path (1230 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#000000" android:pathData="M278.31,103.69C283.28,107.72 286.66,112.72 288,119C288.1,121.9 288.14,124.78 288.13,127.68C288.13,129.01 288.13,129.01 288.13,130.36C288.14,133.35 288.13,136.33 288.12,139.31C288.12,140.88 288.12,140.88 288.12,142.48C288.08,169.62 287.63,196.75 287.17,223.89C287.07,229.39 286.98,234.88 286.9,240.38C286.8,246.45 286.7,252.52 286.6,258.58C286.56,261.12 286.52,263.65 286.48,266.18C286.36,273.79 286.2,281.39 286,289C285.98,289.85 285.96,290.71 285.94,291.59C285.89,293.82 285.81,296.05 285.72,298.29C285.67,299.46 285.63,300.64 285.59,301.85C284.7,306.6 282.25,309.71 278.94,313.13C268.73,319.64 257.73,320.38 246,318.53C240.38,317.1 235.7,314.56 232,310C230.04,305.97 229.69,302.53 229.55,298.06C229.51,297.04 229.51,297.04 229.47,295.99C229.39,293.71 229.32,291.42 229.25,289.14C229.2,287.51 229.14,285.88 229.09,284.24C228.96,280.74 228.85,277.24 228.74,273.74C228.47,265.39 228.18,257.03 227.89,248.67C227.8,245.8 227.7,242.92 227.6,240.05C227.17,227.49 226.68,214.93 226.14,202.38C226.07,200.73 226,199.09 225.93,197.44C225.66,191.22 225.39,185 225.11,178.79C224.43,163.67 223.86,148.56 223.73,133.42C223.71,132.19 223.69,130.95 223.67,129.68C223.65,119.87 225.22,113.06 232.05,105.76C243.97,95.01 265.42,94.86 278.31,103.69Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\process.xml:3: Warning: Very long vector path (1403 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#4C88A9" android:pathData="M65,188C68.72,190.58 71.4,193.52 74.19,197.06C77.34,201 80.58,204.79 84,208.5C88.11,212.96 91.96,217.57 95.77,222.3C98.04,225.05 100.4,227.68 102.81,230.31C111.73,240.23 111.73,240.23 111.4,247.88C110.8,251.06 109.32,252.82 107,255C104.65,256.17 103.26,256.12 100.65,256.1C99.37,256.09 99.37,256.09 98.07,256.09C97.18,256.08 96.29,256.07 95.38,256.06C94.48,256.06 93.58,256.05 92.66,256.05C90.44,256.04 88.22,256.02 86,256C88.78,297.47 100.41,335.16 128,367C128.72,367.84 129.44,368.68 130.18,369.55C143.21,384.46 158.45,395.89 175.83,405.22C179,407 179,407 181,409C177.47,412.63 173.92,416.1 170,419.31C159.33,428.58 156.14,439.54 154,453C149.32,451.62 145.28,449.65 141.13,447.13C140.16,446.54 140.16,446.54 139.17,445.94C131.4,441.14 124.15,435.67 117,430C116.43,429.55 115.86,429.1 115.27,428.63C101.77,417.82 89.55,405.64 79.27,391.72C78.01,390.01 76.73,388.33 75.45,386.64C68.7,377.61 63.19,368 58,358C57.41,356.88 57.41,356.88 56.82,355.73C40.82,324.69 33.31,290.78 34,256C33.42,256.03 32.84,256.05 32.24,256.08C29.6,256.18 26.96,256.25 24.31,256.31C23.4,256.35 22.49,256.4 21.55,256.44C14.73,256.56 14.73,256.56 10.92,253.61C7.89,249.49 7.57,247.15 8,242C10.82,237.21 14.34,233.14 18,229C19.17,227.67 20.33,226.33 21.5,225C22.09,224.33 22.67,223.66 23.28,222.97C25.87,220 28.44,217 31,214C35.32,208.95 39.65,203.91 44,198.88C44.81,197.93 44.81,197.93 45.64,196.97C55.8,185.22 55.8,185.22 65,188Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\process.xml:5: Warning: Very long vector path (1563 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#336C92" android:pathData="M270,8C274.79,10.82 278.86,14.34 283,18C284.33,19.17 285.67,20.33 287,21.5C287.67,22.09 288.34,22.67 289.03,23.28C292,25.87 295,28.44 298,31C303.05,35.32 308.09,39.65 313.13,44C314.07,44.81 314.07,44.81 315.03,45.64C324.34,53.69 324.34,53.69 325.21,59.8C324.66,65.51 321.32,68.96 317.17,72.49C316.53,73.01 315.9,73.53 315.25,74.06C313.89,75.21 312.53,76.35 311.18,77.5C310.48,78.08 309.79,78.66 309.07,79.26C305.83,81.99 302.63,84.78 299.44,87.56C298.2,88.64 296.96,89.71 295.71,90.79C293.84,92.41 291.97,94.03 290.09,95.66C288.23,97.27 286.37,98.89 284.5,100.5C283.67,101.22 283.67,101.22 282.83,101.96C271.36,111.84 271.36,111.84 264.09,111.43C260.94,110.78 259.17,109.3 257,107C255.83,104.65 255.88,103.26 255.9,100.65C255.91,99.8 255.91,98.95 255.91,98.07C255.92,97.18 255.93,96.29 255.94,95.38C255.94,94.48 255.95,93.58 255.95,92.66C255.96,90.44 255.98,88.22 256,86C214.5,88.35 176.75,100.49 145,128C144.16,128.72 143.32,129.44 142.45,130.18C125.87,144.67 114.2,161.68 104,181C100.26,177.89 96.68,174.88 93.63,171.06C84.11,160 73.16,156.08 59,154C60.38,149.32 62.35,145.28 64.88,141.13C65.46,140.16 65.46,140.16 66.06,139.17C70.86,131.4 76.33,124.15 82,117C82.45,116.43 82.9,115.86 83.37,115.27C94.18,101.77 106.36,89.55 120.28,79.27C121.99,78.01 123.67,76.73 125.36,75.45C134.39,68.7 144,63.19 154,58C154.75,57.61 155.5,57.22 156.27,56.82C187.31,40.82 221.22,33.31 256,34C255.97,33.42 255.95,32.84 255.92,32.24C255.82,29.6 255.75,26.96 255.69,24.31C255.62,22.94 255.62,22.94 255.56,21.55C255.44,14.73 255.44,14.73 258.39,10.92C262.51,7.89 264.85,7.57 270,8Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\process.xml:7: Warning: Very long vector path (1677 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#6ECDE2" android:pathData="M358,59C362.68,60.38 366.72,62.35 370.88,64.88C371.84,65.46 371.84,65.46 372.83,66.06C380.6,70.86 387.85,76.33 395,82C395.57,82.45 396.14,82.9 396.73,83.37C410.23,94.18 422.45,106.36 432.73,120.28C433.99,121.99 435.27,123.67 436.55,125.36C443.3,134.39 448.81,144 454,154C454.39,154.75 454.78,155.5 455.18,156.27C471.18,187.31 478.69,221.22 478,256C478.58,255.97 479.16,255.95 479.76,255.92C482.4,255.82 485.04,255.75 487.69,255.69C489.06,255.62 489.06,255.62 490.45,255.56C497.27,255.44 497.27,255.44 501.08,258.39C504.11,262.51 504.43,264.85 504,270C501.18,274.79 497.66,278.86 494,283C492.83,284.33 491.67,285.67 490.5,287C489.91,287.67 489.33,288.34 488.72,289.03C486.13,292 483.56,295 481,298C476.68,303.05 472.35,308.09 468,313.13C467.46,313.75 466.92,314.38 466.36,315.03C458.31,324.34 458.31,324.34 452.2,325.21C445.52,324.56 441.85,320.02 437.88,315.19C437.31,314.52 436.75,313.86 436.17,313.17C435.01,311.79 433.85,310.41 432.69,309.02C430.16,305.99 427.58,303 425,300C424.02,298.85 423.04,297.71 422.06,296.56C419.9,294.04 417.72,291.53 415.54,289.04C414.96,288.37 414.38,287.71 413.78,287.03C412.67,285.76 411.56,284.5 410.45,283.24C400.15,271.45 400.15,271.45 400.57,264.09C401.22,260.94 402.7,259.17 405,257C407.35,255.83 408.74,255.88 411.35,255.9C412.2,255.91 413.05,255.91 413.93,255.91C414.82,255.92 415.71,255.93 416.63,255.94C417.52,255.94 418.42,255.95 419.34,255.95C421.56,255.96 423.78,255.98 426,256C423.22,214.53 411.59,176.84 384,145C383.29,144.17 382.59,143.34 381.86,142.49C367.5,126.04 350.51,113.65 331,104C334.13,100.16 337.2,96.74 341,93.56C345.03,90.12 348.11,86.43 351,82C351.38,81.42 351.76,80.84 352.15,80.24C355.87,73.77 356.75,66.26 358,59Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\process.xml:9: Warning: Very long vector path (1720 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#24A3DA" android:pathData="M408,331C411.84,334.13 415.26,337.2 418.44,341C421.88,345.03 425.57,348.11 430,351C430.58,351.38 431.16,351.76 431.76,352.15C438.23,355.87 445.74,356.75 453,358C451.62,362.68 449.65,366.72 447.13,370.88C446.54,371.84 446.54,371.84 445.94,372.83C432.37,394.79 414.53,415.32 394,431C393.46,431.42 392.93,431.84 392.37,432.27C362.63,455.38 328,469.61 291,476C290.24,476.13 289.49,476.26 288.7,476.4C277.81,478.21 267.01,478.22 256,478C256.03,478.58 256.05,479.16 256.08,479.76C256.18,482.4 256.25,485.04 256.31,487.69C256.35,488.6 256.4,489.51 256.44,490.45C256.56,497.27 256.56,497.27 253.61,501.08C249.49,504.11 247.15,504.43 242,504C237.24,501.2 233.19,497.7 229.06,494.06C227.73,492.9 226.4,491.73 225.07,490.57C224.4,489.98 223.72,489.39 223.02,488.77C219.84,485.98 216.64,483.21 213.44,480.44C212.82,479.91 212.21,479.37 211.58,478.83C208.43,476.09 205.26,473.37 202.09,470.66C201.14,469.85 201.14,469.85 200.17,469.02C198.97,468 197.77,466.98 196.57,465.96C187.67,458.38 187.67,458.38 186.79,452.2C187.44,445.52 191.98,441.85 196.81,437.88C197.48,437.31 198.14,436.75 198.83,436.17C200.21,435.01 201.59,433.85 202.98,432.69C206.01,430.16 209,427.58 212,425C213.15,424.02 214.29,423.04 215.44,422.06C217.96,419.9 220.47,417.72 222.96,415.54C223.96,414.67 223.96,414.67 224.97,413.78C226.24,412.67 227.5,411.56 228.76,410.45C240.55,400.15 240.55,400.15 247.91,400.57C251.06,401.22 252.83,402.7 255,405C256.17,407.35 256.12,408.74 256.1,411.35C256.09,412.63 256.09,412.63 256.09,413.93C256.08,415.26 256.08,415.26 256.06,416.63C256.06,417.52 256.05,418.42 256.05,419.34C256.04,421.56 256.02,423.78 256,426C297.47,423.22 335.16,411.59 367,384C367.84,383.28 368.68,382.56 369.55,381.82C385.9,367.53 398.32,350.37 408,331Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\right_arrow.xml:3: Warning: Very long vector path (2521 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#000000" android:pathData="M182,80C186.96,82.91 190.82,86.54 194.86,90.6C195.52,91.27 196.19,91.93 196.87,92.61C199.09,94.82 201.31,97.04 203.52,99.26C205.11,100.85 206.71,102.44 208.3,104.03C212.62,108.35 216.94,112.67 221.26,116.99C225.77,121.51 230.29,126.02 234.81,130.54C242.88,138.61 250.95,146.68 259.01,154.75C266.33,162.08 273.64,169.39 280.96,176.7C289.47,185.2 297.97,193.7 306.47,202.2C310.97,206.7 315.47,211.2 319.97,215.7C324.2,219.92 328.43,224.15 332.65,228.38C334.2,229.94 335.75,231.49 337.31,233.04C339.43,235.16 341.54,237.27 343.65,239.4C344.27,240.01 344.89,240.63 345.53,241.26C346.38,242.12 346.38,242.12 347.24,242.99C347.73,243.48 348.22,243.97 348.72,244.47C351.74,248.09 352.32,251.22 352.38,255.88C352.4,256.88 352.43,257.89 352.46,258.93C351.45,265.64 346.02,270.27 341.4,274.86C340.4,275.86 340.4,275.86 339.39,276.87C337.18,279.09 334.96,281.31 332.74,283.52C331.15,285.11 329.56,286.71 327.97,288.3C323.65,292.62 319.33,296.94 315.01,301.26C310.49,305.77 305.98,310.29 301.46,314.81C293.39,322.88 285.32,330.95 277.25,339.01C269.92,346.33 262.61,353.64 255.3,360.96C246.8,369.47 238.3,377.97 229.8,386.47C225.3,390.97 220.8,395.47 216.3,399.97C212.08,404.2 207.85,408.43 203.62,412.65C202.06,414.2 200.51,415.75 198.96,417.31C196.84,419.43 194.73,421.54 192.6,423.65C191.99,424.27 191.37,424.89 190.74,425.53C190.17,426.09 189.6,426.66 189.01,427.24C188.52,427.73 188.03,428.22 187.53,428.72C183,432.51 178.62,432.42 172.91,432.31C167.61,431.74 165.16,429.22 161.63,425.5C159.28,420.44 159.03,415.48 160,410C162.81,405.26 166.18,401.54 170.06,397.7C170.67,397.09 171.27,396.49 171.89,395.86C173.91,393.85 175.93,391.84 177.95,389.82C179.4,388.38 180.85,386.93 182.29,385.48C186.22,381.55 190.15,377.63 194.09,373.71C198.2,369.61 202.31,365.5 206.42,361.4C214.19,353.63 221.98,345.86 229.76,338.1C238.62,329.26 247.48,320.41 256.33,311.56C274.55,293.37 292.77,275.18 311,257C309.36,252.83 306.32,250.05 303.19,246.97C302.59,246.36 301.99,245.76 301.37,245.14C299.36,243.13 297.33,241.12 295.31,239.12C293.86,237.67 292.42,236.23 290.97,234.79C287.86,231.68 284.74,228.58 281.63,225.48C276.7,220.57 271.77,215.66 266.85,210.75C256.41,200.33 245.95,189.91 235.5,179.5C224.21,168.26 212.93,157.01 201.65,145.76C196.76,140.88 191.86,136 186.96,131.12C183.91,128.09 180.87,125.06 177.83,122.02C176.42,120.61 175,119.2 173.59,117.8C171.66,115.88 169.73,113.96 167.81,112.03C167.24,111.48 166.68,110.92 166.1,110.35C161.43,105.65 159.71,102.74 159.63,96.13C159.73,90.92 160.3,87.89 164,84C169.13,79.13 175.26,78.81 182,80Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:3: Warning: Very long vector path (3724 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#079CFE" android:pathData="M283.25,22C289.19,25.99 294.88,30.33 300.57,34.65C305.45,38.36 310.38,41.99 315.31,45.63C316.13,46.23 316.95,46.83 317.79,47.46C318.56,48.02 319.32,48.58 320.11,49.16C320.79,49.66 321.47,50.16 322.17,50.68C326.43,53.76 329.12,54.29 334.25,54.29C335.35,54.3 335.35,54.3 336.48,54.31C338.08,54.32 339.67,54.33 341.27,54.34C343.81,54.34 346.34,54.36 348.88,54.39C354.27,54.44 359.67,54.47 365.06,54.5C371.29,54.54 377.52,54.58 383.75,54.64C386.23,54.66 388.72,54.66 391.2,54.67C403.28,54.75 414.25,55.07 424,63C432.48,72.95 433.16,83.08 433.1,95.62C433.1,97.27 433.1,97.27 433.09,98.95C433.09,102.45 433.08,105.94 433.06,109.44C433.06,111.81 433.05,114.19 433.05,116.57C433.04,122.38 433.02,128.19 433,134C433.74,134.27 434.49,134.54 435.25,134.82C437.88,135.95 439.72,137.13 441.93,138.91C442.66,139.48 443.38,140.05 444.13,140.65C444.89,141.26 445.65,141.87 446.44,142.5C452.03,146.93 457.66,151.27 463.5,155.38C479.05,166.34 492.43,177.03 496.53,196.49C497.15,201.11 497.14,205.63 497.14,210.28C497.14,211.83 497.14,211.83 497.15,213.42C497.16,216.88 497.16,220.34 497.16,223.81C497.17,226.29 497.17,228.77 497.18,231.26C497.19,238.01 497.2,244.75 497.2,251.5C497.2,255.72 497.21,259.94 497.21,264.16C497.22,275.85 497.23,287.53 497.24,299.22C497.24,299.97 497.24,300.72 497.24,301.49C497.24,302.24 497.24,302.99 497.24,303.76C497.24,305.28 497.24,306.8 497.24,308.31C497.24,309.07 497.24,309.82 497.24,310.6C497.24,322.79 497.26,334.98 497.29,347.18C497.31,359.71 497.32,372.25 497.32,384.78C497.32,391.81 497.33,398.84 497.35,405.87C497.36,412.49 497.37,419.11 497.36,425.73C497.36,428.15 497.36,430.57 497.37,433C497.45,452.61 496.43,468.89 482.25,483.93C470.03,495.14 456.44,497.61 440.39,497.51C438.73,497.52 437.07,497.53 435.41,497.54C430.88,497.55 426.36,497.55 421.83,497.54C416.93,497.53 412.03,497.55 407.14,497.56C397.56,497.58 387.98,497.58 378.4,497.57C370.61,497.57 362.82,497.57 355.03,497.57C353.36,497.58 353.36,497.58 351.67,497.58C349.41,497.58 347.16,497.58 344.9,497.58C323.77,497.6 302.63,497.59 281.5,497.57C262.18,497.56 242.86,497.57 223.54,497.6C203.69,497.63 183.84,497.64 163.98,497.63C161.73,497.63 159.49,497.63 157.24,497.63C155.58,497.63 155.58,497.63 153.89,497.63C146.11,497.63 138.34,497.64 130.56,497.65C121.08,497.67 111.6,497.67 102.11,497.65C97.28,497.63 92.45,497.63 87.61,497.65C83.18,497.67 78.75,497.66 74.31,497.64C72.72,497.63 71.12,497.63 69.53,497.65C52.96,497.77 40.21,493.48 28,482C18.15,470.69 14.68,458.07 14.74,443.36C14.74,442.32 14.73,441.28 14.73,440.21C14.72,436.76 14.72,433.31 14.73,429.85C14.72,427.37 14.72,424.88 14.71,422.4C14.7,416.36 14.7,410.33 14.7,404.29C14.7,399.38 14.7,394.47 14.69,389.56C14.69,388.86 14.69,388.16 14.69,387.44C14.69,386.01 14.69,384.59 14.69,383.17C14.68,369.84 14.68,356.51 14.69,343.18C14.69,331.01 14.68,318.83 14.66,306.66C14.64,294.14 14.63,281.62 14.64,269.1C14.64,262.08 14.64,255.05 14.62,248.03C14.61,241.43 14.61,234.82 14.62,228.22C14.63,225.8 14.62,223.38 14.62,220.96C14.56,201.9 15.24,185.36 29.05,170.75C38.94,161.21 50.69,153.4 61.85,145.43C66.5,142.11 71.05,138.7 75.47,135.07C77,134 77,134 79,134C79,133.4 78.99,132.79 78.99,132.17C78.94,125.84 78.91,119.51 78.89,113.19C78.88,110.83 78.87,108.47 78.85,106.12C78.82,102.72 78.81,99.32 78.8,95.91C78.79,94.87 78.78,93.83 78.77,92.76C78.77,82.02 80.05,72.54 87,64C95.65,56.63 104.44,54.83 115.64,54.95C117.15,54.97 118.66,54.98 120.17,54.99C120.97,55 121.76,55.01 122.57,55.01C127.7,55.06 132.84,55.04 137.97,55.04C142.46,55.05 146.94,55.07 151.43,55.13C155.8,55.18 160.16,55.19 164.53,55.16C166.18,55.16 167.82,55.18 169.47,55.21C182.34,55.47 189.31,52.36 199,44C200.45,42.9 201.9,41.82 203.38,40.75C204.58,39.83 205.79,38.92 207,38C230.28,20.48 254.64,4.72 283.25,22Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:5: Warning: Very long vector path (3329 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#E4E7EF" android:pathData="M283.25,22C289.19,25.99 294.88,30.33 300.57,34.65C305.45,38.36 310.38,41.99 315.31,45.63C316.13,46.23 316.95,46.83 317.79,47.46C318.56,48.02 319.32,48.58 320.11,49.16C320.79,49.66 321.47,50.16 322.17,50.68C326.43,53.76 329.12,54.29 334.25,54.29C335.35,54.3 335.35,54.3 336.48,54.31C338.08,54.32 339.67,54.33 341.27,54.34C343.81,54.34 346.34,54.36 348.88,54.39C354.27,54.44 359.67,54.47 365.06,54.5C371.29,54.54 377.52,54.58 383.75,54.64C386.23,54.66 388.72,54.66 391.2,54.67C403.28,54.75 414.25,55.07 424,63C431.47,71.77 433.16,80.78 433.13,92.1C433.14,93.33 433.14,93.33 433.14,94.59C433.14,99.47 433.12,104.35 433.09,109.24C433.07,111.54 433.06,113.84 433.05,116.15C433.04,117.38 433.03,118.62 433.02,119.89C432.97,129.04 433.05,138.17 433.16,147.31C433.26,155.73 433.31,164.15 433.33,172.57C433.35,177.9 433.39,183.23 433.48,188.56C433.58,193.67 433.6,198.78 433.58,203.89C433.58,205.8 433.61,207.7 433.67,209.6C433.92,219.01 433.77,226.06 427.09,233.19C424.19,235.97 421.15,238.51 418,241C416.58,242.24 415.16,243.49 413.75,244.75C412.85,245.52 412.85,245.52 411.92,246.3C408.61,249.24 405.55,252.44 402.44,255.59C399.75,258.25 396.93,260.7 394.06,263.16C391.61,265.35 389.31,267.66 387,270C384.1,272.93 381.2,275.76 378.06,278.44C374.71,281.3 371.59,284.36 368.5,287.5C364.81,291.24 361.02,294.78 357.03,298.19C353.68,301.17 350.59,304.4 347.44,307.59C344.74,310.26 341.91,312.73 339.03,315.2C335.18,318.62 331.6,322.32 328,326C325.07,324.67 322.93,323.2 320.61,320.99C319.99,320.4 319.36,319.81 318.72,319.2C318.07,318.58 317.42,317.96 316.75,317.31C315.39,316.03 314.04,314.75 312.68,313.46C312.04,312.86 311.39,312.25 310.73,311.62C308.82,309.83 306.86,308.09 304.88,306.38C301.38,303.37 298,300.25 294.63,297.13C293.78,296.35 293.78,296.35 292.92,295.56C290.37,293.2 287.82,290.83 285.29,288.44C275.7,279.5 266.51,273.41 253.09,273.69C238.72,275.13 228.83,286.88 218.96,296.28C215.69,299.39 212.32,302.37 208.91,305.33C206.05,307.83 203.28,310.41 200.5,313C195.74,317.42 190.93,321.77 186,326C183.08,324.69 181.05,323.28 178.83,320.99C178.25,320.4 177.67,319.81 177.08,319.2C176.17,318.27 176.17,318.27 175.25,317.31C170.96,312.95 166.66,308.72 161.99,304.77C158.88,302 155.99,299.01 153.07,296.05C149.85,292.8 146.51,289.78 143.03,286.81C139.68,283.82 136.59,280.59 133.44,277.41C131.35,275.35 129.23,273.4 127,271.5C122.92,268.02 119.2,264.22 115.44,260.41C113.35,258.35 111.23,256.4 109,254.5C104.93,251.02 101.21,247.23 97.45,243.42C94.76,240.77 91.98,238.29 89.08,235.86C88.39,235.25 87.71,234.63 87,234C86.44,233.66 85.88,233.32 85.3,232.97C81.42,230.14 80.14,227.84 79.34,223.07C79,217.87 79.06,212.68 79.1,207.48C79.09,205.51 79.08,203.54 79.06,201.58C79.03,196.34 79.04,191.1 79.06,185.86C79.07,180.48 79.04,175.09 79.02,169.7C78.99,161.26 78.98,152.83 78.99,144.39C79.01,135 78.97,125.61 78.89,116.23C78.87,112.9 78.85,109.57 78.83,106.24C78.82,104.27 78.81,102.3 78.79,100.33C78.62,74.3 78.62,74.3 87,64C95.65,56.63 104.44,54.83 115.64,54.95C117.15,54.97 118.66,54.98 120.17,54.99C120.97,55 121.76,55.01 122.57,55.01C127.7,55.06 132.84,55.04 137.97,55.04C142.46,55.05 146.94,55.07 151.43,55.13C155.8,55.18 160.16,55.19 164.53,55.16C166.18,55.16 167.82,55.18 169.47,55.21C182.34,55.47 189.31,52.36 199,44C200.45,42.9 201.9,41.82 203.38,40.75C204.58,39.83 205.79,38.92 207,38C230.28,20.48 254.64,4.72 283.25,22Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:7: Warning: Very long vector path (2023 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#5BBDFE" android:pathData="M80,133C79.99,134.15 79.97,135.3 79.96,136.49C79.83,147.37 79.77,158.26 79.77,169.15C79.77,174.74 79.74,180.34 79.67,185.93C79.6,191.34 79.58,196.75 79.61,202.16C79.61,204.21 79.59,206.27 79.55,208.32C79.06,221.48 79.06,221.48 85.19,232.59C87.67,234.93 90.26,236.96 93,239C94.38,240.2 95.76,241.4 97.13,242.63C97.75,243.15 98.37,243.68 99.01,244.23C102.34,247.2 105.43,250.42 108.56,253.59C110.65,255.65 112.77,257.6 115,259.5C119.08,262.98 122.8,266.78 126.56,270.59C128.65,272.65 130.77,274.6 133,276.5C136.15,279.2 139.08,282.05 142,285C144.9,287.93 147.8,290.76 150.94,293.44C154.29,296.3 157.41,299.36 160.5,302.5C164.16,306.21 167.91,309.72 171.88,313.1C176.45,317.19 180.7,321.63 185,326C180.95,331.01 176.6,335.29 171.77,339.55C168.82,342.16 165.9,344.83 163,347.5C155.32,354.55 147.58,361.51 139.69,368.31C133.67,373.5 127.8,378.82 121.98,384.23C118.54,387.44 115.03,390.58 111.53,393.72C108.67,396.3 105.83,398.9 103,401.5C98.55,405.59 94.06,409.65 89.56,413.69C86.69,416.28 83.85,418.89 81,421.5C76.55,425.59 72.06,429.65 67.56,433.69C64.69,436.28 61.85,438.89 59,441.5C54.55,445.59 50.07,449.64 45.57,453.68C42.66,456.31 39.76,458.97 36.88,461.63C32.31,465.82 27.7,469.95 23,474C20.54,471.01 19.16,468.21 17.88,464.56C17.54,463.64 17.21,462.71 16.87,461.75C14.97,455.71 14.85,450.12 14.86,443.84C14.86,442.8 14.85,441.77 14.85,440.71C14.84,437.24 14.84,433.77 14.84,430.31C14.83,427.82 14.83,425.34 14.82,422.86C14.81,416.1 14.8,409.35 14.8,402.6C14.8,398.38 14.79,394.16 14.79,389.94C14.78,378.25 14.77,366.55 14.76,354.86C14.76,354.11 14.76,353.36 14.76,352.59C14.76,351.84 14.76,351.09 14.76,350.32C14.76,348.8 14.76,347.28 14.76,345.76C14.76,345.01 14.76,344.25 14.76,343.48C14.76,331.28 14.74,319.08 14.71,306.88C14.69,294.34 14.68,281.8 14.68,269.26C14.68,262.23 14.67,255.19 14.65,248.16C14.64,241.53 14.63,234.91 14.64,228.29C14.64,225.87 14.64,223.44 14.63,221.02C14.56,201.94 15.23,185.38 29.05,170.75C40.16,160.04 53.48,151.37 66.01,142.43C69.74,139.75 73.43,137.02 77.13,134.29C79,133 79,133 80,133Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:9: Warning: Very long vector path (2330 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#5BBDFE" android:pathData="M432,133C435.96,135.24 439.76,137.42 443.39,140.18C444.17,140.77 444.96,141.36 445.76,141.97C446.99,142.92 446.99,142.92 448.25,143.88C453.54,147.89 458.83,151.9 464.21,155.78C479.47,166.78 492.47,177.21 496.53,196.49C497.15,201.11 497.14,205.63 497.14,210.28C497.14,211.32 497.15,212.35 497.15,213.42C497.16,216.88 497.16,220.34 497.16,223.81C497.17,226.29 497.17,228.77 497.18,231.26C497.19,238.01 497.2,244.76 497.2,251.5C497.2,255.72 497.21,259.94 497.21,264.16C497.22,275.85 497.23,287.53 497.24,299.22C497.24,300.34 497.24,300.34 497.24,301.49C497.24,302.24 497.24,302.99 497.24,303.76C497.24,305.28 497.24,306.8 497.24,308.31C497.24,309.44 497.24,309.44 497.24,310.6C497.24,322.79 497.26,334.98 497.29,347.18C497.31,359.71 497.32,372.25 497.32,384.78C497.32,391.81 497.33,398.84 497.35,405.87C497.36,412.49 497.37,419.11 497.36,425.73C497.36,428.15 497.36,430.57 497.37,433C497.43,447.91 497.43,460.67 490,474C484.53,470.2 479.86,465.97 475.2,461.21C472.67,458.67 470,456.34 467.28,454C464.39,451.47 461.57,448.86 458.75,446.25C458.19,445.74 457.64,445.22 457.07,444.69C455.93,443.64 454.8,442.59 453.67,441.55C446.68,435.08 439.65,428.67 432.56,422.31C429.18,419.26 425.82,416.17 422.46,413.09C419.32,410.21 416.17,407.34 413,404.5C409.31,401.19 405.65,397.85 402,394.5C397.5,390.37 392.98,386.26 388.43,382.18C386.79,380.71 385.16,379.24 383.54,377.76C379.25,373.85 374.9,370.08 370.38,366.44C366.28,363.13 362.54,359.65 358.88,355.88C356.47,353.47 353.91,351.27 351.33,349.04C348.36,346.44 345.46,343.75 342.56,341.06C337.43,336.31 332.25,331.62 327,327C333.66,318.94 340.99,311.63 348.94,304.84C351.39,302.65 353.69,300.34 356,298C358.9,295.07 361.8,292.24 364.94,289.56C368.29,286.7 371.41,283.64 374.5,280.5C378.19,276.76 381.98,273.22 385.97,269.81C389.32,266.82 392.41,263.59 395.56,260.41C398.25,257.75 401.07,255.3 403.94,252.84C407.29,249.85 410.39,246.61 413.55,243.42C416.24,240.77 419.02,238.29 421.92,235.86C422.61,235.25 423.29,234.63 424,234C424.56,233.54 425.12,233.09 425.7,232.62C429.18,229.56 431.41,227.31 431.88,222.59C431.92,220.92 431.92,219.25 431.89,217.58C431.9,216.2 431.9,216.2 431.92,214.8C431.95,211.78 431.92,208.77 431.9,205.75C431.91,203.66 431.92,201.56 431.94,199.46C431.97,193.96 431.96,188.45 431.94,182.94C431.93,177.32 431.96,171.7 431.99,166.07C432.03,155.05 432.03,144.02 432,133Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:13: Warning: Very long vector path (1142 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#0D9EFE" android:pathData="M225.78,174.82C233.83,179.11 237.2,185.6 240,194C240.87,204.97 240.33,213.01 233.46,222.02C228.06,228.1 221.08,231.08 213,231.56C207.42,231.35 201.02,229.02 197,225C196.99,225.8 196.97,226.6 196.96,227.43C196.88,231.06 196.78,234.69 196.69,238.31C196.65,240.21 196.65,240.21 196.62,242.14C196.58,243.35 196.55,244.56 196.51,245.8C196.49,246.92 196.46,248.03 196.43,249.18C196,252 196,252 194.62,253.86C192.25,255.53 190.84,255.48 188,255C184.92,252.72 184.08,251.53 183.5,247.69C183.5,246.38 183.5,245.06 183.5,243.71C183.49,242.98 183.49,242.25 183.48,241.5C183.47,239.11 183.49,236.71 183.5,234.31C183.5,232.64 183.5,230.97 183.5,229.3C183.5,225.81 183.51,222.31 183.53,218.82C183.56,214.34 183.56,209.86 183.55,205.38C183.54,201.93 183.55,198.49 183.56,195.04C183.56,193.39 183.56,191.74 183.56,190.08C183.56,187.78 183.57,185.47 183.6,183.16C183.6,181.85 183.61,180.53 183.61,179.18C184,176 184,176 185.3,174.07C188.14,172.28 190.75,172.7 194,173C196,175 196,175 196,178C196.54,177.65 197.09,177.3 197.64,176.94C198.72,176.26 198.72,176.26 199.81,175.56C200.52,175.11 201.23,174.66 201.96,174.19C209.36,169.87 218.34,171.41 225.78,174.82Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:19: Warning: Very long vector path (2518 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#0D9EFE" android:pathData="M350.3,174.07C352.68,175.37 354.35,176.87 356,179C356,179.66 356,180.32 356,181C356.59,180.52 357.18,180.04 357.79,179.54C358.58,178.91 359.37,178.28 360.19,177.63C360.96,177 361.74,176.37 362.54,175.73C368.17,171.77 373.3,171.33 380,172C386.3,173.96 390.12,177 393.62,182.58C395.4,187 395.43,190.73 395.4,195.45C395.4,196.34 395.4,197.24 395.41,198.16C395.41,200.04 395.4,201.92 395.39,203.8C395.38,206.69 395.39,209.57 395.41,212.46C395.41,214.29 395.4,216.12 395.4,217.95C395.4,218.81 395.41,219.67 395.42,220.56C395.4,221.76 395.4,221.76 395.39,222.99C395.39,223.69 395.38,224.4 395.38,225.12C394.88,227.59 393.98,228.48 392,230C389,230.44 389,230.44 386,230C383.19,227.48 382.99,225.9 382.62,222.11C382.59,220.54 382.59,218.97 382.59,217.4C382.58,216.57 382.57,215.74 382.56,214.89C382.52,211.34 382.5,207.79 382.49,204.24C382.47,201.65 382.44,199.06 382.41,196.46C382.41,195.66 382.41,194.86 382.42,194.03C382.41,189.77 382.41,189.77 380.6,186.03C376.87,183.62 373.35,183.41 369,184C365.08,186.07 362.22,188.15 360,192C359.71,194.72 359.58,197.22 359.59,199.94C359.58,200.71 359.57,201.47 359.56,202.26C359.52,205.52 359.5,208.78 359.49,212.04C359.47,214.42 359.44,216.81 359.41,219.19C359.41,219.93 359.41,220.67 359.42,221.43C359.37,224.37 359.33,226.49 357.7,229C354.73,230.75 352.2,229.81 349,229C346.44,225.15 346.7,221.96 346.69,217.5C346.67,215.79 346.64,214.08 346.62,212.38C346.59,209.68 346.56,206.99 346.54,204.3C346.52,201.7 346.48,199.1 346.44,196.5C346.44,195.69 346.44,194.89 346.44,194.06C346.49,189.88 346.49,189.88 344.6,186.33C341.67,183.9 339.03,183.45 335.23,183.57C331.57,184.28 329.4,185.85 326.68,188.35C322.99,194.18 324.08,202 324.25,208.69C324.29,211.1 324.32,213.5 324.32,215.91C324.33,217.41 324.35,218.9 324.4,220.39C324.43,223.81 324.23,225.68 322.21,228.5C320,230 320,230 316.31,229.88C315.22,229.59 314.13,229.3 313,229C311.75,226.51 311.87,224.82 311.85,222.03C311.85,221 311.84,219.96 311.84,218.9C311.83,217.78 311.83,216.65 311.83,215.5C311.83,213.78 311.83,213.78 311.82,212.03C311.82,209.6 311.81,207.17 311.81,204.74C311.81,201.02 311.79,197.29 311.78,193.56C311.77,191.21 311.77,188.85 311.77,186.5C311.76,185.38 311.76,184.26 311.75,183.1C311.75,182.07 311.75,181.03 311.76,179.97C311.76,179.06 311.75,178.14 311.75,177.2C312,175 312,175 314,173C317.5,172.75 317.5,172.75 321,173C323,175 323,175 323,179C323.54,178.58 324.09,178.16 324.64,177.72C325.36,177.17 326.08,176.63 326.81,176.06C327.52,175.52 328.23,174.97 328.96,174.41C335.15,170.14 343.7,171.25 350.3,174.07Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:21: Warning: Very long vector path (1218 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#0E9EFE" android:pathData="M287,174.6C292.74,178.61 296.15,183.28 298,190C298.21,192.81 298.33,195.51 298.34,198.32C298.34,199.1 298.35,199.88 298.36,200.69C298.37,202.33 298.37,203.97 298.37,205.61C298.38,208.12 298.41,210.63 298.45,213.14C298.46,214.74 298.46,216.35 298.46,217.95C298.48,218.69 298.49,219.44 298.5,220.21C298.47,223.68 298.25,225.66 296.2,228.5C294,230 294,230 290.38,230C287,229 287,229 285.79,226.94C285.4,225.98 285.4,225.98 285,225C284.31,225.35 283.63,225.7 282.92,226.06C281.57,226.74 281.57,226.74 280.19,227.44C279.29,227.89 278.4,228.34 277.48,228.81C271.81,231.53 265.78,232.18 259.71,230.28C254.47,227.84 251.51,224.79 248.75,219.69C247.2,214.14 247.18,209.85 249.94,204.81C253.71,199.33 259.58,196.34 266,195C267.51,194.92 269.03,194.89 270.55,194.9C271.39,194.91 272.24,194.91 273.11,194.91C273.98,194.92 274.85,194.93 275.75,194.94C276.64,194.94 277.53,194.95 278.44,194.95C280.63,194.96 282.81,194.98 285,195C284.23,189.69 284.23,189.69 281.32,185.51C278.12,184.81 275.15,184.78 271.88,184.81C271.21,184.82 270.55,184.82 269.87,184.83C265.14,184.89 260.52,185.27 255.86,185.98C254.94,185.99 254.94,185.99 254,186C252,184 252,184 251.63,181.06C252,178 252,178 253.71,176.41C263.04,170.64 277.21,169.52 287,174.6Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:23: Warning: Very long vector path (1051 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#0B9DFE" android:pathData="M170,157C171,158 171,158 171.19,160.94C171,164 171,164 169,166C164.4,166.37 160.72,165.66 156.3,164.45C149.38,163.09 141.52,163.21 135,166C131.98,168.11 131.98,168.11 130,171C129.58,174.52 129.88,176.64 131,180C134.48,181.71 138.18,182.7 141.89,183.77C143.58,184.27 143.58,184.27 145.29,184.78C147.62,185.46 149.96,186.14 152.3,186.81C169.06,191.82 169.06,191.82 173,199C174.78,204.77 175.21,211.67 173.13,217.39C169.5,223.63 163.89,227.88 157,230C144.79,232.34 129.8,232.22 119.09,225.05C117,223 117,223 116.56,219.88C117,217 117,217 119,215C123.31,214.55 126.32,215.04 130.38,216.5C135.19,218.05 139.67,218.34 144.69,218.38C145.4,218.39 146.11,218.4 146.85,218.42C151.92,218.37 155.49,217.34 160,215C161.71,212.77 161.96,211.25 162.44,208.5C162.22,205.82 162.22,205.82 159.91,204C156.66,201.77 153.76,200.68 150,199.5C149.35,199.29 148.69,199.08 148.02,198.86C144.84,197.84 141.66,196.91 138.45,196.04C131.23,194.05 125.14,191.66 120,186C116.89,180.05 116.72,172.15 118.69,165.77C121.95,159.6 127.66,155.59 134,153C145.59,150.4 159.6,150.86 170,157Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:25: Warning: Very long vector path (1019 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#57BCFF" android:pathData="M230.39,445.35C231.11,445.34 231.82,445.34 232.56,445.33C234.91,445.3 237.25,445.32 239.6,445.34C241.24,445.33 242.87,445.33 244.51,445.32C247.94,445.31 251.37,445.32 254.79,445.35C259.18,445.38 263.57,445.36 267.96,445.33C271.34,445.31 274.72,445.32 278.1,445.33C279.72,445.33 281.33,445.33 282.95,445.32C285.21,445.31 287.47,445.33 289.73,445.35C291.02,445.36 292.31,445.36 293.63,445.37C297.87,446.16 299.36,447.65 302,451C302.63,454 302.63,454 302,457C299.36,460.35 297.87,461.84 293.63,462.63C292.34,462.64 291.06,462.64 289.73,462.65C289.02,462.66 288.31,462.66 287.57,462.67C285.23,462.7 282.88,462.68 280.54,462.66C278.9,462.67 277.26,462.67 275.62,462.68C272.19,462.69 268.77,462.68 265.34,462.65C260.95,462.62 256.57,462.64 252.18,462.67C248.8,462.69 245.42,462.68 242.04,462.67C240.42,462.67 238.8,462.67 237.19,462.68C234.92,462.69 232.66,462.67 230.39,462.65C228.47,462.64 228.47,462.64 226.5,462.63C221.88,461.8 220.26,460.33 217,457C216.25,454 216.25,454 217,451C221.39,446.5 223.95,445.37 230.39,445.35Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:27: Warning: Very long vector path (1003 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#5BBDFF" android:pathData="M250.35,411.47C251.09,411.46 251.82,411.45 252.58,411.44C255,411.42 257.42,411.42 259.84,411.43C261.53,411.43 263.21,411.42 264.9,411.41C268.43,411.4 271.96,411.41 275.5,411.42C280.02,411.44 284.54,411.42 289.07,411.38C292.55,411.36 296.03,411.36 299.52,411.37C301.18,411.37 302.85,411.36 304.52,411.35C306.85,411.33 309.19,411.35 311.52,411.37C312.55,411.35 312.55,411.35 313.6,411.33C317.37,411.41 318.95,411.96 321.96,414.31C324,417 324,417 324.13,420.63C323,424 323,424 320.89,425.69C317.28,427.33 314.58,427.39 310.61,427.4C309.89,427.41 309.16,427.42 308.41,427.42C306.02,427.44 303.63,427.44 301.25,427.43C299.58,427.44 297.92,427.44 296.25,427.45C292.77,427.46 289.28,427.46 285.8,427.45C281.33,427.43 276.87,427.45 272.41,427.48C268.97,427.5 265.53,427.5 262.1,427.5C260.45,427.5 258.81,427.5 257.16,427.51C254.86,427.53 252.56,427.52 250.26,427.5C248.95,427.5 247.64,427.5 246.29,427.5C243,427 243,427 240.58,425.19C238.27,421.99 238.45,419.84 239,416C242.14,411.63 245.3,411.42 250.35,411.47Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:31: Warning: Very long vector path (972 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#089CFE" android:pathData="M125.88,116.6C127.69,116.58 127.69,116.58 129.54,116.56C130.84,116.56 132.15,116.56 133.5,116.57C134.85,116.56 136.2,116.56 137.56,116.55C140.39,116.54 143.23,116.54 146.07,116.55C149.7,116.57 153.33,116.55 156.97,116.52C159.76,116.5 162.56,116.5 165.35,116.5C167.34,116.5 169.33,116.49 171.32,116.47C172.53,116.48 173.75,116.49 174.99,116.5C176.06,116.5 177.13,116.5 178.22,116.5C181.69,117.13 182.91,118.2 185,121C184.94,124.13 184.94,124.13 184,127C180.54,129.31 179.19,129.26 175.11,129.27C173.29,129.29 173.29,129.29 171.44,129.3C170.13,129.3 168.82,129.3 167.47,129.3C166.11,129.3 164.76,129.31 163.4,129.31C160.56,129.32 157.72,129.32 154.88,129.32C151.23,129.31 147.59,129.33 143.95,129.35C141.15,129.36 138.35,129.37 135.55,129.36C133.56,129.36 131.56,129.38 129.56,129.39C127.75,129.38 127.75,129.38 125.89,129.37C124.82,129.37 123.76,129.37 122.66,129.37C120,129 120,129 118.16,127.66C116.35,125.07 116.62,123.07 117,120C119.43,116.51 121.8,116.62 125.88,116.6Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml:33: Warning: Very long vector path (959 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#0C9DFE" android:pathData="M160.9,88.6C162.68,88.58 162.68,88.58 164.5,88.56C165.78,88.56 167.06,88.56 168.38,88.57C170.35,88.56 170.35,88.56 172.36,88.55C175.15,88.54 177.93,88.54 180.72,88.55C184.28,88.57 187.85,88.55 191.41,88.52C194.81,88.49 198.22,88.5 201.63,88.5C202.9,88.49 204.18,88.48 205.5,88.47C206.69,88.48 207.88,88.49 209.1,88.5C210.67,88.5 210.67,88.5 212.27,88.5C215.7,89.13 216.88,90.28 219,93C219.44,95.56 219.44,95.56 219,98C217.85,99.63 217.85,99.63 216,101C213.31,101.38 213.31,101.38 210.02,101.39C208.78,101.4 207.55,101.41 206.28,101.42C204.94,101.41 203.59,101.41 202.25,101.4C200.87,101.4 199.49,101.4 198.11,101.41C195.23,101.41 192.34,101.41 189.45,101.39C185.75,101.37 182.04,101.38 178.34,101.4C175.5,101.41 172.65,101.41 169.8,101.4C168.44,101.4 167.07,101.4 165.71,101.41C163.8,101.42 161.89,101.4 159.98,101.39C158.36,101.38 158.36,101.38 156.69,101.38C154,101 154,101 152.15,99.66C150.33,97.04 150.55,95.09 151,92C154.12,88.87 156.58,88.62 160.9,88.6Z"/>
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:60: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
        <LinearLayout
         ~~~~~~~~~~~~

   Explanation for issues of type "DisableBaselineAlignment":
   When a LinearLayout is used to distribute the space proportionally between
   nested layouts, the baseline alignment property should be turned off to
   make the layout computation faster.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_dashboard.xml:8: Warning: Possible overdraw: Root element paints background #F5F5F5 with a theme that also paints a background (inferred theme is @style/Theme.Ekvayu) [Overdraw]
    android:background="#F5F5F5"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_home.xml:7: Warning: Possible overdraw: Root element paints background @color/background with a theme that also paints a background (inferred theme is @style/Theme.Ekvayu) [Overdraw]
    android:background="@color/background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:8: Warning: Possible overdraw: Root element paints background @color/surface with a theme that also paints a background (inferred theme is @style/Theme.Ekvayu) [Overdraw]
    android:background="@color/surface">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\header_layout.xml:5: Warning: Possible overdraw: Root element paints background @color/transparent with a theme that also paints a background (inferred theme is @style/Theme.Ekvayu) [Overdraw]
    android:background="@color/transparent"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:2: Warning: The resource R.layout.activity_email_auth appears to be unused [UnusedResources]
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:2: Warning: The resource R.layout.activity_email_demo appears to be unused [UnusedResources]
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:2: Warning: The resource R.layout.activity_email_detail appears to be unused [UnusedResources]
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_gmail_auth.xml:2: Warning: The resource R.layout.activity_gmail_auth appears to be unused [UnusedResources]
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_anim_button.xml:2: Warning: The resource R.drawable.bg_anim_button appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_search_bar.xml:2: Warning: The resource R.drawable.bg_search_bar appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_status.xml:2: Warning: The resource R.drawable.bg_status appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\colors.xml:16: Warning: The resource R.color.orange appears to be unused [UnusedResources]
    <color name="orange">#FF8C00</color>
           ~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\colors.xml:17: Warning: The resource R.color.blue appears to be unused [UnusedResources]
    <color name="blue">#0066CC</color>
           ~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\colors.xml:25: Warning: The resource R.color.divider_light appears to be unused [UnusedResources]
    <color name="divider_light">#E9ECEF</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\colors.xml:26: Warning: The resource R.color.border_light appears to be unused [UnusedResources]
    <color name="border_light">#DEE2E6</color>
           ~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\colors.xml:34: Warning: The resource R.color.divider_dark appears to be unused [UnusedResources]
    <color name="divider_dark">#404040</color>
           ~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\colors.xml:35: Warning: The resource R.color.border_dark appears to be unused [UnusedResources]
    <color name="border_dark">#505050</color>
           ~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\colors.xml:38: Warning: The resource R.color.transparent_black appears to be unused [UnusedResources]
    <color name="transparent_black">#CC000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\colors.xml:50: Warning: The resource R.color.divider appears to be unused [UnusedResources]
    <color name="divider">@color/divider_light</color>
           ~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\colors.xml:51: Warning: The resource R.color.border appears to be unused [UnusedResources]
    <color name="border">@color/border_light</color>
           ~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\device_d.png: Warning: The resource R.drawable.device_d appears to be unused [UnusedResources]
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\device_details.xml:1: Warning: The resource R.drawable.device_details appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\dispute.xml:1: Warning: The resource R.drawable.dispute appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\dispute_.xml:1: Warning: The resource R.drawable.dispute_ appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android" android:height="26dp" android:viewportHeight="512" android:viewportWidth="512" android:width="26dp">
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\dispute_mails.xml:1: Warning: The resource R.drawable.dispute_mails appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\edit_text_bg.xml:2: Warning: The resource R.drawable.edit_text_bg appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\ic_attachment.xml:2: Warning: The resource R.drawable.ic_attachment appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\ic_launcher_background.xml:2: Warning: The resource R.drawable.ic_launcher_background appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\ic_launcher_foreground.xml:1: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:2: Warning: The resource R.layout.item_email appears to be unused [UnusedResources]
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_dispute_raise.xml:3: Warning: The resource R.layout.layout_dispute_raise appears to be unused [UnusedResources]
    <androidx.constraintlayout.widget.ConstraintLayout
    ^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\raw\raw: Warning: The resource R.raw.raw appears to be unused [UnusedResources]
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\right_arrow.xml:1: Warning: The resource R.drawable.right_arrow appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android" android:height="24dp" android:viewportHeight="512" android:viewportWidth="512" android:width="24dp">
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:4: Warning: The resource R.string.accessibility_service_gmail appears to be unused [UnusedResources]
    <string name="accessibility_service_gmail">Monitors for mail in Gmail.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.continuee appears to be unused [UnusedResources]
    <string name="continuee">Continue</string>
            ~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.google_client_id appears to be unused [UnusedResources]
    <string name="google_client_id">408481275944-9clep0h2rardm0f07ugg6f765ik6bpnb.apps.googleusercontent.com</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:11: Warning: The resource R.string.appAuthRedirectScheme appears to be unused [UnusedResources]
    <string name="appAuthRedirectScheme">https://ekvayuy.firebaseapp.com/__/auth/handler</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:12: Warning: The resource R.string.gmail_auth appears to be unused [UnusedResources]
    <string name="gmail_auth">Gmail Auth</string>
            ~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:13: Warning: The resource R.string.yahoo_auth appears to be unused [UnusedResources]
    <string name="yahoo_auth">Yahoo Auth</string>
            ~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:18: Warning: The resource R.string.hello_blank_fragment appears to be unused [UnusedResources]
    <string name="hello_blank_fragment">Hello blank fragment</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:19: Warning: The resource R.string.safe appears to be unused [UnusedResources]
    <string name="safe">safe</string>
            ~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.unsafe appears to be unused [UnusedResources]
    <string name="unsafe">unsafe</string>
            ~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:22: Warning: The resource R.string.refresh appears to be unused [UnusedResources]
    <string name="refresh">Refresh</string>
            ~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:24: Warning: The resource R.string.basic_information appears to be unused [UnusedResources]
    <string name="basic_information">Basic Information</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:25: Warning: The resource R.string.manufacturer_name appears to be unused [UnusedResources]
    <string name="manufacturer_name">Manufacturer Name</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-hdpi\theme.png: Warning: The resource R.drawable.theme appears to be unused [UnusedResources]
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\themes.xml:43: Warning: The resource R.style.CardStyle appears to be unused [UnusedResources]
    <style name="CardStyle">
           ~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\themes.xml:50: Warning: The resource R.style.ButtonPrimary appears to be unused [UnusedResources]
    <style name="ButtonPrimary" parent="Widget.Material3.Button">
           ~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\themes.xml:55: Warning: The resource R.style.ButtonSecondary appears to be unused [UnusedResources]
    <style name="ButtonSecondary" parent="Widget.Material3.Button.OutlinedButton">
           ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_raise_bottom_sheet.xml:15: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:android="http://schemas.android.com/apk/res/android"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantNamespace":
   In Android XML documents, only specify the namespace on the root/document
   element. Namespace declarations elsewhere in the document are typically
   accidental leftovers from copy/pasting XML from other files or
   documentation.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_raise_bottom_sheet.xml:15: Warning: Unused namespace declaration xmlns:android; already declared on the root element [UnusedNamespace]
        xmlns:android="http://schemas.android.com/apk/res/android"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedNamespace":
   Unused namespace declarations take up space and require processing that is
   not necessary

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml:10: Warning: Replace "-" with an "en dash" character (–, &#8211;) ? [TypographyDashes]
    <string name="google_client_id">408481275944-9clep0h2rardm0f07ugg6f765ik6bpnb.apps.googleusercontent.com</string>
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "TypographyDashes":
   The "n dash" (u2013, &#8211;) and the "m dash" (u2014, &#8212;) characters
   are used for ranges (n dash) and breaks (m dash). Using these instead of
   plain hyphens can make text easier to read and your application will look
   more polished.

   https://en.wikipedia.org/wiki/Dash

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp: Warning: The following images appear both as density independent .xml files and as bitmap files: srcmainresdrawableic_launcher_foreground.xml, srcmainresmipmap-hdpiic_launcher_foreground.webp [IconXmlAndPng]

   Explanation for issues of type "IconXmlAndPng":
   If a drawable resource appears as an .xml file in the drawable/ folder,
   it's usually not intentional for it to also appear as a bitmap using the
   same name; generally you expect the drawable XML file to define states and
   each state has a corresponding drawable bitmap.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml:2: Warning: The application adaptive icon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml:2: Warning: The application adaptive roundIcon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "MonochromeLauncherIcon":
   If android:roundIcon and android:icon are both in your manifest, you must
   either remove the reference to android:roundIcon if it is not needed; or,
   supply the monochrome icon in the drawable defined by the android:roundIcon
   and android:icon attribute.

   For example, if android:roundIcon and android:icon are both in the
   manifest, a launcher might choose to use android:roundIcon over
   android:icon to display the adaptive app icon. Therefore, your themed
   application iconwill not show if your monochrome attribute is not also
   specified in android:roundIcon.

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\device_d.png: Warning: Found bitmap drawable res/drawable/device_d.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:51: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:62: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:73: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:97: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:108: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:119: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:108: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:118: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:219: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                android:textSize="10sp"
                ~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:235: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                android:textSize="10sp"
                ~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:22: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:148: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                android:textSize="10sp"
                ~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_progress_dialog.xml:38: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SmallSp":
   Avoid using sizes smaller than 11sp.

D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:65: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:74: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.squareup.retrofit2:retrofit:2.9.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:75: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.squareup.retrofit2:converter-gson:2.9.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:76: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.squareup.okhttp3:okhttp:4.12.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:77: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.squareup.okhttp3:logging-interceptor:4.12.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:78: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.airbnb.android:lottie:3.4.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:80: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.firebase:firebase-auth:22.3.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:81: Warning: Use version catalog instead [UseTomlInstead]
    implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:83: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.google.android.gms:play-services-auth:21.3.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:86: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:87: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\build.gradle.kts:89: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.github.PhilJay:MPAndroidChart:v3.1.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:93: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:133: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DeviceDetailsFragment.kt:118: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
        binding.tvApiLevel.text=deviceInfo.apiLevel.toString()
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DeviceDetailsFragment.kt:126: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
        binding.tvDensity.text=deviceInfo.screenInfo.densityDpi.toString()
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DeviceDetailsFragment.kt:127: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
        binding.tvDensityFactor.text=deviceInfo.screenInfo.density.toString()
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DeviceDetailsFragment.kt:128: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
        binding.tvScaleDensity.text=deviceInfo.screenInfo.scaledDensity.toString()
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DeviceDetailsFragment.kt:133: Warning: Number formatting does not take into account locale settings. Consider using String.format instead. [SetTextI18n]
        binding.tvTargetSdk.text=deviceInfo.appInfo.targetSdkVersion.toString()
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DisputeListAdapter.kt:27: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.binding.tvSender.text = "Sender"+":"+item.sendersEmail
                                       ~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DisputeListAdapter.kt:27: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.binding.tvSender.text = "Sender"+":"+item.sendersEmail
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DisputeListAdapter.kt:27: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.binding.tvSender.text = "Sender"+":"+item.sendersEmail
                                        ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DisputeListAdapter.kt:28: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.binding.tvReceiver.text = "Receiver"+":"+item.recieversEmail
                                         ~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DisputeListAdapter.kt:28: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.binding.tvReceiver.text = "Receiver"+":"+item.recieversEmail
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DisputeListAdapter.kt:28: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.binding.tvReceiver.text = "Receiver"+":"+item.recieversEmail
                                          ~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DisputeListAdapter.kt:35: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            holder.binding.tvStatus.text="Safe"
                                          ~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DisputeListAdapter.kt:40: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            holder.binding.tvStatus.text="Unsafe"
                                          ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\DisputeListAdapter.kt:45: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            holder.binding.tvStatus.text="Pending"
                                          ~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DisputeMaillistFragment.kt:142: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.tvSender.text = "From: ${item.sendersEmail}"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DisputeMaillistFragment.kt:142: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvSender.text = "From: ${item.sendersEmail}"
                                 ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DisputeMaillistFragment.kt:143: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.tvReceiver.text = "To: ${item.recieversEmail}"
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DisputeMaillistFragment.kt:143: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvReceiver.text = "To: ${item.recieversEmail}"
                                   ~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DisputeMaillistFragment.kt:144: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.tvStatus.text = "Status: ${item.status}"
                                ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\DisputeMaillistFragment.kt:144: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvStatus.text = "Status: ${item.status}"
                                 ~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:118: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        binding.tvDispute.text="\uFE0F\uD83D\uDEE1\uFE0F "+" "+total_disputes+" Dispute Mails"
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:118: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        binding.tvDispute.text="\uFE0F\uD83D\uDEE1\uFE0F "+" "+total_disputes+" Dispute Mails"
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:118: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        binding.tvDispute.text="\uFE0F\uD83D\uDEE1\uFE0F "+" "+total_disputes+" Dispute Mails"
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:118: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        binding.tvDispute.text="\uFE0F\uD83D\uDEE1\uFE0F "+" "+total_disputes+" Dispute Mails"
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:118: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        binding.tvDispute.text="\uFE0F\uD83D\uDEE1\uFE0F "+" "+total_disputes+" Dispute Mails"
                                                                                               ~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:119: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        binding.tvSpam.text="❗  "+total_spam_emails+" Spam Mails"
                                            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:119: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        binding.tvSpam.text="❗  "+total_spam_emails+" Spam Mails"
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:119: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        binding.tvSpam.text="❗  "+total_spam_emails+" Spam Mails"
                                                                     ~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:120: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        binding.tvProcess.text="⚖\uFE0F "+" "+total_processed_emails+" Process Mails"
                                               ~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:120: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        binding.tvProcess.text="⚖\uFE0F "+" "+total_processed_emails+" Process Mails"
                                               ~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:120: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        binding.tvProcess.text="⚖\uFE0F "+" "+total_processed_emails+" Process Mails"
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:120: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        binding.tvProcess.text="⚖\uFE0F "+" "+total_processed_emails+" Process Mails"
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\HomeFragment.kt:120: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        binding.tvProcess.text="⚖\uFE0F "+" "+total_processed_emails+" Process Mails"
                                                                                      ~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\SpamMailAdapter.kt:26: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.binding.tvSender.text = "Sender"+":"+item.sendersEmail
                                       ~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\SpamMailAdapter.kt:26: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.binding.tvSender.text = "Sender"+":"+item.sendersEmail
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\SpamMailAdapter.kt:26: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.binding.tvSender.text = "Sender"+":"+item.sendersEmail
                                        ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\SpamMailAdapter.kt:27: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.binding.tvReceiver.text = "Receiver"+":"+item.recieversEmail
                                         ~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\SpamMailAdapter.kt:27: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.binding.tvReceiver.text = "Receiver"+":"+item.recieversEmail
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\SpamMailAdapter.kt:27: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.binding.tvReceiver.text = "Receiver"+":"+item.recieversEmail
                                          ~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Adapter\SpamMailAdapter.kt:32: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            holder.binding.tvStatus.text = "Unsafe"
                                            ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\SpamMailFragment.kt:91: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.tvSender.text = "From: ${item.sendersEmail}"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\SpamMailFragment.kt:91: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvSender.text = "From: ${item.sendersEmail}"
                                 ~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\SpamMailFragment.kt:92: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.tvReceiver.text = "To: ${item.recieversEmail}"
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\SpamMailFragment.kt:92: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvReceiver.text = "To: ${item.recieversEmail}"
                                   ~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\SpamMailFragment.kt:93: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.tvStatus.text = "Status: ${item.status}"
                                ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\Fragments\SpamMailFragment.kt:93: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvStatus.text = "Status: ${item.status}"
                                 ~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\BottomSheet\ThemeSettingsBottomSheetFragment.kt:94: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        binding.tvCurrentTheme.text = "Current: $themeName"
                                      ~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\java\com\tech\ekvayu\BottomSheet\ThemeSettingsBottomSheetFragment.kt:94: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        binding.tvCurrentTheme.text = "Current: $themeName"
                                       ~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:20: Warning: Hardcoded string "Email Provider Authentication", should use @string resource [HardcodedText]
            android:text="Email Provider Authentication"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:34: Warning: Hardcoded string "Authenticate with your email providers to access your emails securely using OAuth 2.0", should use @string resource [HardcodedText]
            android:text="Authenticate with your email providers to access your emails securely using OAuth 2.0"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:47: Warning: Hardcoded string "Authenticated providers: 0/3", should use @string resource [HardcodedText]
            android:text="Authenticated providers: 0/3"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:63: Warning: Hardcoded string "Authenticate Gmail", should use @string resource [HardcodedText]
            android:text="Authenticate Gmail"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:78: Warning: Hardcoded string "Authenticate Outlook", should use @string resource [HardcodedText]
            android:text="Authenticate Outlook"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:93: Warning: Hardcoded string "Authenticate Yahoo", should use @string resource [HardcodedText]
            android:text="Authenticate Yahoo"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:117: Warning: Hardcoded string "Authenticating...", should use @string resource [HardcodedText]
            android:text="Authenticating..."
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:132: Warning: Hardcoded string "Check Status", should use @string resource [HardcodedText]
            android:text="Check Status"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:145: Warning: Hardcoded string "Clear All", should use @string resource [HardcodedText]
            android:text="Clear All"
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:159: Warning: Hardcoded string "Features", should use @string resource [HardcodedText]
            android:text="Features"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml:172: Warning: Hardcoded string "✅ OAuth 2.0 Secure Authenticationn✅ Full Email Access (Headers, Body, Attachments)n✅ Raw Email Content (.eml format)n✅ Attachment Downloadn✅ Secure Token Storagen✅ Automatic Token Refreshn✅ Multi-Provider Support", should use @string resource [HardcodedText]
            android:text="✅ OAuth 2.0 Secure Authentication\n✅ Full Email Access (Headers, Body, Attachments)\n✅ Raw Email Content (.eml format)\n✅ Attachment Download\n✅ Secure Token Storage\n✅ Automatic Token Refresh\n✅ Multi-Provider Support"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:14: Warning: Hardcoded string "Email Provider Demo", should use @string resource [HardcodedText]
        android:text="Email Provider Demo"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:30: Warning: Hardcoded string "No providers authenticated", should use @string resource [HardcodedText]
        android:text="No providers authenticated"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:56: Warning: Hardcoded string "Auth", should use @string resource [HardcodedText]
            android:text="Auth"
            ~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:67: Warning: Hardcoded string "Sync All", should use @string resource [HardcodedText]
            android:text="Sync All"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:78: Warning: Hardcoded string "Clear", should use @string resource [HardcodedText]
            android:text="Clear"
            ~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:102: Warning: Hardcoded string "Gmail", should use @string resource [HardcodedText]
            android:text="Gmail"
            ~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:113: Warning: Hardcoded string "Outlook", should use @string resource [HardcodedText]
            android:text="Outlook"
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:124: Warning: Hardcoded string "Yahoo", should use @string resource [HardcodedText]
            android:text="Yahoo"
            ~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:137: Warning: Hardcoded string "Total emails: 0", should use @string resource [HardcodedText]
        android:text="Total emails: 0"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:151: Warning: Hardcoded string "No emails", should use @string resource [HardcodedText]
        android:text="No emails"
        ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml:175: Warning: Hardcoded string "Loading...", should use @string resource [HardcodedText]
        android:text="Loading..."
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:30: Warning: Hardcoded string "← Back", should use @string resource [HardcodedText]
                android:text="← Back"
                ~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:44: Warning: Hardcoded string "Refresh", should use @string resource [HardcodedText]
                android:text="Refresh"
                ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:54: Warning: Hardcoded string "Raw", should use @string resource [HardcodedText]
                android:text="Raw"
                ~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:76: Warning: Hardcoded string "Loading...", should use @string resource [HardcodedText]
            android:text="Loading..."
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:102: Warning: Hardcoded string "Email Subject", should use @string resource [HardcodedText]
                android:text="Email Subject"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:114: Warning: Hardcoded string "From: <EMAIL>", should use @string resource [HardcodedText]
                android:text="From: <EMAIL>"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:124: Warning: Hardcoded string "Date: December 25, 2023 at 10:30", should use @string resource [HardcodedText]
                android:text="Date: December 25, 2023 at 10:30"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:135: Warning: Hardcoded string "To: <EMAIL>", should use @string resource [HardcodedText]
                android:text="To: <EMAIL>"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:148: Warning: Hardcoded string "Provider: Gmail", should use @string resource [HardcodedText]
                android:text="Provider: Gmail"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:161: Warning: Hardcoded string "Read: Yes | Starred: No", should use @string resource [HardcodedText]
                android:text="Read: Yes | Starred: No"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:180: Warning: Hardcoded string "Email Body:", should use @string resource [HardcodedText]
                android:text="Email Body:"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:190: Warning: Hardcoded string "Email body content will appear here...", should use @string resource [HardcodedText]
                android:text="Email body content will appear here..."
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:203: Warning: Hardcoded string "Attachments:ndocument.pdf (1.2 MB)nimage.jpg (500 KB)", should use @string resource [HardcodedText]
                android:text="Attachments:\ndocument.pdf (1.2 MB)\nimage.jpg (500 KB)"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:218: Warning: Hardcoded string "Headers:nMessage-ID: <<EMAIL>>", should use @string resource [HardcodedText]
                android:text="Headers:\nMessage-ID: &lt;<EMAIL>&gt;"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml:234: Warning: Hardcoded string "Raw email content will appear here after downloading...", should use @string resource [HardcodedText]
                android:text="Raw email content will appear here after downloading..."
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_gmail_auth.xml:18: Warning: Hardcoded string "ClickMe", should use @string resource [HardcodedText]
        android:text="ClickMe"/>
        ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_yahoo_auth.xml:19: Warning: Hardcoded string "ClickMe", should use @string resource [HardcodedText]
        android:text="ClickMe"/>
        ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:27: Warning: Hardcoded string "Email Activity Report", should use @string resource [HardcodedText]
            android:text="Email Activity Report"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:41: Warning: Hardcoded string "Latest scan summary threat insights", should use @string resource [HardcodedText]
            android:text="Latest scan summary threat insights"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:86: Warning: Hardcoded string "🟩 0", should use @string resource [HardcodedText]
                    android:text="🟩 0"
                    ~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:95: Warning: Hardcoded string "Dispute", should use @string resource [HardcodedText]
                    android:text="Dispute"
                    ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:119: Warning: Hardcoded string "🟨 0", should use @string resource [HardcodedText]
                    android:text="🟨 0"
                    ~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:129: Warning: Hardcoded string "Spam", should use @string resource [HardcodedText]
                    android:text="Spam"
                    ~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:153: Warning: Hardcoded string "🟥 0", should use @string resource [HardcodedText]
                    android:text="🟥 0"
                    ~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:162: Warning: Hardcoded string "Processed", should use @string resource [HardcodedText]
                    android:text="Processed"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:184: Warning: Hardcoded string "📅 Last Scan: 24 June 2025", should use @string resource [HardcodedText]
                android:text="📅 Last Scan: 24 June 2025"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:195: Warning: Hardcoded string "📈 Highest Risk Day: 21 June (14 Spam)", should use @string resource [HardcodedText]
                android:text="📈 Highest Risk Day: 21 June (14 Spam)"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:206: Warning: Hardcoded string "🔄 Scans this week: 112", should use @string resource [HardcodedText]
                android:text="🔄 Scans this week: 112"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml:214: Warning: Hardcoded string "🧠 AI Decisions: 94% Accurate", should use @string resource [HardcodedText]
                android:text="🧠 AI Decisions: 94% Accurate"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_device_details.xml:24: Warning: Hardcoded string "Device Info", should use @string resource [HardcodedText]
        android:text="Device Info"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_device_details.xml:324: Warning: Hardcoded string "Density", should use @string resource [HardcodedText]
                android:text="Density"
                ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_device_details.xml:408: Warning: Hardcoded string "Package", should use @string resource [HardcodedText]
                android:text="Package"
                ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_device_details.xml:448: Warning: Hardcoded string "Target SDK", should use @string resource [HardcodedText]
                android:text="Target SDK"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_home.xml:37: Warning: Hardcoded string "....", should use @string resource [HardcodedText]
                android:text="...."
                ~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_home.xml:46: Warning: Hardcoded string "...", should use @string resource [HardcodedText]
                android:text="..."
                ~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_home.xml:55: Warning: Hardcoded string "...", should use @string resource [HardcodedText]
                android:text="..."
                ~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_home.xml:81: Warning: Hardcoded string "Mails", should use @string resource [HardcodedText]
        android:text="Mails"
        ~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_raise_bottom_sheet.xml:81: Warning: Hardcoded string "123455453254325", should use @string resource [HardcodedText]
                    android:text="123455453254325"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_raise_bottom_sheet.xml:131: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
                    android:text="<EMAIL>"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_raise_bottom_sheet.xml:188: Warning: Hardcoded string "Pending", should use @string resource [HardcodedText]
                        android:text="Pending"
                        ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_raise_bottom_sheet.xml:235: Warning: Hardcoded string "0 of 3", should use @string resource [HardcodedText]
                        android:text="0 of 3"
                        ~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_raise_bottom_sheet.xml:287: Warning: Hardcoded string "Enter reason....", should use @string resource [HardcodedText]
                    android:hint="Enter reason...."
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_suggetion_bottom.xml:46: Warning: Hardcoded string "Go To Gmail", should use @string resource [HardcodedText]
        android:text="Go To Gmail"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:14: Warning: Hardcoded string "Theme Settings", should use @string resource [HardcodedText]
        android:text="Theme Settings"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:26: Warning: Hardcoded string "Current: System Default", should use @string resource [HardcodedText]
        android:text="Current: System Default"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:37: Warning: Hardcoded string "☀️ Light Mode Active", should use @string resource [HardcodedText]
        android:text="☀️ Light Mode Active"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:49: Warning: Hardcoded string "Choose Theme:", should use @string resource [HardcodedText]
        android:text="Choose Theme:"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:65: Warning: Hardcoded string "☀️ Light Mode", should use @string resource [HardcodedText]
            android:text="☀️ Light Mode"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:75: Warning: Hardcoded string "🌙 Dark Mode", should use @string resource [HardcodedText]
            android:text="🌙 Dark Mode"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:85: Warning: Hardcoded string "🔄 System Default", should use @string resource [HardcodedText]
            android:text="🔄 System Default"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:96: Warning: Hardcoded string "System Default follows your device's theme settings automatically.", should use @string resource [HardcodedText]
        android:text="System Default follows your device's theme settings automatically."
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:113: Warning: Hardcoded string "Close", should use @string resource [HardcodedText]
            android:text="Close"
            ~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml:123: Warning: Hardcoded string "Apply Theme", should use @string resource [HardcodedText]
            android:text="Apply Theme"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_warning_bottom_sheet.xml:44: Warning: Hardcoded string "Mail Access Required", should use @string resource [HardcodedText]
                android:text="Mail Access Required"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\header_layout.xml:37: Warning: Hardcoded string "Ekvayu Tech Pvt Ltd", should use @string resource [HardcodedText]
        android:text="Ekvayu Tech Pvt Ltd"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_dispute_list.xml:33: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
            android:text="<EMAIL>"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_dispute_list.xml:46: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
            android:text="<EMAIL>"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_dispute_list.xml:61: Warning: Hardcoded string "Pending", should use @string resource [HardcodedText]
            android:text="Pending"
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:21: Warning: Hardcoded string "Gmail", should use @string resource [HardcodedText]
            android:text="Gmail"
            ~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:36: Warning: Hardcoded string "John Doe", should use @string resource [HardcodedText]
            android:text="John Doe"
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:52: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
            android:text="<EMAIL>"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:68: Warning: Hardcoded string "Dec 25, 2023 10:30", should use @string resource [HardcodedText]
            android:text="Dec 25, 2023 10:30"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:80: Warning: Hardcoded string "Important Meeting Tomorrow", should use @string resource [HardcodedText]
            android:text="Important Meeting Tomorrow"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:109: Warning: Hardcoded string "Hi team, I wanted to remind everyone about our important meeting scheduled for tomorrow at 2 PM...", should use @string resource [HardcodedText]
            android:text="Hi team, I wanted to remind everyone about our important meeting scheduled for tomorrow at 2 PM..."
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml:147: Warning: Hardcoded string "+3", should use @string resource [HardcodedText]
                android:text="+3"
                ~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_spam_mail.xml:33: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
            android:text="<EMAIL>"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_spam_mail.xml:46: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
            android:text="<EMAIL>"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_spam_mail.xml:61: Warning: Hardcoded string "Pending", should use @string resource [HardcodedText]
            android:text="Pending"
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_dispute_raise.xml:74: Warning: Hardcoded string "123455453254325", should use @string resource [HardcodedText]
                    android:text="123455453254325"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_dispute_raise.xml:124: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
                    android:text="<EMAIL>"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_dispute_raise.xml:181: Warning: Hardcoded string "Pending", should use @string resource [HardcodedText]
                    android:text="Pending"
                    ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_dispute_raise.xml:228: Warning: Hardcoded string "0 of 3", should use @string resource [HardcodedText]
                    android:text="0 of 3"
                    ~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_dispute_raise.xml:280: Warning: Hardcoded string "Enter reason....", should use @string resource [HardcodedText]
                android:hint="Enter reason...."
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_progress_dialog.xml:37: Warning: Hardcoded string "Please wait...", should use @string resource [HardcodedText]
            android:text="Please wait..."
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_spam_detail.xml:35: Warning: Hardcoded string "Sender: ", should use @string resource [HardcodedText]
                    android:text="Sender: "
                    ~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_spam_detail.xml:48: Warning: Hardcoded string "Receiver: ", should use @string resource [HardcodedText]
                    android:text="Receiver: "
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_spam_detail.xml:60: Warning: Hardcoded string "Subject", should use @string resource [HardcodedText]
                    android:text="Subject"
                    ~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_spam_detail.xml:74: Warning: Hardcoded string "Status: Safe", should use @string resource [HardcodedText]
                    android:text="Status: Safe"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_spam_detail.xml:86: Warning: Hardcoded string "Attachment: None", should use @string resource [HardcodedText]
                    android:text="Attachment: None"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_spam_detail.xml:97: Warning: Hardcoded string "Email Body", should use @string resource [HardcodedText]
                    android:text="Email Body"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

4 errors, 324 warnings
