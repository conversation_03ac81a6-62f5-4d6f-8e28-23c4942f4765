<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.0" type="incidents">

    <incident
        id="SuspiciousImport"
        severity="warning"
        message="Don&apos;t include `android.R` here; use a fully qualified name for each usage instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DisputeListAdapter.kt"
            line="3"
            column="1"
            startOffset="33"
            endLine="3"
            endColumn="17"
            endOffset="49"/>
    </incident>

    <incident
        id="SuspiciousImport"
        severity="warning"
        message="Don&apos;t include `android.R` here; use a fully qualified name for each usage instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/SpamMailAdapter.kt"
            line="3"
            column="1"
            startOffset="33"
            endLine="3"
            endColumn="17"
            endOffset="49"/>
    </incident>

    <incident
        id="RecyclerView"
        severity="error"
        message="Do not treat position as fixed; only use immediately and call `holder.getAdapterPosition()` to look it up later">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DashboardMenuAdapter.kt"
            line="25"
            column="59"
            startOffset="987"
            endLine="25"
            endColumn="72"
            endOffset="1000"/>
    </incident>

    <incident
        id="UnknownId"
        severity="fatal"
        message="The id &quot;`ivLogo`&quot; is not defined anywhere.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_layout.xml"
            line="40"
            column="9"
            startOffset="1626"
            endLine="40"
            endColumn="57"
            endOffset="1674"/>
    </incident>

    <incident
        id="ScrollViewSize"
        severity="warning"
        message="This androidx.constraintlayout.widget.ConstraintLayout should use `android:layout_height=&quot;wrap_content&quot;`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="19"
            column="9"
            startOffset="723"
            endLine="19"
            endColumn="45"
            endOffset="759"/>
    </incident>

    <incident
        id="ScrollViewSize"
        severity="warning"
        message="This androidx.constraintlayout.widget.ConstraintLayout should use `android:layout_height=&quot;wrap_content&quot;`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_device_details.xml"
            line="12"
            column="9"
            startOffset="461"
            endLine="12"
            endColumn="45"
            endOffset="497"/>
    </incident>

    <incident
        id="ScrollViewSize"
        severity="warning"
        message="This androidx.constraintlayout.widget.ConstraintLayout should use `android:layout_height=&quot;wrap_content&quot;`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_raise_bottom_sheet.xml"
            line="12"
            column="9"
            startOffset="446"
            endLine="12"
            endColumn="45"
            endOffset="482"/>
    </incident>

    <incident
        id="ScrollViewSize"
        severity="warning"
        message="This androidx.cardview.widget.CardView should use `android:layout_height=&quot;wrap_content&quot;`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_warning_bottom_sheet.xml"
            line="12"
            column="9"
            startOffset="452"
            endLine="12"
            endColumn="45"
            endOffset="488"/>
    </incident>

    <incident
        id="ScrollViewSize"
        severity="warning"
        message="This androidx.constraintlayout.widget.ConstraintLayout should use `android:layout_height=&quot;wrap_content&quot;`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_spam_detail.xml"
            line="9"
            column="9"
            startOffset="313"
            endLine="9"
            endColumn="45"
            endOffset="349"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/mail_raised.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.8.0 is available: 8.11.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.11.0 is difficult: 8.8.2)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.11.0"
                family="Update versions"
                oldString="8.8.0"
                replacement="8.11.0"
                priority="0"/>
            <fix-replace
                description="Change to 8.8.2"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.8.0"
                replacement="8.8.2"
                priority="0"/>
        </fix-alternatives>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-auth than 22.3.1 is available: 23.2.1">
        <fix-replace
            description="Change to 23.2.1"
            family="Update versions"
            oldString="22.3.1"
            replacement="23.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="80"
            column="20"
            startOffset="2098"
            endLine="80"
            endColumn="62"
            endOffset="2140"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.7.0 is available: 33.16.0">
        <fix-replace
            description="Change to 33.16.0"
            family="Update versions"
            oldString="32.7.0"
            replacement="33.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="81"
            column="20"
            startOffset="2161"
            endLine="81"
            endColumn="71"
            endOffset="2212"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="53"
            endLine="4"
            endColumn="19"
            endOffset="61"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="94"
            endLine="6"
            endColumn="23"
            endOffset="101"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="117"
            endLine="7"
            endColumn="23"
            endOffset="124"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="13"
            startOffset="137"
            endLine="8"
            endColumn="20"
            endOffset="144"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.10.0"
            replacement="1.12.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="12"
            startOffset="156"
            endLine="9"
            endColumn="20"
            endOffset="164"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity than 1.8.0 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="12"
            startOffset="176"
            endLine="10"
            endColumn="19"
            endOffset="183"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="20"
            startOffset="203"
            endLine="11"
            endColumn="27"
            endOffset="210"/>
    </incident>

    <incident
        id="SwitchIntDef"
        severity="warning"
        message="Switch statement on an `int` with known associated constant missing case `AccessibilityEvent.TYPE_ANNOUNCEMENT`, `AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT`, `AccessibilityEvent.TYPE_GESTURE_DETECTION_END`, `AccessibilityEvent.TYPE_GESTURE_DETECTION_START`, `AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED`, `AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE`, `AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END`, `AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START`, `AccessibilityEvent.TYPE_TOUCH_INTERACTION_END`, `AccessibilityEvent.TYPE_TOUCH_INTERACTION_START`, `AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED`, `AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED`, `AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED`, `AccessibilityEvent.TYPE_VIEW_FOCUSED`, `AccessibilityEvent.TYPE_VIEW_HOVER_ENTER`, `AccessibilityEvent.TYPE_VIEW_HOVER_EXIT`, `AccessibilityEvent.TYPE_VIEW_LONG_CLICKED`, `AccessibilityEvent.TYPE_VIEW_SCROLLED`, `AccessibilityEvent.TYPE_VIEW_SELECTED`, `AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL`, `AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED`, `AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED`, `AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY`, `AccessibilityEvent.TYPE_WINDOWS_CHANGED`">
        <fix-data cases="android.view.accessibility.AccessibilityEvent.TYPE_ANNOUNCEMENT, android.view.accessibility.AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT, android.view.accessibility.AccessibilityEvent.TYPE_GESTURE_DETECTION_END, android.view.accessibility.AccessibilityEvent.TYPE_GESTURE_DETECTION_START, android.view.accessibility.AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED, android.view.accessibility.AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_INTERACTION_END, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_INTERACTION_START, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_FOCUSED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_HOVER_ENTER, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_HOVER_EXIT, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_LONG_CLICKED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_SCROLLED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_SELECTED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY, android.view.accessibility.AccessibilityEvent.TYPE_WINDOWS_CHANGED"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/EkService/GmailAccessibilityService.kt"
            line="70"
            column="9"
            startOffset="2471"
            endLine="70"
            endColumn="13"
            endOffset="2475"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getString` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/EkService/GmailAccessibilityService.kt"
            line="298"
            column="25"
            startOffset="11239"
            endLine="298"
            endColumn="95"
            endOffset="11309"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getString` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/EkService/NewGmailAccessibilityService.kt"
            line="179"
            column="25"
            startOffset="6627"
            endLine="179"
            endColumn="95"
            endOffset="6697"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/BaseClass/CommonUtil.kt"
            line="57"
            column="13"
            startOffset="1885"
            endLine="57"
            endColumn="59"
            endOffset="1931"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/BaseClass/DeviceInfoHelper.kt"
            line="162"
            column="31"
            startOffset="5914"
            endLine="162"
            endColumn="77"
            endOffset="5960"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/BaseClass/DeviceInfoHelper.kt"
            line="175"
            column="34"
            startOffset="6577"
            endLine="175"
            endColumn="80"
            endOffset="6623"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/BaseClass/DeviceInfoHelper.kt"
            line="300"
            column="33"
            startOffset="11749"
            endLine="300"
            endColumn="79"
            endOffset="11795"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/EkService/GmailAccessibilityService.kt"
            line="763"
            column="20"
            startOffset="31833"
            endLine="763"
            endColumn="66"
            endOffset="31879"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/EkService/GmailAccessibilityService.kt"
            line="771"
            column="13"
            startOffset="32053"
            endLine="771"
            endColumn="59"
            endOffset="32099"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/EkService/NewGmailAccessibilityService.kt"
            line="570"
            column="20"
            startOffset="23801"
            endLine="570"
            endColumn="66"
            endOffset="23847"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/EkService/NewGmailAccessibilityService.kt"
            line="578"
            column="13"
            startOffset="24021"
            endLine="578"
            endColumn="59"
            endOffset="24067"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="121"
            column="10"
            startOffset="5026"
            endLine="121"
            endColumn="22"
            endOffset="5038"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3574 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/argument.xml"
            line="3"
            column="57"
            startOffset="233"
            endLine="3"
            endColumn="3631"
            endOffset="3807"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1569 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/argument.xml"
            line="5"
            column="57"
            startOffset="3876"
            endLine="5"
            endColumn="1626"
            endOffset="5445"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2237 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/argument.xml"
            line="7"
            column="57"
            startOffset="5514"
            endLine="7"
            endColumn="2294"
            endOffset="7751"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1627 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/argument.xml"
            line="9"
            column="57"
            startOffset="7820"
            endLine="9"
            endColumn="1684"
            endOffset="9447"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1477 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/argument.xml"
            line="13"
            column="57"
            startOffset="10014"
            endLine="13"
            endColumn="1534"
            endOffset="11491"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1077 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/argument.xml"
            line="15"
            column="57"
            startOffset="11560"
            endLine="15"
            endColumn="1134"
            endOffset="12637"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1010 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/argument.xml"
            line="17"
            column="57"
            startOffset="12706"
            endLine="17"
            endColumn="1067"
            endOffset="13716"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2297 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/back.xml"
            line="7"
            column="25"
            startOffset="222"
            endLine="7"
            endColumn="2322"
            endOffset="2519"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4948 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="3"
            column="57"
            startOffset="233"
            endLine="3"
            endColumn="5005"
            endOffset="5181"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (9478 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="5"
            column="57"
            startOffset="5250"
            endLine="5"
            endColumn="9535"
            endOffset="14728"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4644 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="7"
            column="57"
            startOffset="14797"
            endLine="7"
            endColumn="4701"
            endOffset="19441"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2448 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="9"
            column="57"
            startOffset="19510"
            endLine="9"
            endColumn="2505"
            endOffset="21958"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1663 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="13"
            column="57"
            startOffset="22475"
            endLine="13"
            endColumn="1720"
            endOffset="24138"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1027 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="15"
            column="57"
            startOffset="24207"
            endLine="15"
            endColumn="1084"
            endOffset="25234"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1380 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="17"
            column="57"
            startOffset="25303"
            endLine="17"
            endColumn="1437"
            endOffset="26683"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (935 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="19"
            column="57"
            startOffset="26752"
            endLine="19"
            endColumn="992"
            endOffset="27687"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1373 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="21"
            column="57"
            startOffset="27756"
            endLine="21"
            endColumn="1430"
            endOffset="29129"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1359 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="27"
            column="57"
            startOffset="30240"
            endLine="27"
            endColumn="1416"
            endOffset="31599"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1253 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="29"
            column="57"
            startOffset="31668"
            endLine="29"
            endColumn="1310"
            endOffset="32921"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1104 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="31"
            column="57"
            startOffset="32990"
            endLine="31"
            endColumn="1161"
            endOffset="34094"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1044 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="35"
            column="57"
            startOffset="34904"
            endLine="35"
            endColumn="1101"
            endOffset="35948"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1036 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="37"
            column="57"
            startOffset="36017"
            endLine="37"
            endColumn="1093"
            endOffset="37053"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1021 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/details.xml"
            line="39"
            column="57"
            startOffset="37122"
            endLine="39"
            endColumn="1078"
            endOffset="38143"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4473 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dispute_.xml"
            line="3"
            column="57"
            startOffset="233"
            endLine="3"
            endColumn="4530"
            endOffset="4706"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1230 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dispute_.xml"
            line="5"
            column="57"
            startOffset="4775"
            endLine="5"
            endColumn="1287"
            endOffset="6005"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1403 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/process.xml"
            line="3"
            column="57"
            startOffset="233"
            endLine="3"
            endColumn="1460"
            endOffset="1636"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1563 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/process.xml"
            line="5"
            column="57"
            startOffset="1705"
            endLine="5"
            endColumn="1620"
            endOffset="3268"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1677 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/process.xml"
            line="7"
            column="57"
            startOffset="3337"
            endLine="7"
            endColumn="1734"
            endOffset="5014"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1720 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/process.xml"
            line="9"
            column="57"
            startOffset="5083"
            endLine="9"
            endColumn="1777"
            endOffset="6803"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2521 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/right_arrow.xml"
            line="3"
            column="57"
            startOffset="233"
            endLine="3"
            endColumn="2578"
            endOffset="2754"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3724 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="3"
            column="57"
            startOffset="233"
            endLine="3"
            endColumn="3781"
            endOffset="3957"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3329 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="5"
            column="57"
            startOffset="4026"
            endLine="5"
            endColumn="3386"
            endOffset="7355"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2023 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="7"
            column="57"
            startOffset="7424"
            endLine="7"
            endColumn="2080"
            endOffset="9447"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2330 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="9"
            column="57"
            startOffset="9516"
            endLine="9"
            endColumn="2387"
            endOffset="11846"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1142 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="13"
            column="57"
            startOffset="12634"
            endLine="13"
            endColumn="1199"
            endOffset="13776"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2518 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="19"
            column="57"
            startOffset="15056"
            endLine="19"
            endColumn="2575"
            endOffset="17574"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1218 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="21"
            column="57"
            startOffset="17643"
            endLine="21"
            endColumn="1275"
            endOffset="18861"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1051 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="23"
            column="57"
            startOffset="18930"
            endLine="23"
            endColumn="1108"
            endOffset="19981"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1019 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="25"
            column="57"
            startOffset="20050"
            endLine="25"
            endColumn="1076"
            endOffset="21069"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1003 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="27"
            column="57"
            startOffset="21138"
            endLine="27"
            endColumn="1060"
            endOffset="22141"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (972 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="31"
            column="57"
            startOffset="22674"
            endLine="31"
            endColumn="1029"
            endOffset="23646"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (959 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spam_mail.xml"
            line="33"
            column="57"
            startOffset="23715"
            endLine="33"
            endColumn="1016"
            endOffset="24674"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="60"
            column="10"
            startOffset="2332"
            endLine="60"
            endColumn="22"
            endOffset="2344"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#F5F5F5` with a theme that also paints a background (inferred theme is `@style/Theme.Ekvayu`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_dashboard.xml"
            line="8"
            column="5"
            startOffset="368"
            endLine="8"
            endColumn="33"
            endOffset="396"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme.Ekvayu`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="43"
            endOffset="379"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/surface` with a theme that also paints a background (inferred theme is `@style/Theme.Ekvayu`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="8"
            column="5"
            startOffset="315"
            endLine="8"
            endColumn="40"
            endOffset="350"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/transparent` with a theme that also paints a background (inferred theme is `@style/Theme.Ekvayu`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_layout.xml"
            line="5"
            column="5"
            startOffset="238"
            endLine="5"
            endColumn="44"
            endOffset="277"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:android"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_raise_bottom_sheet.xml"
            line="15"
            column="9"
            startOffset="597"
            endLine="15"
            endColumn="67"
            endOffset="655"/>
    </incident>

    <incident
        id="UnusedNamespace"
        severity="warning"
        message="Unused namespace declaration xmlns:android; already declared on the root element">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_raise_bottom_sheet.xml"
            line="15"
            column="9"
            startOffset="597"
            endLine="15"
            endColumn="67"
            endOffset="655"/>
    </incident>

    <incident
        id="TypographyDashes"
        severity="warning"
        message="Replace &quot;-&quot; with an &quot;en dash&quot; character (–, &amp;#8211;) ?">
        <fix-replace
            description="Replace with –"
            oldString="-"
            replacement="–"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="37"
            startOffset="616"
            endLine="10"
            endColumn="109"
            endOffset="688"/>
    </incident>

    <incident
        id="IconXmlAndPng"
        severity="warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\drawable\ic_launcher_foreground.xml, src\main\res\mipmap-hdpi\ic_launcher_foreground.webp">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_foreground.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_foreground.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_foreground.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_foreground.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive icon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="269"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive roundIcon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="269"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/device_d.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/device_d.png"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="1595"
                    endOffset="3187"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="1987"
                    endOffset="2389"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="2399"
                    endOffset="2808"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="2818"
                    endOffset="3166"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="51"
            column="10"
            startOffset="1988"
            endLine="51"
            endColumn="16"
            endOffset="1994"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="1595"
                    endOffset="3187"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="1987"
                    endOffset="2389"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="2399"
                    endOffset="2808"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="2818"
                    endOffset="3166"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="62"
            column="10"
            startOffset="2400"
            endLine="62"
            endColumn="16"
            endOffset="2406"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="1595"
                    endOffset="3187"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="1987"
                    endOffset="2389"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="2399"
                    endOffset="2808"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="2818"
                    endOffset="3166"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="73"
            column="10"
            startOffset="2819"
            endLine="73"
            endColumn="16"
            endOffset="2825"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="3228"
                    endOffset="4872"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="3678"
                    endOffset="4058"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="4068"
                    endOffset="4459"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="4469"
                    endOffset="4851"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="97"
            column="10"
            startOffset="3679"
            endLine="97"
            endColumn="16"
            endOffset="3685"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="3228"
                    endOffset="4872"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="3678"
                    endOffset="4058"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="4068"
                    endOffset="4459"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="4469"
                    endOffset="4851"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="108"
            column="10"
            startOffset="4069"
            endLine="108"
            endColumn="16"
            endOffset="4075"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="3228"
                    endOffset="4872"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="3678"
                    endOffset="4058"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="4068"
                    endOffset="4459"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
                    startOffset="4469"
                    endOffset="4851"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="119"
            column="10"
            startOffset="4470"
            endLine="119"
            endColumn="16"
            endOffset="4476"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
                    startOffset="3558"
                    endOffset="4462"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
                    startOffset="3712"
                    endOffset="4077"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
                    startOffset="4087"
                    endOffset="4441"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="108"
            column="10"
            startOffset="3713"
            endLine="108"
            endColumn="16"
            endOffset="3719"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
                    startOffset="3558"
                    endOffset="4462"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
                    startOffset="3712"
                    endOffset="4077"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
                    startOffset="4087"
                    endOffset="4441"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="118"
            column="10"
            startOffset="4088"
            endLine="118"
            endColumn="16"
            endOffset="4094"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="219"
            column="17"
            startOffset="9339"
            endLine="219"
            endColumn="40"
            endOffset="9362"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="235"
            column="17"
            startOffset="10146"
            endLine="235"
            endColumn="40"
            endOffset="10169"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="22"
            column="13"
            startOffset="829"
            endLine="22"
            endColumn="36"
            endOffset="852"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="148"
            column="17"
            startOffset="6165"
            endLine="148"
            endColumn="40"
            endOffset="6188"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_progress_dialog.xml"
            line="38"
            column="13"
            startOffset="1554"
            endLine="38"
            endColumn="35"
            endOffset="1576"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-swiperefreshlayout"
            robot="true">
            <fix-replace
                description="Replace with swiperefreshlayout = &quot;1.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="swiperefreshlayout = &quot;1.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="280"
                    endOffset="280"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-swiperefreshlayout = { module = &quot;androidx.swiperefreshlayout:swiperefreshlayout&quot;, version.ref = &quot;swiperefreshlayout&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-swiperefreshlayout = { module = &quot;androidx.swiperefreshlayout:swiperefreshlayout&quot;, version.ref = &quot;swiperefreshlayout&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="385"
                    endOffset="385"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.swiperefreshlayout"
                robot="true"
                replacement="libs.androidx.swiperefreshlayout"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1428"
                    endOffset="1482"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="65"
            column="20"
            startOffset="1428"
            endLine="65"
            endColumn="74"
            endOffset="1482"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit"
            robot="true">
            <fix-replace
                description="Replace with retrofit = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="211"
                    endOffset="211"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofit&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofit&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1088"
                    endOffset="1088"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit"
                robot="true"
                replacement="libs.retrofit"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1780"
                    endOffset="1819"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="74"
            column="21"
            startOffset="1780"
            endLine="74"
            endColumn="60"
            endOffset="1819"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for converter-gson"
            robot="true">
            <fix-replace
                description="Replace with converterGson = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="converterGson = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGson&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGson&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="385"
                    endOffset="385"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.converter.gson"
                robot="true"
                replacement="libs.converter.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1841"
                    endOffset="1886"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="75"
            column="21"
            startOffset="1841"
            endLine="75"
            endColumn="66"
            endOffset="1886"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for okhttp"
            robot="true">
            <fix-replace
                description="Replace with okhttp = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttp = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="211"
                    endOffset="211"/>
            </fix-replace>
            <fix-replace
                description="Replace with okhttp = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;okhttp&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttp = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;okhttp&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1088"
                    endOffset="1088"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.okhttp"
                robot="true"
                replacement="libs.okhttp"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1908"
                    endOffset="1944"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="76"
            column="21"
            startOffset="1908"
            endLine="76"
            endColumn="57"
            endOffset="1944"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for logging-interceptor"
            robot="true">
            <fix-replace
                description="Replace with loggingInterceptor = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="loggingInterceptor = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="145"
                    endOffset="145"/>
            </fix-replace>
            <fix-replace
                description="Replace with logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptor&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptor&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="764"
                    endOffset="764"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.logging.interceptor"
                robot="true"
                replacement="libs.logging.interceptor"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1966"
                    endOffset="2015"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="77"
            column="21"
            startOffset="1966"
            endLine="77"
            endColumn="70"
            endOffset="2015"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for lottie"
            robot="true">
            <fix-replace
                description="Replace with lottie = &quot;3.4.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lottie = &quot;3.4.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="145"
                    endOffset="145"/>
            </fix-replace>
            <fix-replace
                description="Replace with lottie = { module = &quot;com.airbnb.android:lottie&quot;, version.ref = &quot;lottie&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lottie = { module = &quot;com.airbnb.android:lottie&quot;, version.ref = &quot;lottie&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="764"
                    endOffset="764"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.lottie"
                robot="true"
                replacement="libs.lottie"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2037"
                    endOffset="2070"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="78"
            column="21"
            startOffset="2037"
            endLine="78"
            endColumn="54"
            endOffset="2070"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for firebase-auth"
            robot="true">
            <fix-replace
                description="Replace with firebaseAuth = &quot;22.3.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebaseAuth = &quot;22.3.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with firebase-auth = { module = &quot;com.google.firebase:firebase-auth&quot;, version.ref = &quot;firebaseAuth&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebase-auth = { module = &quot;com.google.firebase:firebase-auth&quot;, version.ref = &quot;firebaseAuth&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="385"
                    endOffset="385"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.firebase.auth"
                robot="true"
                replacement="libs.firebase.auth"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2098"
                    endOffset="2140"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="80"
            column="20"
            startOffset="2098"
            endLine="80"
            endColumn="62"
            endOffset="2140"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for firebase-bom"
            robot="true">
            <fix-replace
                description="Replace with firebaseBom = &quot;32.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebaseBom = &quot;32.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with firebase-bom = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBom&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebase-bom = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBom&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="385"
                    endOffset="385"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.firebase.bom"
                robot="true"
                replacement="libs.firebase.bom"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2161"
                    endOffset="2212"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="81"
            column="20"
            startOffset="2161"
            endLine="81"
            endColumn="71"
            endOffset="2212"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for play-services-auth"
            robot="true">
            <fix-replace
                description="Replace with playServicesAuth = &quot;21.3.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="playServicesAuth = &quot;21.3.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="211"
                    endOffset="211"/>
            </fix-replace>
            <fix-replace
                description="Replace with play-services-auth = { module = &quot;com.google.android.gms:play-services-auth&quot;, version.ref = &quot;playServicesAuth&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="play-services-auth = { module = &quot;com.google.android.gms:play-services-auth&quot;, version.ref = &quot;playServicesAuth&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1088"
                    endOffset="1088"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.play.services.auth"
                robot="true"
                replacement="libs.play.services.auth"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2241"
                    endOffset="2291"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="83"
            column="21"
            startOffset="2241"
            endLine="83"
            endColumn="71"
            endOffset="2291"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for kotlinx-coroutines-android"
            robot="true">
            <fix-replace
                description="Replace with kotlinxCoroutinesAndroid = &quot;1.7.3&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="kotlinxCoroutinesAndroid = &quot;1.7.3&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="145"
                    endOffset="145"/>
            </fix-replace>
            <fix-replace
                description="Replace with kotlinx-coroutines-android = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-android&quot;, version.ref = &quot;kotlinxCoroutinesAndroid&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="kotlinx-coroutines-android = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-android&quot;, version.ref = &quot;kotlinxCoroutinesAndroid&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="764"
                    endOffset="764"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.kotlinx.coroutines.android"
                robot="true"
                replacement="libs.kotlinx.coroutines.android"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2358"
                    endOffset="2414"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="86"
            column="20"
            startOffset="2358"
            endLine="86"
            endColumn="76"
            endOffset="2414"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for kotlinx-coroutines-core"
            robot="true">
            <fix-replace
                description="Replace with kotlinxCoroutinesCore = &quot;1.7.3&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="kotlinxCoroutinesCore = &quot;1.7.3&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="145"
                    endOffset="145"/>
            </fix-replace>
            <fix-replace
                description="Replace with kotlinx-coroutines-core = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-core&quot;, version.ref = &quot;kotlinxCoroutinesCore&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="kotlinx-coroutines-core = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-core&quot;, version.ref = &quot;kotlinxCoroutinesCore&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="764"
                    endOffset="764"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.kotlinx.coroutines.core"
                robot="true"
                replacement="libs.kotlinx.coroutines.core"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2435"
                    endOffset="2488"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="87"
            column="20"
            startOffset="2435"
            endLine="87"
            endColumn="73"
            endOffset="2488"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for mpandroidchart"
            robot="true">
            <fix-replace
                description="Replace with mpandroidchart = &quot;v3.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="mpandroidchart = &quot;v3.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="211"
                    endOffset="211"/>
            </fix-replace>
            <fix-replace
                description="Replace with mpandroidchart = { module = &quot;com.github.PhilJay:MPAndroidChart&quot;, version.ref = &quot;mpandroidchart&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="mpandroidchart = { module = &quot;com.github.PhilJay:MPAndroidChart&quot;, version.ref = &quot;mpandroidchart&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1088"
                    endOffset="1088"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.mpandroidchart"
                robot="true"
                replacement="libs.mpandroidchart"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2517"
                    endOffset="2559"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="89"
            column="21"
            startOffset="2517"
            endLine="89"
            endColumn="63"
            endOffset="2559"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="93"
            column="10"
            startOffset="3722"
            endLine="93"
            endColumn="19"
            endOffset="3731"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="133"
            column="14"
            startOffset="5561"
            endLine="133"
            endColumn="23"
            endOffset="5570"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DeviceDetailsFragment.kt"
            line="118"
            column="33"
            startOffset="4047"
            endLine="118"
            endColumn="63"
            endOffset="4077"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DeviceDetailsFragment.kt"
            line="126"
            column="32"
            startOffset="4410"
            endLine="126"
            endColumn="75"
            endOffset="4453"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DeviceDetailsFragment.kt"
            line="127"
            column="38"
            startOffset="4491"
            endLine="127"
            endColumn="78"
            endOffset="4531"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DeviceDetailsFragment.kt"
            line="128"
            column="37"
            startOffset="4568"
            endLine="128"
            endColumn="83"
            endOffset="4614"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DeviceDetailsFragment.kt"
            line="133"
            column="34"
            startOffset="4793"
            endLine="133"
            endColumn="80"
            endOffset="4839"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DisputeListAdapter.kt"
            line="27"
            column="40"
            startOffset="982"
            endLine="27"
            endColumn="52"
            endOffset="994"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DisputeListAdapter.kt"
            line="27"
            column="40"
            startOffset="982"
            endLine="27"
            endColumn="70"
            endOffset="1012"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DisputeListAdapter.kt"
            line="27"
            column="41"
            startOffset="983"
            endLine="27"
            endColumn="47"
            endOffset="989"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DisputeListAdapter.kt"
            line="28"
            column="42"
            startOffset="1054"
            endLine="28"
            endColumn="56"
            endOffset="1068"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DisputeListAdapter.kt"
            line="28"
            column="42"
            startOffset="1054"
            endLine="28"
            endColumn="76"
            endOffset="1088"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DisputeListAdapter.kt"
            line="28"
            column="43"
            startOffset="1055"
            endLine="28"
            endColumn="51"
            endOffset="1063"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DisputeListAdapter.kt"
            line="35"
            column="43"
            startOffset="1486"
            endLine="35"
            endColumn="47"
            endOffset="1490"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DisputeListAdapter.kt"
            line="40"
            column="43"
            startOffset="1716"
            endLine="40"
            endColumn="49"
            endOffset="1722"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/DisputeListAdapter.kt"
            line="45"
            column="43"
            startOffset="1918"
            endLine="45"
            endColumn="50"
            endOffset="1925"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DisputeMaillistFragment.kt"
            line="142"
            column="33"
            startOffset="6653"
            endLine="142"
            endColumn="61"
            endOffset="6681"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DisputeMaillistFragment.kt"
            line="142"
            column="34"
            startOffset="6654"
            endLine="142"
            endColumn="40"
            endOffset="6660"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DisputeMaillistFragment.kt"
            line="143"
            column="35"
            startOffset="6716"
            endLine="143"
            endColumn="63"
            endOffset="6744"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DisputeMaillistFragment.kt"
            line="143"
            column="36"
            startOffset="6717"
            endLine="143"
            endColumn="40"
            endOffset="6721"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DisputeMaillistFragment.kt"
            line="144"
            column="33"
            startOffset="6777"
            endLine="144"
            endColumn="57"
            endOffset="6801"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/DisputeMaillistFragment.kt"
            line="144"
            column="34"
            startOffset="6778"
            endLine="144"
            endColumn="42"
            endOffset="6786"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="118"
            column="48"
            startOffset="5238"
            endLine="118"
            endColumn="75"
            endOffset="5265"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="118"
            column="48"
            startOffset="5238"
            endLine="118"
            endColumn="79"
            endOffset="5269"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="118"
            column="48"
            startOffset="5238"
            endLine="118"
            endColumn="94"
            endOffset="5284"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="118"
            column="48"
            startOffset="5238"
            endLine="118"
            endColumn="111"
            endOffset="5301"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="118"
            column="96"
            startOffset="5286"
            endLine="118"
            endColumn="110"
            endOffset="5300"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="119"
            column="45"
            startOffset="5346"
            endLine="119"
            endColumn="68"
            endOffset="5369"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="119"
            column="45"
            startOffset="5346"
            endLine="119"
            endColumn="82"
            endOffset="5383"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="119"
            column="70"
            startOffset="5371"
            endLine="119"
            endColumn="81"
            endOffset="5382"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="120"
            column="48"
            startOffset="5431"
            endLine="120"
            endColumn="58"
            endOffset="5441"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="120"
            column="48"
            startOffset="5431"
            endLine="120"
            endColumn="62"
            endOffset="5445"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="120"
            column="48"
            startOffset="5431"
            endLine="120"
            endColumn="85"
            endOffset="5468"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="120"
            column="48"
            startOffset="5431"
            endLine="120"
            endColumn="102"
            endOffset="5485"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/HomeFragment.kt"
            line="120"
            column="87"
            startOffset="5470"
            endLine="120"
            endColumn="101"
            endOffset="5484"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/SpamMailAdapter.kt"
            line="26"
            column="40"
            startOffset="961"
            endLine="26"
            endColumn="52"
            endOffset="973"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/SpamMailAdapter.kt"
            line="26"
            column="40"
            startOffset="961"
            endLine="26"
            endColumn="70"
            endOffset="991"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/SpamMailAdapter.kt"
            line="26"
            column="41"
            startOffset="962"
            endLine="26"
            endColumn="47"
            endOffset="968"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/SpamMailAdapter.kt"
            line="27"
            column="42"
            startOffset="1033"
            endLine="27"
            endColumn="56"
            endOffset="1047"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/SpamMailAdapter.kt"
            line="27"
            column="42"
            startOffset="1033"
            endLine="27"
            endColumn="76"
            endOffset="1067"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/SpamMailAdapter.kt"
            line="27"
            column="43"
            startOffset="1034"
            endLine="27"
            endColumn="51"
            endOffset="1042"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Adapter/SpamMailAdapter.kt"
            line="32"
            column="45"
            startOffset="1159"
            endLine="32"
            endColumn="51"
            endOffset="1165"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/SpamMailFragment.kt"
            line="91"
            column="33"
            startOffset="3388"
            endLine="91"
            endColumn="61"
            endOffset="3416"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/SpamMailFragment.kt"
            line="91"
            column="34"
            startOffset="3389"
            endLine="91"
            endColumn="40"
            endOffset="3395"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/SpamMailFragment.kt"
            line="92"
            column="35"
            startOffset="3451"
            endLine="92"
            endColumn="63"
            endOffset="3479"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/SpamMailFragment.kt"
            line="92"
            column="36"
            startOffset="3452"
            endLine="92"
            endColumn="40"
            endOffset="3456"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/SpamMailFragment.kt"
            line="93"
            column="33"
            startOffset="3512"
            endLine="93"
            endColumn="57"
            endOffset="3536"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Fragments/SpamMailFragment.kt"
            line="93"
            column="34"
            startOffset="3513"
            endLine="93"
            endColumn="42"
            endOffset="3521"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/BottomSheet/ThemeSettingsBottomSheetFragment.kt"
            line="94"
            column="39"
            startOffset="3372"
            endLine="94"
            endColumn="60"
            endOffset="3393"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/BottomSheet/ThemeSettingsBottomSheetFragment.kt"
            line="94"
            column="40"
            startOffset="3373"
            endLine="94"
            endColumn="49"
            endOffset="3382"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email Provider Authentication&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="20"
            column="13"
            startOffset="739"
            endLine="20"
            endColumn="57"
            endOffset="783"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Authenticate with your email providers to access your emails securely using OAuth 2.0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="34"
            column="13"
            startOffset="1309"
            endLine="34"
            endColumn="113"
            endOffset="1409"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Authenticated providers: 0/3&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="47"
            column="13"
            startOffset="1917"
            endLine="47"
            endColumn="56"
            endOffset="1960"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Authenticate Gmail&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="63"
            column="13"
            startOffset="2594"
            endLine="63"
            endColumn="46"
            endOffset="2627"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Authenticate Outlook&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="78"
            column="13"
            startOffset="3244"
            endLine="78"
            endColumn="48"
            endOffset="3279"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Authenticate Yahoo&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="93"
            column="13"
            startOffset="3892"
            endLine="93"
            endColumn="46"
            endOffset="3925"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Authenticating...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="117"
            column="13"
            startOffset="4911"
            endLine="117"
            endColumn="45"
            endOffset="4943"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Check Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="132"
            column="13"
            startOffset="5521"
            endLine="132"
            endColumn="40"
            endOffset="5548"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Clear All&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="145"
            column="13"
            startOffset="6083"
            endLine="145"
            endColumn="37"
            endOffset="6107"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Features&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="159"
            column="13"
            startOffset="6689"
            endLine="159"
            endColumn="36"
            endOffset="6712"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;✅ OAuth 2.0 Secure Authentication\n✅ Full Email Access (Headers, Body, Attachments)\n✅ Raw Email Content (.eml format)\n✅ Attachment Download\n✅ Secure Token Storage\n✅ Automatic Token Refresh\n✅ Multi-Provider Support&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="172"
            column="13"
            startOffset="7224"
            endLine="172"
            endColumn="246"
            endOffset="7457"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email Provider Demo&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="14"
            column="9"
            startOffset="547"
            endLine="14"
            endColumn="43"
            endOffset="581"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;No providers authenticated&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="30"
            column="9"
            startOffset="1161"
            endLine="30"
            endColumn="50"
            endOffset="1202"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Auth&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="56"
            column="13"
            startOffset="2172"
            endLine="56"
            endColumn="32"
            endOffset="2191"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Sync All&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="67"
            column="13"
            startOffset="2580"
            endLine="67"
            endColumn="36"
            endOffset="2603"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Clear&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="78"
            column="13"
            startOffset="3003"
            endLine="78"
            endColumn="33"
            endOffset="3023"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Gmail&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="102"
            column="13"
            startOffset="3861"
            endLine="102"
            endColumn="33"
            endOffset="3881"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Outlook&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="113"
            column="13"
            startOffset="4253"
            endLine="113"
            endColumn="35"
            endOffset="4275"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Yahoo&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="124"
            column="13"
            startOffset="4652"
            endLine="124"
            endColumn="33"
            endOffset="4672"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Total emails: 0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="137"
            column="9"
            startOffset="5042"
            endLine="137"
            endColumn="39"
            endOffset="5072"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;No emails&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="151"
            column="9"
            startOffset="5565"
            endLine="151"
            endColumn="33"
            endOffset="5589"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="175"
            column="9"
            startOffset="6472"
            endLine="175"
            endColumn="34"
            endOffset="6497"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;← Back&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="30"
            column="17"
            startOffset="1195"
            endLine="30"
            endColumn="38"
            endOffset="1216"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Refresh&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="44"
            column="17"
            startOffset="1712"
            endLine="44"
            endColumn="39"
            endOffset="1734"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Raw&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="54"
            column="17"
            startOffset="2131"
            endLine="54"
            endColumn="35"
            endOffset="2149"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="76"
            column="13"
            startOffset="2937"
            endLine="76"
            endColumn="38"
            endOffset="2962"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email Subject&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="102"
            column="17"
            startOffset="4018"
            endLine="102"
            endColumn="45"
            endOffset="4046"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;From: <EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="114"
            column="17"
            startOffset="4505"
            endLine="114"
            endColumn="56"
            endOffset="4544"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Date: December 25, 2023 at 10:30&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="124"
            column="17"
            startOffset="4930"
            endLine="124"
            endColumn="64"
            endOffset="4977"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;To: <EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="135"
            column="17"
            startOffset="5422"
            endLine="135"
            endColumn="57"
            endOffset="5462"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Provider: Gmail&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="148"
            column="17"
            startOffset="6000"
            endLine="148"
            endColumn="47"
            endOffset="6030"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Read: Yes | Starred: No&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="161"
            column="17"
            startOffset="6533"
            endLine="161"
            endColumn="55"
            endOffset="6571"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email Body:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="180"
            column="17"
            startOffset="7327"
            endLine="180"
            endColumn="43"
            endOffset="7353"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email body content will appear here...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="190"
            column="17"
            startOffset="7731"
            endLine="190"
            endColumn="70"
            endOffset="7784"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Attachments:\ndocument.pdf (1.2 MB)\nimage.jpg (500 KB)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="203"
            column="17"
            startOffset="8510"
            endLine="203"
            endColumn="87"
            endOffset="8580"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Headers:\nMessage-ID: &lt;<EMAIL>>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="218"
            column="17"
            startOffset="9259"
            endLine="218"
            endColumn="80"
            endOffset="9322"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Raw email content will appear here after downloading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="234"
            column="17"
            startOffset="10059"
            endLine="234"
            endColumn="87"
            endOffset="10129"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;ClickMe&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gmail_auth.xml"
            line="18"
            column="9"
            startOffset="795"
            endLine="18"
            endColumn="31"
            endOffset="817"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;ClickMe&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_yahoo_auth.xml"
            line="19"
            column="9"
            startOffset="796"
            endLine="19"
            endColumn="31"
            endOffset="818"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email Activity Report&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="27"
            column="13"
            startOffset="980"
            endLine="27"
            endColumn="49"
            endOffset="1016"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Latest scan summary threat insights&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="41"
            column="13"
            startOffset="1540"
            endLine="41"
            endColumn="63"
            endOffset="1590"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🟩 0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="86"
            column="21"
            startOffset="3374"
            endLine="86"
            endColumn="40"
            endOffset="3393"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Dispute&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="95"
            column="21"
            startOffset="3759"
            endLine="95"
            endColumn="43"
            endOffset="3781"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🟨 0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="119"
            column="21"
            startOffset="4644"
            endLine="119"
            endColumn="40"
            endOffset="4663"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Spam&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="129"
            column="21"
            startOffset="5050"
            endLine="129"
            endColumn="40"
            endOffset="5069"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🟥 0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="153"
            column="21"
            startOffset="5940"
            endLine="153"
            endColumn="40"
            endOffset="5959"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Processed&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="162"
            column="21"
            startOffset="6326"
            endLine="162"
            endColumn="45"
            endOffset="6350"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📅 Last Scan: 24 June 2025&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="184"
            column="17"
            startOffset="7056"
            endLine="184"
            endColumn="58"
            endOffset="7097"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📈 Highest Risk Day: 21 June (14 Spam)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="195"
            column="17"
            startOffset="7468"
            endLine="195"
            endColumn="70"
            endOffset="7521"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔄 Scans this week: 112&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="206"
            column="17"
            startOffset="7892"
            endLine="206"
            endColumn="55"
            endOffset="7930"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🧠 AI Decisions: 94% Accurate&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_activity_graph.xml"
            line="214"
            column="17"
            startOffset="8221"
            endLine="214"
            endColumn="61"
            endOffset="8265"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Device Info&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_device_details.xml"
            line="24"
            column="9"
            startOffset="887"
            endLine="24"
            endColumn="35"
            endOffset="913"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Density&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_device_details.xml"
            line="324"
            column="17"
            startOffset="11169"
            endLine="324"
            endColumn="39"
            endOffset="11191"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Package&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_device_details.xml"
            line="408"
            column="17"
            startOffset="14100"
            endLine="408"
            endColumn="39"
            endOffset="14122"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Target SDK&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_device_details.xml"
            line="448"
            column="17"
            startOffset="15375"
            endLine="448"
            endColumn="42"
            endOffset="15400"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;....&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="37"
            column="17"
            startOffset="1590"
            endLine="37"
            endColumn="36"
            endOffset="1609"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="46"
            column="17"
            startOffset="1994"
            endLine="46"
            endColumn="35"
            endOffset="2012"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="55"
            column="17"
            startOffset="2400"
            endLine="55"
            endColumn="35"
            endOffset="2418"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Mails&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="81"
            column="9"
            startOffset="3327"
            endLine="81"
            endColumn="29"
            endOffset="3347"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;123455453254325&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_raise_bottom_sheet.xml"
            line="81"
            column="21"
            startOffset="3437"
            endLine="81"
            endColumn="51"
            endOffset="3467"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;<EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_raise_bottom_sheet.xml"
            line="131"
            column="21"
            startOffset="5450"
            endLine="131"
            endColumn="50"
            endOffset="5479"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Pending&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_raise_bottom_sheet.xml"
            line="188"
            column="25"
            startOffset="7847"
            endLine="188"
            endColumn="47"
            endOffset="7869"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0 of 3&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_raise_bottom_sheet.xml"
            line="235"
            column="25"
            startOffset="9906"
            endLine="235"
            endColumn="46"
            endOffset="9927"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter reason....&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_raise_bottom_sheet.xml"
            line="287"
            column="21"
            startOffset="11996"
            endLine="287"
            endColumn="52"
            endOffset="12027"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Go To Gmail&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_suggetion_bottom.xml"
            line="46"
            column="9"
            startOffset="1973"
            endLine="46"
            endColumn="35"
            endOffset="1999"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Theme Settings&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="14"
            column="9"
            startOffset="484"
            endLine="14"
            endColumn="38"
            endOffset="513"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Current: System Default&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="26"
            column="9"
            startOffset="893"
            endLine="26"
            endColumn="47"
            endOffset="931"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;☀️ Light Mode Active&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="37"
            column="9"
            startOffset="1273"
            endLine="37"
            endColumn="44"
            endOffset="1308"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Choose Theme:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="49"
            column="9"
            startOffset="1687"
            endLine="49"
            endColumn="37"
            endOffset="1715"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;☀️ Light Mode&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="65"
            column="13"
            startOffset="2235"
            endLine="65"
            endColumn="41"
            endOffset="2263"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🌙 Dark Mode&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="75"
            column="13"
            startOffset="2604"
            endLine="75"
            endColumn="40"
            endOffset="2631"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔄 System Default&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="85"
            column="13"
            startOffset="2974"
            endLine="85"
            endColumn="45"
            endOffset="3006"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;System Default follows your device&apos;s theme settings automatically.&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="96"
            column="9"
            startOffset="3289"
            endLine="96"
            endColumn="90"
            endOffset="3370"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Close&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="113"
            column="13"
            startOffset="3897"
            endLine="113"
            endColumn="33"
            endOffset="3917"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Apply Theme&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_theme_settings_bottom_sheet.xml"
            line="123"
            column="13"
            startOffset="4272"
            endLine="123"
            endColumn="39"
            endOffset="4298"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Mail Access Required&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_warning_bottom_sheet.xml"
            line="44"
            column="17"
            startOffset="1687"
            endLine="44"
            endColumn="52"
            endOffset="1722"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Ekvayu Tech Pvt Ltd&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_layout.xml"
            line="37"
            column="9"
            startOffset="1478"
            endLine="37"
            endColumn="43"
            endOffset="1512"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;<EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_dispute_list.xml"
            line="33"
            column="13"
            startOffset="1393"
            endLine="33"
            endColumn="46"
            endOffset="1426"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;<EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_dispute_list.xml"
            line="46"
            column="13"
            startOffset="1988"
            endLine="46"
            endColumn="46"
            endOffset="2021"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Pending&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_dispute_list.xml"
            line="61"
            column="13"
            startOffset="2632"
            endLine="61"
            endColumn="35"
            endOffset="2654"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Gmail&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="21"
            column="13"
            startOffset="796"
            endLine="21"
            endColumn="33"
            endOffset="816"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;John Doe&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="36"
            column="13"
            startOffset="1391"
            endLine="36"
            endColumn="36"
            endOffset="1414"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;<EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="52"
            column="13"
            startOffset="2019"
            endLine="52"
            endColumn="48"
            endOffset="2054"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Dec 25, 2023 10:30&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="68"
            column="13"
            startOffset="2682"
            endLine="68"
            endColumn="46"
            endOffset="2715"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Important Meeting Tomorrow&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="80"
            column="13"
            startOffset="3161"
            endLine="80"
            endColumn="54"
            endOffset="3202"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Hi team, I wanted to remind everyone about our important meeting scheduled for tomorrow at 2 PM...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="109"
            column="13"
            startOffset="4347"
            endLine="109"
            endColumn="126"
            endOffset="4460"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;+3&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="147"
            column="17"
            startOffset="6131"
            endLine="147"
            endColumn="34"
            endOffset="6148"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;<EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_spam_mail.xml"
            line="33"
            column="13"
            startOffset="1393"
            endLine="33"
            endColumn="46"
            endOffset="1426"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;<EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_spam_mail.xml"
            line="46"
            column="13"
            startOffset="1988"
            endLine="46"
            endColumn="46"
            endOffset="2021"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Pending&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_spam_mail.xml"
            line="61"
            column="13"
            startOffset="2632"
            endLine="61"
            endColumn="35"
            endOffset="2654"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;123455453254325&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_dispute_raise.xml"
            line="74"
            column="21"
            startOffset="3208"
            endLine="74"
            endColumn="51"
            endOffset="3238"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;<EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_dispute_raise.xml"
            line="124"
            column="21"
            startOffset="5268"
            endLine="124"
            endColumn="50"
            endOffset="5297"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Pending&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_dispute_raise.xml"
            line="181"
            column="21"
            startOffset="7569"
            endLine="181"
            endColumn="43"
            endOffset="7591"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0 of 3&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_dispute_raise.xml"
            line="228"
            column="21"
            startOffset="9522"
            endLine="228"
            endColumn="42"
            endOffset="9543"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter reason....&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_dispute_raise.xml"
            line="280"
            column="17"
            startOffset="11503"
            endLine="280"
            endColumn="48"
            endOffset="11534"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Please wait...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_progress_dialog.xml"
            line="37"
            column="13"
            startOffset="1511"
            endLine="37"
            endColumn="42"
            endOffset="1540"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Sender: &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_spam_detail.xml"
            line="35"
            column="21"
            startOffset="1465"
            endLine="35"
            endColumn="44"
            endOffset="1488"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Receiver: &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_spam_detail.xml"
            line="48"
            column="21"
            startOffset="2084"
            endLine="48"
            endColumn="46"
            endOffset="2109"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Subject&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_spam_detail.xml"
            line="60"
            column="21"
            startOffset="2666"
            endLine="60"
            endColumn="43"
            endOffset="2688"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Status: Safe&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_spam_detail.xml"
            line="74"
            column="21"
            startOffset="3355"
            endLine="74"
            endColumn="48"
            endOffset="3382"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Attachment: None&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_spam_detail.xml"
            line="86"
            column="21"
            startOffset="3947"
            endLine="86"
            endColumn="52"
            endOffset="3978"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Email Body&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_spam_detail.xml"
            line="97"
            column="21"
            startOffset="4484"
            endLine="97"
            endColumn="46"
            endOffset="4509"/>
    </incident>

</incidents>
