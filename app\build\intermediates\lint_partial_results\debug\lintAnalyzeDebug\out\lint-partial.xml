<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.0" type="partial_results">
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.settings.ACCESSIBILITY_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/Activities/MainActivity.kt"
                            line="38"
                            column="22"
                            startOffset="1129"
                            endLine="38"
                            endColumn="68"
                            endOffset="1175"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/BottomSheet/WarningBottomSheetFragment.kt"
                            line="48"
                            column="22"
                            startOffset="1593"
                            endLine="48"
                            endColumn="68"
                            endOffset="1639"/>
                    </map>
                    <map id="android.settings.action.MANAGE_OVERLAY_PERMISSION (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/EkService/GmailAccessibilityService.kt"
                            line="773"
                            column="30"
                            startOffset="32186"
                            endLine="776"
                            endColumn="18"
                            endOffset="32336"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/tech/ekvayu/EkService/NewGmailAccessibilityService.kt"
                            line="580"
                            column="30"
                            startOffset="24154"
                            endLine="583"
                            endColumn="18"
                            endOffset="24304"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.tech.ekvayu.Activities.DashboardActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="17"
            column="12"
            startOffset="548"
            endLine="17"
            endColumn="23"
            endOffset="559"/>
        <location id="R.color.border"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="51"
            column="12"
            startOffset="2082"
            endLine="51"
            endColumn="25"
            endOffset="2095"/>
        <location id="R.color.border_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="35"
            column="12"
            startOffset="1315"
            endLine="35"
            endColumn="30"
            endOffset="1333"/>
        <location id="R.color.border_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="26"
            column="12"
            startOffset="931"
            endLine="26"
            endColumn="31"
            endOffset="950"/>
        <location id="R.color.divider"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="50"
            column="12"
            startOffset="2027"
            endLine="50"
            endColumn="26"
            endOffset="2041"/>
        <location id="R.color.divider_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="34"
            column="12"
            startOffset="1268"
            endLine="34"
            endColumn="31"
            endOffset="1287"/>
        <location id="R.color.divider_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="25"
            column="12"
            startOffset="883"
            endLine="25"
            endColumn="32"
            endOffset="903"/>
        <location id="R.color.orange"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="16"
            column="12"
            startOffset="507"
            endLine="16"
            endColumn="25"
            endOffset="520"/>
        <location id="R.color.transparent_black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="38"
            column="12"
            startOffset="1418"
            endLine="38"
            endColumn="36"
            endOffset="1442"/>
        <location id="R.drawable.bg_anim_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_anim_button.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="8"
            endColumn="12"
            endOffset="278"/>
        <location id="R.drawable.bg_search_bar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_search_bar.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="7"
            endColumn="9"
            endOffset="282"/>
        <location id="R.drawable.bg_status"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_status.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="9"
            endOffset="225"/>
        <location id="R.drawable.device_d"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/device_d.png"/>
        <location id="R.drawable.device_details"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/device_details.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="12"
            endColumn="10"
            endOffset="846"/>
        <location id="R.drawable.dispute"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dispute.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="62"
            endColumn="10"
            endOffset="2153"/>
        <location id="R.drawable.dispute_"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dispute_.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="6384"/>
        <location id="R.drawable.dispute_mails"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dispute_mails.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="365"/>
        <location id="R.drawable.edit_text_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/edit_text_bg.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="9"
            endOffset="253"/>
        <location id="R.drawable.ic_attachment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_attachment.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="10"
            endOffset="618"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="170"
            endColumn="10"
            endOffset="5605"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="1702"/>
        <location id="R.drawable.right_arrow"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/right_arrow.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="5"
            endColumn="10"
            endOffset="2774"/>
        <location id="R.drawable.theme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/theme.png"/>
        <location id="R.layout.activity_email_auth"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_auth.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="182"
            endColumn="14"
            endOffset="7856"/>
        <location id="R.layout.activity_email_demo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_demo.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="207"
            endColumn="53"
            endOffset="7803"/>
        <location id="R.layout.activity_email_detail"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_detail.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="248"
            endColumn="14"
            endOffset="10711"/>
        <location id="R.layout.activity_gmail_auth"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_gmail_auth.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="22"
            endColumn="53"
            endOffset="875"/>
        <location id="R.layout.item_email"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_email.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="161"
            endColumn="37"
            endOffset="6647"/>
        <location id="R.layout.layout_dispute_raise"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_dispute_raise.xml"
            line="3"
            column="5"
            startOffset="46"
            endLine="319"
            endColumn="53"
            endOffset="12877"/>
        <location id="R.raw.raw"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/raw"/>
        <location id="R.string.accessibility_service_gmail"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="118"
            endLine="4"
            endColumn="47"
            endOffset="152"/>
        <location id="R.string.appAuthRedirectScheme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="11"
            column="13"
            startOffset="710"
            endLine="11"
            endColumn="41"
            endOffset="738"/>
        <location id="R.string.basic_information"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="1628"
            endLine="24"
            endColumn="37"
            endOffset="1652"/>
        <location id="R.string.continuee"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="545"
            endLine="9"
            endColumn="29"
            endOffset="561"/>
        <location id="R.string.gmail_auth"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="808"
            endLine="12"
            endColumn="30"
            endOffset="825"/>
        <location id="R.string.google_client_id"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="592"
            endLine="10"
            endColumn="36"
            endOffset="615"/>
        <location id="R.string.hello_blank_fragment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="13"
            startOffset="1336"
            endLine="18"
            endColumn="40"
            endOffset="1363"/>
        <location id="R.string.manufacturer_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="1692"
            endLine="25"
            endColumn="37"
            endOffset="1716"/>
        <location id="R.string.refresh"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="22"
            column="13"
            startOffset="1528"
            endLine="22"
            endColumn="27"
            endOffset="1542"/>
        <location id="R.string.safe"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="13"
            startOffset="1406"
            endLine="19"
            endColumn="24"
            endOffset="1417"/>
        <location id="R.string.unsafe"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="1444"
            endLine="20"
            endColumn="26"
            endOffset="1457"/>
        <location id="R.string.yahoo_auth"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="13"
            column="13"
            startOffset="858"
            endLine="13"
            endColumn="30"
            endOffset="875"/>
        <location id="R.style.ButtonPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="50"
            column="12"
            startOffset="2028"
            endLine="50"
            endColumn="32"
            endOffset="2048"/>
        <location id="R.style.ButtonSecondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="55"
            column="12"
            startOffset="2227"
            endLine="55"
            endColumn="34"
            endOffset="2249"/>
        <location id="R.style.CardStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="43"
            column="12"
            startOffset="1789"
            endLine="43"
            endColumn="28"
            endOffset="1805"/>
        <entry
            name="model"
            string="attr[colorOnSurface(E),selectableItemBackground(R)],color[black(U),white(U),transparent_black_60(U),app_color(U),grey(U),green(U),dark_grey(U),light_grey(U),background(U),surface(U),text_primary(U),text_secondary(U),card_background(U),transparent(U),ic_launcher_background(U),app_color_light(U),app_color_dark(U),red(U),orange(D),blue(D),background_light(U),surface_light(U),card_background_light(U),text_primary_light(U),text_secondary_light(U),divider_light(D),border_light(D),background_dark(U),surface_dark(U),card_background_dark(U),text_primary_dark(U),text_secondary_dark(U),divider_dark(D),border_dark(D),transparent_black(D),divider(D),border(D)],dimen[_20sdp(R),_10sdp(R),_100sdp(R),_12sdp(R),_15sdp(R),_11sdp(R),_200sdp(R),_16sdp(R),_30sdp(R),_5sdp(R),_8sdp(R),_9sdp(R),_120sdp(R),_50sdp(R),_35sdp(R),_13sdp(R),_24sdp(R),_14sdp(R),_6sdp(R),_40sdp(R),_26sdp(R),_2sdp(R)],drawable[activity(U),argument(U),back(U),bg_alert_dialog(U),bg_anim_button(D),bg_button_app_color(U),bg_button_grey_color(U),bg_button_white(U),bg_dialog(U),bg_search_bar(D),bg_status(D),details(U),device_d(D),device_details(D),dispute(D),dispute_(D),dispute_mails(D),edit_text_bg(D),ic_attachment(D),ic_launcher_background(D),ic_launcher_foreground(D),ic_launcher_foreground_1(E),mail_raised(U),process(U),right_arrow(D),spam_mail(U),table_background(U),theme_icon(U),icon_secure_phishing(U),theme(D)],font[roboto_semi_medium(R),roboto_semi_regular(R),roboto_semi_bold(R)],id[main(U),header(U),fragmentContainer(U),tv_title(D),tv_description(D),tv_auth_status(D),btn_gmail_auth(D),btn_outlook_auth(D),btn_yahoo_auth(D),progress_bar(D),tv_progress(D),btn_check_status(D),btn_clear_auth(D),tv_features_title(D),tv_features(D),layout_controls(D),btn_authenticate(D),btn_sync_all(D),btn_clear_emails(D),layout_provider_sync(D),btn_sync_gmail(D),btn_sync_outlook(D),btn_sync_yahoo(D),tv_email_count(D),tv_email_details(D),swipe_refresh(D),recycler_view_emails(D),layout_header(D),btn_back(D),btn_refresh(D),btn_download_raw(D),layout_content(D),tv_subject(D),tv_from(D),tv_date(D),tv_recipients(D),tv_provider(D),tv_status(D),tv_body(D),tv_attachments(D),tv_headers(D),tv_raw_content(D),btAuth(U),ivIcon(U),tvContent(D),btPermission(U),tvHeader(U),tvSubTitle(U),pieChart(U),summaryCards(U),tvDispute(U),tvSpam(U),tvProcessed(U),tvTitle(U),tlDeviceInfo(U),tvDeviceName(U),tvManufacturer(U),tvModelName(U),tvBradName(U),tvProductName(U),tvTitleAndroid(U),tlAndroidInfo(U),tvAndroidVersion(U),tvApiLevel(U),tvBuildNumber(U),tvSerialNumber(U),tvAndroidId(U),tvScreenInfo(U),tlScreenInfo(U),tvResolution(U),tvDensity(U),tvDensityFactor(U),tvScaleDensity(U),tvAppInfo(U),tlAppInfo(D),tvPackage(U),tvVersion(U),tvTargetSdk(U),tvMinSdk(U),rvDisputelist(U),btRaiseDispute(U),crTop(U),llMain(D),tvProcess(U),tvMails(U),rvMenu(U),clMain(U),ivDisputeLogo(U),tvDisputeHeading(U),cvMessageId(U),tvMessageId(D),etMessageId(U),cvSenderEmail(U),tvTitleSenderEmail(D),etSenderMail(U),llMainPending(U),cvStatus(D),tvStatusTitle(D),etStatus(U),cvCounter(D),tvTitleStatus(D),etCounter(U),cvReason(U),tvTitleReason(D),etReason(U),btSubmit(U),rvSpamMail(U),tvSuggetion(U),btGotoGmail(U),tvCurrentTheme(U),tvThemePreview(U),rgThemeOptions(U),rbLight(U),rbDark(U),rbSystem(U),btnClose(U),btnApply(U),tvMailAccess(U),cvBack(U),ivBack(D),ivLogo(U),cvTheme(U),ivTheme(D),cvMain(U),tvMenuName(U),tvDescripton(U),tvSender(U),tvStatus(U),tvReceiver(U),tv_sender(D),tv_sender_email(D),iv_star(D),tv_body_preview(D),iv_attachment(D),tv_recipient_count(D),etHashId(D),etBotMail(D),etBotStatus(D),etBotCounter(D),etBotReason(D),lvWarning(U),progressBar(U),tvMessage(U),cvMailItem(D),clContainer(D),tvSubject(U),tvAttachment(U),tvBody(U),btClose(U)],integer[google_play_services_version(U)],layout[activity_dashboard(U),header_layout(U),activity_email_auth(D),activity_email_demo(D),item_email(D),activity_email_detail(D),activity_gmail_auth(D),activity_main(U),activity_yahoo_auth(U),fragment_activity_graph(U),fragment_device_details(U),fragment_dispute_maillist(U),fragment_home(U),fragment_raise_bottom_sheet(U),fragment_spam_mail(U),fragment_suggetion_bottom(U),fragment_theme_settings_bottom_sheet(U),fragment_warning_bottom_sheet(U),item_dashboard_menu(U),item_dispute_list(U),item_spam_mail(U),layout_dispute_raise(D),layout_progress(U),layout_progress_dialog(U),layout_spam_detail(U),layout_validation(U)],mipmap[ic_launcher(U),ic_launcher_round(U),ic_launcher_foreground(U)],raw[ai_loading(U),wait(U),raw(D),safe(U),unsafe(U)],string[app_name(U),secure_my_app_from_phishing(U),permission(U),device_name(U),manufacturer(U),model_name(U),brand_name(U),product(U),android_info(U),android_version(U),api_level(U),build_number(U),serial_number(U),android_id(U),screen_info(U),resolution(U),density_factor(U),scaled_density(U),app_info(U),version(U),min_sdk(U),raise_dispute(U),get_stared_raised_disputes_against(U),message_id(U),sender_email(U),status(U),counter(U),reason_of_raising_dispute(U),submit(U),i_am_facing_difficulties_retrieving_the_recipient_s_email_id_could_you_kindly_enable_the_service_and_assist_in_verifying_the_mail_for_a_timely_resolution(U),to_extract_email_details_securely(U),device_details(U),warning_this_email_is_unsafe_proceed_with_caution(U),close(U),accessibility_service_gmail(D),accessibility_service_yahoo(U),accessibility_service_outlook(U),continuee(D),google_client_id(D),appAuthRedirectScheme(D),gmail_auth(D),yahoo_auth(D),default_web_client_id(D),hello_blank_fragment(D),safe(D),unsafe(D),refresh(D),basic_information(D),manufacturer_name(D)],style[Theme_Ekvayu(U),Base_Theme_Ekvayu(U),Theme_Material3_DayNight_NoActionBar(R),CardStyle(D),ButtonPrimary(D),Widget_Material3_Button(E),ButtonSecondary(D),Widget_Material3_Button_OutlinedButton(E)],xml[data_extraction_rules(U),backup_rules(U),gmail_accessibility_service_config(U),yahoo_accessibility_service_config(U),outlook_accessibility_service_config(U)];a^16^1d,b^17^1e,c^19^20,d^1a^21,e^18^1f,25^1b^22,26^1c^23,3d^2,3f^3,40^4,41^5,42^5^27,43^6^27,44^3^27,4a^2,4b^2,4d^2,4f^0,51^52,58^5,f4^f5^28^5f,f5^f^2f^36^5^27^37^3f^5d^38^d4^d6^b^58,f6^61^44^62^42^63^64^65^66^67^6a^68^69^6b,f7^42^44^61^63^6d^71^75^76^67^68^f8,f8^42^82^80^df^e1^e0^7e^e2^4f,f9^42^79^67^68^44,fb^29^59^5b^27^28^117^7^2a^89^42^2b^118^3,fd^8^2a^2c^8c^2d^28^8d^8e^5b^5c^2e^8f,fe^5d^28^2f^2a^57^30^93^3^5c^119^31^32^9^11a^11b^11c^11d^11e^94^9a^11f^120^121^122^123^124^9b^a1^125^126^127^128^a2^a7^129^12a,ff^27^ae^42^28^5b^2b^12b^3^2a,100^a^28^2b^2f^5^27^5c^3^6^33^5d^8^2a^af^8b^b2^42^5b^118,101^1^28^34^53^5d^12c^2a^b5^9^b6^5c^12d^8^31^5b^2^b7^12e^30^ba^12f^130^bd^131^43^27^2b^132^3^c4,103^5c^27^133^2^28^42^5b^2a^2b^3^c9,104^b^c^d^e^5^3,105^2b^9^28^35^59^5d^8^36^89^5c^27^134^2^32^d3^42^5b^2a^118^3^c9,106^30^1^31^29^28^5b^135^2^2c^5c^da^3d,107^30^1^28^39^5c^5^dd^dc^5b^2^de,108^30^1^28^39^5c^5^dd^dc^5b^2^de,109^1^28^34^53^5d^12c^2a^b5^9^b6^5c^12d^8^31^5b^2^b7^12e^30^ba^12f^130^bd^131^42^27^2b^132^3^c4,10a^3a^111,10b^45^28^30^37^eb,10c^28^1^dc^de^ef^dd^f0,10d^3b^27^2b^3^34^112^5c^136^28^f2^ea^44^5^5b^3c^39^137^31,10e^10^110,10f^10^110,147^148,148^149^5^12^3^11^a^b^c^d^13^1d,14a^e,14b^14c^5^3,14d^14e^5,152^139,153^13a;;;"/>
    </map>

</incidents>
