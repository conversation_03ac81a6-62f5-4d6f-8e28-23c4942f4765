1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tech.ekvayu"
4    android:versionCode="5"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:6:5-8:47
12-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:7:9-69
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
13-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
14    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:5-71
14-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:10:22-68
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:5-79
15-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:11:22-76
16    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:5-75
16-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:12:22-72
17    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:5-74
17-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:13:22-71
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:5-76
18-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:14:22-73
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
19-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:5-80
20-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:18:22-78
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:5-80
21-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:19:22-77
22    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
22-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:5-78
22-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:9:22-75
23    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
23-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:23:5-94
23-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:23:22-92
24    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
24-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
24-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb892034480d0c85f046f5a0cfaabe3\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
25
26    <permission
26-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
27        android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.tech.ekvayu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
31
32    <application
32-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:26:5-109:19
33        android:name="com.tech.ekvayu.BaseClass.MyApplication"
33-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:27:9-48
34        android:allowBackup="true"
34-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:28:9-35
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\632308f076161c5800c7a556acd883e8\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
36        android:dataExtractionRules="@xml/data_extraction_rules"
36-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:29:9-65
37        android:debuggable="true"
38        android:extractNativeLibs="false"
39        android:fullBackupContent="@xml/backup_rules"
39-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:30:9-54
40        android:icon="@mipmap/ic_launcher"
40-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:31:9-43
41        android:label="@string/app_name"
41-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:32:9-41
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:33:9-54
43        android:supportsRtl="true"
43-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:34:9-35
44        android:testOnly="true"
45        android:theme="@style/Theme.Ekvayu"
45-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:35:9-44
46        android:usesCleartextTraffic="true" >
46-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:36:9-44
47        <activity
47-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:39:9-41:40
48            android:name="com.tech.ekvayu.Activities.MainActivity"
48-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:40:13-52
49            android:exported="false" />
49-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:41:13-37
50        <activity
50-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:43:9-45:40
51            android:name="com.tech.ekvayu.Activities.YahooAuth"
51-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:44:13-49
52            android:exported="false" />
52-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:45:13-37
53        <activity
53-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:46:9-67:20
54            android:name="com.tech.ekvayu.Activities.DashboardActivity"
54-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:47:13-57
55            android:exported="true" >
55-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:48:13-36
56
57            <!-- Main launcher intent filter -->
58            <intent-filter>
58-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:51:13-55:29
59                <action android:name="android.intent.action.MAIN" />
59-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:52:17-69
59-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:52:25-66
60
61                <category android:name="android.intent.category.LAUNCHER" />
61-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:17-77
61-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:54:27-74
62            </intent-filter>
63            <!-- OAuth redirect intent filter -->
64            <intent-filter>
64-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:57:13-66:29
65                <action android:name="android.intent.action.VIEW" />
65-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:58:17-69
65-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:58:25-66
66
67                <category android:name="android.intent.category.DEFAULT" />
67-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:17-76
67-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:27-73
68                <category android:name="android.intent.category.BROWSABLE" />
68-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:17-78
68-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:27-75
69
70                <data
70-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:17-65:56
71                    android:host="oauth2redirect"
71-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:21-50
72                    android:scheme="com.tech.ekvayu" />
72-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:21-53
73            </intent-filter>
74        </activity>
75
76        <service
76-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:69:9-80:19
77            android:name="com.tech.ekvayu.EkService.NewGmailAccessibilityService"
77-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:70:13-67
78            android:enabled="true"
78-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:71:13-35
79            android:exported="true"
79-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:72:13-36
80            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
80-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:73:13-79
81            <intent-filter>
81-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:74:13-76:29
82                <action android:name="android.accessibilityservice.AccessibilityService" />
82-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:17-92
82-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:25-89
83            </intent-filter>
84
85            <meta-data
85-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:77:13-79:78
86                android:name="android.accessibilityservice"
86-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:17-60
87                android:resource="@xml/gmail_accessibility_service_config" />
87-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:79:17-75
88        </service>
89        <service
89-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:81:9-93:19
90            android:name="com.tech.ekvayu.EkService.YahooAccessibilityService"
90-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:82:13-64
91            android:enabled="true"
91-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:83:13-35
92            android:exported="true"
92-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:84:13-36
93            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
93-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:85:13-79
94            <intent-filter>
94-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:74:13-76:29
95                <action android:name="android.accessibilityservice.AccessibilityService" />
95-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:17-92
95-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:25-89
96            </intent-filter>
97
98            <meta-data
98-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:77:13-79:78
99                android:name="android.accessibilityservice"
99-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:17-60
100                android:resource="@xml/yahoo_accessibility_service_config" />
100-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:79:17-75
101        </service>
102        <service
102-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:94:9-105:19
103            android:name="com.tech.ekvayu.EkService.OutlookAccessibilityService"
103-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:95:13-66
104            android:exported="true"
104-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:96:13-36
105            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
105-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:97:13-79
106            <intent-filter>
106-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:74:13-76:29
107                <action android:name="android.accessibilityservice.AccessibilityService" />
107-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:17-92
107-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:75:25-89
108            </intent-filter>
109
110            <meta-data
110-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:77:13-79:78
111                android:name="android.accessibilityservice"
111-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:78:17-60
112                android:resource="@xml/outlook_accessibility_service_config" />
112-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:79:17-75
113        </service> <!-- Google Play Services Auth metadata -->
114        <meta-data
114-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:106:9-108:69
115            android:name="com.google.android.gms.version"
115-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:107:13-58
116            android:value="@integer/google_play_services_version" />
116-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:108:13-66
117
118        <provider
118-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
119            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
119-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
120            android:authorities="com.tech.ekvayu.mlkitinitprovider"
120-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
121            android:exported="false"
121-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
122            android:initOrder="99" />
122-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
123
124        <service
124-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
125            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
125-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:16:13-91
126            android:directBootAware="true"
126-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
127            android:exported="false" >
127-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:18:13-37
128            <meta-data
128-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
129                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
129-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a83cec2028fbe0cfd9b4e12c50029c\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
131        </service>
132
133        <activity
133-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
134            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
134-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
135            android:excludeFromRecents="true"
135-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
136            android:exported="true"
136-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
137            android:launchMode="singleTask"
137-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
138            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
138-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
139            <intent-filter>
139-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
140                <action android:name="android.intent.action.VIEW" />
140-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:58:17-69
140-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:58:25-66
141
142                <category android:name="android.intent.category.DEFAULT" />
142-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:17-76
142-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:27-73
143                <category android:name="android.intent.category.BROWSABLE" />
143-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:17-78
143-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:27-75
144
145                <data
145-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:17-65:56
146                    android:host="firebase.auth"
146-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:21-50
147                    android:path="/"
148                    android:scheme="genericidp" />
148-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:21-53
149            </intent-filter>
150        </activity>
151        <activity
151-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
152            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
152-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
153            android:excludeFromRecents="true"
153-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
154            android:exported="true"
154-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
155            android:launchMode="singleTask"
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
156            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
156-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
157            <intent-filter>
157-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
158                <action android:name="android.intent.action.VIEW" />
158-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:58:17-69
158-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:58:25-66
159
160                <category android:name="android.intent.category.DEFAULT" />
160-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:17-76
160-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:60:27-73
161                <category android:name="android.intent.category.BROWSABLE" />
161-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:17-78
161-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:61:27-75
162
163                <data
163-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:63:17-65:56
164                    android:host="firebase.auth"
164-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:64:21-50
165                    android:path="/"
166                    android:scheme="recaptcha" />
166-->D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\AndroidManifest.xml:65:21-53
167            </intent-filter>
168        </activity>
169
170        <service
170-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
171            android:name="com.google.firebase.components.ComponentDiscoveryService"
171-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
172            android:directBootAware="true"
172-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
173            android:exported="false" >
173-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
174            <meta-data
174-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
175                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
175-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
176                android:value="com.google.firebase.components.ComponentRegistrar" />
176-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98ee83ed22b67205153b584fea3f5b38\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
177            <meta-data
177-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
178                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
178-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
179                android:value="com.google.firebase.components.ComponentRegistrar" />
179-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2767a37834c56ebd8d1117f4578bbf11\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
180            <meta-data
180-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
181                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
181-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
182                android:value="com.google.firebase.components.ComponentRegistrar" />
182-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
183        </service>
184
185        <activity
185-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
186            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
186-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
187            android:excludeFromRecents="true"
187-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
188            android:exported="false"
188-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
189            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
189-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
190        <!--
191            Service handling Google Sign-In user revocation. For apps that do not integrate with
192            Google Sign-In, this service will never be started.
193        -->
194        <service
194-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
195            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
195-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
196            android:exported="true"
196-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
197            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
197-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
198            android:visibleToInstantApps="true" />
198-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354dabbec65665e468d12efc051d1449\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
199
200        <activity
200-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
201            android:name="com.google.android.gms.common.api.GoogleApiActivity"
201-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
202            android:exported="false"
202-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
203            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
203-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\effb50e780aa7b13f4a7b447c543253f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
204
205        <provider
205-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
206            android:name="com.google.firebase.provider.FirebaseInitProvider"
206-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
207            android:authorities="com.tech.ekvayu.firebaseinitprovider"
207-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
208            android:directBootAware="true"
208-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
209            android:exported="false"
209-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
210            android:initOrder="100" />
210-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c58b2574ad5c1c54f2e920aecac0bdb\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
211        <provider
211-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
212            android:name="androidx.startup.InitializationProvider"
212-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
213            android:authorities="com.tech.ekvayu.androidx-startup"
213-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
214            android:exported="false" >
214-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
215            <meta-data
215-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
216                android:name="androidx.emoji2.text.EmojiCompatInitializer"
216-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
217                android:value="androidx.startup" />
217-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0279a263e550395f673c12192c0a525f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
218            <meta-data
218-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
219                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
219-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
220                android:value="androidx.startup" />
220-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f472fa038cb2b9b1f07bc245e86bb0e\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
221            <meta-data
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
222                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
223                android:value="androidx.startup" />
223-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
224        </provider>
225
226        <receiver
226-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
227            android:name="androidx.profileinstaller.ProfileInstallReceiver"
227-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
228            android:directBootAware="false"
228-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
229            android:enabled="true"
229-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
230            android:exported="true"
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
231            android:permission="android.permission.DUMP" >
231-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
232            <intent-filter>
232-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
233                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
234            </intent-filter>
235            <intent-filter>
235-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
236                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
236-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
236-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
237            </intent-filter>
238            <intent-filter>
238-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
239                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
240            </intent-filter>
241            <intent-filter>
241-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
242                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
242-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
242-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4bb91da058e1be48a11fcea135e9677\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
243            </intent-filter>
244        </receiver>
245
246        <service
246-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
247            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
247-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
248            android:exported="false" >
248-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
249            <meta-data
249-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
250                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
250-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
251                android:value="cct" />
251-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1f31c9626cd91a6be77b4ca9f4974e9\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
252        </service>
253        <service
253-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
254            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
254-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
255            android:exported="false"
255-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
256            android:permission="android.permission.BIND_JOB_SERVICE" >
256-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
257        </service>
258
259        <receiver
259-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
260            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
260-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
261            android:exported="false" />
261-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5df61bf7dbf50563ff08b2b84b5d4d30\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
262    </application>
263
264</manifest>
