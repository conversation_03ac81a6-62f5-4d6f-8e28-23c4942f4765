{"logs": [{"outputFile": "com.tech.ekvayu.app-mergeDebugResources-37:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\effb50e780aa7b13f4a7b447c543253f\\transformed\\play-services-base-18.5.0\\res\\values-gl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,454,579,683,838,966,1081,1186,1353,1458,1623,1754,1915,2063,2126,2191", "endColumns": "101,158,124,103,154,127,114,104,166,104,164,130,160,147,62,64,80", "endOffsets": "294,453,578,682,837,965,1080,1185,1352,1457,1622,1753,1914,2062,2125,2190,2271"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4565,4671,4834,4963,5071,5230,5362,5481,5724,5895,6004,6173,6308,6473,6625,6692,6761", "endColumns": "105,162,128,107,158,131,118,108,170,108,168,134,164,151,66,68,84", "endOffsets": "4666,4829,4958,5066,5225,5357,5476,5585,5890,5999,6168,6303,6468,6620,6687,6756,6841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\632308f076161c5800c7a556acd883e8\\transformed\\core-1.10.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "38,39,40,41,42,43,44,131", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3519,3618,3720,3820,3918,4025,4131,12305", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "3613,3715,3815,3913,4020,4126,4242,12401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cb76107ce93d8bc8b448aa81aa5b4ec4\\transformed\\browser-1.4.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,376", "endColumns": "106,101,111,105", "endOffsets": "157,259,371,477"}, "to": {"startLines": "66,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6846,7127,7229,7341", "endColumns": "106,101,111,105", "endOffsets": "6948,7224,7336,7442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a789a3e91da4aac57216b80449d8079\\transformed\\appcompat-1.6.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,429,533,641,726,827,955,1041,1122,1214,1308,1405,1499,1599,1693,1789,1884,1976,2068,2149,2257,2364,2471,2580,2685,2799,2976,12222", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "424,528,636,721,822,950,1036,1117,1209,1303,1400,1494,1594,1688,1784,1879,1971,2063,2144,2252,2359,2466,2575,2680,2794,2971,3070,12300"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f51be04829e2a9c07e0f9602f02bc0a\\transformed\\material-1.10.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1115,1211,1290,1353,1448,1512,1581,1644,1718,1782,1838,1959,2017,2079,2135,2212,2351,2439,2519,2659,2739,2819,2968,3058,3139,3195,3251,3317,3396,3477,3565,3653,3732,3809,3891,3980,4081,4165,4257,4350,4451,4525,4617,4719,4771,4855,4921,5013,5101,5163,5227,5290,5360,5471,5576,5682,5781,5841,5901", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,148,89,80,55,55,65,78,80,87,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84", "endOffsets": "270,351,431,516,618,714,819,952,1032,1110,1206,1285,1348,1443,1507,1576,1639,1713,1777,1833,1954,2012,2074,2130,2207,2346,2434,2514,2654,2734,2814,2963,3053,3134,3190,3246,3312,3391,3472,3560,3648,3727,3804,3886,3975,4076,4160,4252,4345,4446,4520,4612,4714,4766,4850,4916,5008,5096,5158,5222,5285,5355,5466,5571,5677,5776,5836,5896,5981"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3156,3236,3321,3423,4247,4352,4485,6953,7031,7447,7526,7589,7684,7748,7817,7880,7954,8018,8074,8195,8253,8315,8371,8448,8587,8675,8755,8895,8975,9055,9204,9294,9375,9431,9487,9553,9632,9713,9801,9889,9968,10045,10127,10216,10317,10401,10493,10586,10687,10761,10853,10955,11007,11091,11157,11249,11337,11399,11463,11526,11596,11707,11812,11918,12017,12077,12137", "endLines": "5,33,34,35,36,37,45,46,47,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,148,89,80,55,55,65,78,80,87,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84", "endOffsets": "320,3151,3231,3316,3418,3514,4347,4480,4560,7026,7122,7521,7584,7679,7743,7812,7875,7949,8013,8069,8190,8248,8310,8366,8443,8582,8670,8750,8890,8970,9050,9199,9289,9370,9426,9482,9548,9627,9708,9796,9884,9963,10040,10122,10211,10312,10396,10488,10581,10682,10756,10848,10950,11002,11086,11152,11244,11332,11394,11458,11521,11591,11702,11807,11913,12012,12072,12132,12217"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0ebe8269091ddc2f9e5c8fcd397042e\\transformed\\play-services-basement-18.4.0\\res\\values-gl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "129", "endOffsets": "324"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5590", "endColumns": "133", "endOffsets": "5719"}}]}]}