{"logs": [{"outputFile": "com.tech.ekvayu.app-mergeDebugResources-37:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\632308f076161c5800c7a556acd883e8\\transformed\\core-1.10.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "38,39,40,41,42,43,44,131", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3400,3498,3600,3703,3804,3906,4004,11846", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "3493,3595,3698,3799,3901,3999,4128,11942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cb76107ce93d8bc8b448aa81aa5b4ec4\\transformed\\browser-1.4.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "66,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6701,6962,7063,7177", "endColumns": "104,100,113,102", "endOffsets": "6801,7058,7172,7275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0ebe8269091ddc2f9e5c8fcd397042e\\transformed\\play-services-basement-18.4.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5447", "endColumns": "150", "endOffsets": "5593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a789a3e91da4aac57216b80449d8079\\transformed\\appcompat-1.6.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,11766", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,11841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f51be04829e2a9c07e0f9602f02bc0a\\transformed\\material-1.10.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1055,1147,1216,1275,1360,1423,1485,1543,1607,1668,1722,1836,1894,1954,2008,2078,2205,2286,2365,2500,2576,2653,2782,2866,2948,3003,3058,3124,3193,3270,3356,3435,3503,3579,3649,3714,3816,3911,3984,4078,4171,4245,4314,4408,4464,4547,4614,4698,4786,4848,4912,4975,5042,5139,5245,5336,5438,5497,5556", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,128,83,81,54,54,65,68,76,85,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76", "endOffsets": "258,335,414,495,594,683,791,903,986,1050,1142,1211,1270,1355,1418,1480,1538,1602,1663,1717,1831,1889,1949,2003,2073,2200,2281,2360,2495,2571,2648,2777,2861,2943,2998,3053,3119,3188,3265,3351,3430,3498,3574,3644,3709,3811,3906,3979,4073,4166,4240,4309,4403,4459,4542,4609,4693,4781,4843,4907,4970,5037,5134,5240,5331,5433,5492,5551,5628"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2975,3052,3131,3212,3311,4133,4241,4353,6806,6870,7280,7349,7408,7493,7556,7618,7676,7740,7801,7855,7969,8027,8087,8141,8211,8338,8419,8498,8633,8709,8786,8915,8999,9081,9136,9191,9257,9326,9403,9489,9568,9636,9712,9782,9847,9949,10044,10117,10211,10304,10378,10447,10541,10597,10680,10747,10831,10919,10981,11045,11108,11175,11272,11378,11469,11571,11630,11689", "endLines": "5,33,34,35,36,37,45,46,47,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,128,83,81,54,54,65,68,76,85,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76", "endOffsets": "308,3047,3126,3207,3306,3395,4236,4348,4431,6865,6957,7344,7403,7488,7551,7613,7671,7735,7796,7850,7964,8022,8082,8136,8206,8333,8414,8493,8628,8704,8781,8910,8994,9076,9131,9186,9252,9321,9398,9484,9563,9631,9707,9777,9842,9944,10039,10112,10206,10299,10373,10442,10536,10592,10675,10742,10826,10914,10976,11040,11103,11170,11267,11373,11464,11566,11625,11684,11761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\effb50e780aa7b13f4a7b447c543253f\\transformed\\play-services-base-18.5.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4436,4543,4716,4846,4955,5102,5231,5344,5598,5760,5869,6042,6174,6327,6488,6553,6619", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "4538,4711,4841,4950,5097,5226,5339,5442,5755,5864,6037,6169,6322,6483,6548,6614,6696"}}]}]}