[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_activity_email_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\activity_email_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_argument.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\argument.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_activity_email_auth.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\activity_email_auth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_activity_yahoo_auth.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\activity_yahoo_auth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\font_roboto_semi_bold.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\font\\roboto_semi_bold.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_edit_text_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\edit_text_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_activity_gmail_auth.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\activity_gmail_auth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_item_email.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\item_email.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_bg_alert_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\bg_alert_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_right_arrow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\right_arrow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_header_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\header_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_item_spam_mail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\item_spam_mail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_bg_button_app_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\bg_button_app_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable-xxhdpi_theme.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable-xxhdpi\\theme.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\raw_ai_loading.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\raw\\ai_loading.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable-hdpi_icon_secure_phishing.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable-hdpi\\icon_secure_phishing.png"}, {"merged": "com.tech.ekvayu.app-debug-39:/layout_fragment_theme_settings_bottom_sheet.xml.flat", "source": "com.tech.ekvayu.app-main-41:/layout/fragment_theme_settings_bottom_sheet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_dispute_.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\dispute_.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_layout_validation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\layout_validation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_table_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\table_background.xml"}, {"merged": "com.tech.ekvayu.app-debug-39:/drawable_theme_icon.xml.flat", "source": "com.tech.ekvayu.app-main-41:/drawable/theme_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\font_roboto_semi_medium.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\font\\roboto_semi_medium.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "com.tech.ekvayu.app-debug-39:/layout_fragment_home.xml.flat", "source": "com.tech.ekvayu.app-main-41:/layout/fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_bg_search_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\bg_search_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_ic_attachment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\ic_attachment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_fragment_email_config_bottom_sheet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\fragment_email_config_bottom_sheet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_dispute_mails.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\dispute_mails.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\raw_unsafe.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\raw\\unsafe.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_fragment_spam_mail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\fragment_spam_mail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_fragment_dispute_maillist.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\fragment_dispute_maillist.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable-xxxhdpi_theme.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable-xxxhdpi\\theme.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_layout_dispute_raise.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\layout_dispute_raise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_activity_email_demo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\activity_email_demo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_fragment_device_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\fragment_device_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable-xhdpi_theme.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable-xhdpi\\theme.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_item_dashboard_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\item_dashboard_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable-xxxhdpi_icon_secure_phishing.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable-xxxhdpi\\icon_secure_phishing.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_bg_status.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\bg_status.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_bg_anim_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\bg_anim_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_layout_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\layout_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_layout_spam_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\layout_spam_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_fragment_raise_bottom_sheet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\fragment_raise_bottom_sheet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_bg_button_grey_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\bg_button_grey_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_activity.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\activity.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_spam_mail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\spam_mail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_layout_progress_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\layout_progress_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_bg_button_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\bg_button_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_item_dispute_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\item_dispute_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_dispute.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\dispute.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_fragment_suggetion_bottom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\fragment_suggetion_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable-mdpi_icon_secure_phishing.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable-mdpi\\icon_secure_phishing.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_activity_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\activity_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_fragment_activity_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\fragment_activity_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\xml_yahoo_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\xml\\yahoo_accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable-xhdpi_icon_secure_phishing.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable-xhdpi\\icon_secure_phishing.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_bg_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\bg_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_mail_raised.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\mail_raised.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\xml_gmail_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\xml\\gmail_accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_device_d.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\device_d.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_device_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\device_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\raw_wait.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\raw\\wait.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\font_roboto_semi_regular.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\font\\roboto_semi_regular.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable-xxhdpi_icon_secure_phishing.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable-xxhdpi\\icon_secure_phishing.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\raw_safe.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\raw\\safe.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable-hdpi_theme.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable-hdpi\\theme.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\xml_outlook_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\xml\\outlook_accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable_process.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable\\process.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\drawable-mdpi_theme.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\drawable-mdpi\\theme.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\layout_fragment_warning_bottom_sheet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\layout\\fragment_warning_bottom_sheet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-debug-39:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-main-41:\\xml\\backup_rules.xml"}]