<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".Fragments.DeviceDetailsFragment">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_semi_bold"
        android:textSize="@dimen/_12sdp"
        android:text="Device Info"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_30sdp"
        />

    <TableLayout
        android:id="@+id/tlDeviceInfo"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:stretchColumns="*"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:background="@drawable/table_background"
        android:elevation="4dp">

        <TableRow
            android:background="@color/white">
            <TextView
                android:text="@string/device_name"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvDeviceName"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>


        <TableRow
            android:background="@color/light_grey">
            <TextView
                android:text="@string/manufacturer"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvManufacturer"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>


        <TableRow
            android:background="@color/white">
            <TextView
                android:text="@string/model_name"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvModelName"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>

        <TableRow
            android:background="@color/light_grey">
            <TextView
                android:text="@string/brand_name"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvBradName"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>

        <TableRow
            android:background="@color/white">
            <TextView
                android:text="@string/product"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvProductName"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>


    </TableLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitleAndroid"
        app:layout_constraintTop_toBottomOf="@+id/tlDeviceInfo"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_semi_bold"
        android:textSize="@dimen/_12sdp"
        android:text="@string/android_info"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_10sdp"
        />

    <TableLayout
        android:id="@+id/tlAndroidInfo"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleAndroid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:stretchColumns="*"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:background="@drawable/table_background"
        android:elevation="4dp">

        <TableRow
            android:background="@color/white">

            <TextView
                android:text="@string/android_version"
                android:padding="10dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvAndroidVersion"
                android:padding="10dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>


        <TableRow
            android:background="@color/light_grey">
            <TextView
                android:text="@string/api_level"
                android:padding="10dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvApiLevel"
                android:padding="10dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>


        <TableRow
            android:background="@color/white">
            <TextView
                android:text="@string/build_number"
                android:padding="10dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvBuildNumber"
                android:padding="10dp"
                android:layout_weight="1"
                android:text=""
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>

        <TableRow
            android:background="@color/light_grey">
            <TextView
                android:text="@string/serial_number"
                android:padding="10dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvSerialNumber"
                android:padding="10dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>

        <TableRow
            android:background="@color/white">
            <TextView
                android:text="@string/android_id"
                android:padding="10dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvAndroidId"
                android:padding="10dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />

        </TableRow>


    </TableLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvScreenInfo"
        app:layout_constraintTop_toBottomOf="@+id/tlAndroidInfo"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_semi_bold"
        android:textSize="@dimen/_12sdp"
        android:text="@string/screen_info"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_10sdp"
        />

    <TableLayout
        android:id="@+id/tlScreenInfo"
        app:layout_constraintTop_toBottomOf="@+id/tvScreenInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:stretchColumns="*"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:background="@drawable/table_background"
        android:elevation="4dp">

        <TableRow
            android:background="@color/white">
            <TextView
                android:text="@string/resolution"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvResolution"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>


        <TableRow
            android:background="@color/light_grey">
            <TextView
                android:text="Density"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvDensity"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>


        <TableRow
            android:background="@color/white">
            <TextView
                android:text="@string/density_factor"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvDensityFactor"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>

        <TableRow
            android:background="@color/light_grey">
            <TextView
                android:text="@string/scaled_density"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvScaleDensity"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>

    </TableLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAppInfo"
        app:layout_constraintTop_toBottomOf="@+id/tlScreenInfo"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_semi_bold"
        android:textSize="@dimen/_12sdp"
        android:text="@string/app_info"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_10sdp"
        />

    <TableLayout
        android:id="@+id/tlAppInfo"
        app:layout_constraintTop_toBottomOf="@+id/tvAppInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:stretchColumns="*"
        android:layout_marginHorizontal="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:background="@drawable/table_background"
        android:elevation="4dp">

        <TableRow
            android:background="@color/white">
            <TextView
                android:text="Package"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvPackage"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>


        <TableRow
            android:background="@color/light_grey">
            <TextView
                android:text="@string/version"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvVersion"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>


        <TableRow
            android:background="@color/white">
            <TextView
                android:text="Target SDK"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvTargetSdk"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />
        </TableRow>

        <TableRow
            android:background="@color/light_grey">
            <TextView
                android:text="@string/min_sdk"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_8sdp"
                android:gravity="start"
                />

            <TextView
                android:id="@+id/tvMinSdk"
                android:padding="10dp"
                android:fontFamily="@font/roboto_semi_regular"
                android:textSize="@dimen/_9sdp"
                android:gravity="end"
                />

        </TableRow>

    </TableLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


</ScrollView>