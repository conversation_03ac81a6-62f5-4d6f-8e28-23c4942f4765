<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Email Configuration"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Set your primary email address for better email detection and processing."
        android:textSize="14sp"
        android:textColor="@android:color/darker_gray"
        android:gravity="center"
        android:layout_marginBottom="20dp" />

    <!-- Current Email Display -->
    <TextView
        android:id="@+id/tvCurrentEmail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Current: Not set"
        android:textSize="12sp"
        android:textColor="@android:color/holo_blue_dark"
        android:layout_marginBottom="12dp" />

    <!-- Email Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/tilPrimaryEmail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Enter your primary email"
        app:boxStrokeColor="@android:color/holo_blue_dark"
        app:hintTextColor="@android:color/holo_blue_dark"
        android:layout_marginBottom="16dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etPrimaryEmail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textEmailAddress"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Cancel"
            android:textColor="@android:color/darker_gray"
            android:background="@android:color/transparent"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btnSaveEmail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Save Email"
            android:textColor="@android:color/white"
            android:background="@android:color/holo_blue_dark"
            android:enabled="false"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Stats Button -->
    <Button
        android:id="@+id/btnShowStats"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Show Detection Statistics"
        android:textColor="@android:color/holo_orange_dark"
        android:background="@android:color/transparent"
        android:textSize="12sp"
        android:layout_marginBottom="8dp" />

    <!-- Stats Display -->
    <TextView
        android:id="@+id/tvStats"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text=""
        android:textSize="11sp"
        android:textColor="@android:color/darker_gray"
        android:background="@android:color/background_light"
        android:padding="12dp"
        android:visibility="gone"
        android:fontFamily="monospace" />

</LinearLayout>
