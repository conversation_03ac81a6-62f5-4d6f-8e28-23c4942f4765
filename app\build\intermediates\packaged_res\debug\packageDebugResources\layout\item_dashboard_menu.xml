<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cvMain"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardUseCompatPadding="true"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="@dimen/_8sdp"
    android:elevation="@dimen/_5sdp"
    app:cardElevation="@dimen/_5sdp"
    android:foreground="?attr/selectableItemBackground"
    xmlns:app="http://schemas.android.com/apk/res-auto">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_100sdp"
            android:padding="@dimen/_10sdp"
            android:orientation="vertical">



            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvMenuName"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/device_details"
                android:textColor="@color/black"
                android:textSize="@dimen/_11sdp"
                android:fontFamily="@font/roboto_semi_medium"
                />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvDescripton"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMenuName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/device_details"
                android:textColor="@color/black"
                android:textSize="@dimen/_10sdp"
                android:fontFamily="@font/roboto_semi_regular"
                />


            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivIcon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:scaleType="centerInside"
                android:src="@drawable/activity"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>