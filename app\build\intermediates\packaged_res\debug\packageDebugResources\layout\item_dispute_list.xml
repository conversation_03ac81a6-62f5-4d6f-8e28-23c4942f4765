<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="@dimen/_6sdp"
    android:elevation="@dimen/_5sdp"
    app:cardElevation="@dimen/_5sdp"
    android:layout_marginHorizontal="@dimen/_10sdp"
    android:layout_marginVertical="@dimen/_5sdp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_10sdp">


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSender"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/app_color"
            android:textSize="@dimen/_10sdp"
            app:layout_constraintEnd_toStartOf="@+id/tvStatus"
            android:fontFamily="@font/roboto_semi_regular"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginEnd="@dimen/_5sdp"
            android:text="<EMAIL>"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvReceiver"
            app:layout_constraintTop_toBottomOf="@+id/tvSender"
            app:layout_constraintEnd_toStartOf="@+id/tvStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/app_color"
            android:textSize="@dimen/_10sdp"
            android:fontFamily="@font/roboto_semi_regular"
            app:layout_constraintStart_toStartOf="parent"
            android:text="<EMAIL>"
            android:layout_marginEnd="@dimen/_5sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvSender"
            app:layout_constraintBottom_toBottomOf="@+id/tvReceiver"
            android:text="Pending"
            android:padding="@dimen/_5sdp"
            android:fontFamily="@font/roboto_semi_medium"
            android:textColor="@color/black"
            android:textSize="@dimen/_10sdp"
            android:layout_marginHorizontal="@dimen/_5sdp"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>
