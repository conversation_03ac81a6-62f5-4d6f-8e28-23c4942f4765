<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Provider indicator -->
        <TextView
            android:id="@+id/tv_provider"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Gmail"
            android:textSize="10sp"
            android:textColor="@android:color/white"
            android:background="@drawable/bg_button_app_color"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Sender name -->
        <TextView
            android:id="@+id/tv_sender"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="John Doe"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_provider"
            tools:text="John Doe" />

        <!-- Sender email -->
        <TextView
            android:id="@+id/tv_sender_email"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="<EMAIL>"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="2dp"
            app:layout_constraintTop_toBottomOf="@id/tv_sender"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_date"
            tools:text="<EMAIL>" />

        <!-- Date -->
        <TextView
            android:id="@+id/tv_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Dec 25, 2023 10:30"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            app:layout_constraintTop_toBottomOf="@id/tv_sender"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Dec 25, 2023 10:30" />

        <!-- Subject -->
        <TextView
            android:id="@+id/tv_subject"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Important Meeting Tomorrow"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/tv_sender_email"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_star"
            tools:text="Important Meeting Tomorrow" />

        <!-- Star icon -->
        <ImageView
            android:id="@+id/iv_star"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@android:drawable/btn_star_big_on"
            android:visibility="gone"
            android:layout_marginStart="8dp"
            app:layout_constraintTop_toTopOf="@id/tv_subject"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible" />

        <!-- Body preview -->
        <TextView
            android:id="@+id/tv_body_preview"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Hi team, I wanted to remind everyone about our important meeting scheduled for tomorrow at 2 PM..."
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginTop="4dp"
            app:layout_constraintTop_toBottomOf="@id/tv_subject"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Hi team, I wanted to remind everyone about our important meeting scheduled for tomorrow at 2 PM..." />

        <!-- Bottom row with indicators -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="start|center_vertical"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/tv_body_preview"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <!-- Attachment icon -->
            <ImageView
                android:id="@+id/iv_attachment"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_attachment"
                android:visibility="gone"
                android:layout_marginEnd="8dp"
                tools:visibility="visible" />

            <!-- Recipient count -->
            <TextView
                android:id="@+id/tv_recipient_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="+3"
                android:textSize="10sp"
                android:textColor="@android:color/white"
                android:background="@drawable/bg_button_app_color"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="+3" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
