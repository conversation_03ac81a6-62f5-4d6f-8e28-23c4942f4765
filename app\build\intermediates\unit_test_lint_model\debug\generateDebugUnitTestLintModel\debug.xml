<variant
    name="debug"
    useSupportLibraryVectorDrawables="true"
    package="com.tech.ekvayu"
    minSdkVersion="24"
    targetSdkVersion="35"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-debug-report.txt"
    partialResultsDir="build\intermediates\unit_test_lint_partial_results\debug\lintAnalyzeDebugUnitTest\out">
  <buildFeatures
      viewBinding="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\test\AndroidManifest.xml"
        javaDirectories="src\test\java;src\testDebug\java;src\test\kotlin;src\testDebug\kotlin"
        assetsDirectories="src\test\assets;src\testDebug\assets"
        unitTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="appAuthRedirectScheme"
        value="com.tech.ekvayu" />
  </manifestPlaceholders>
  <artifact
      type="UNIT_TEST">
  </artifact>
</variant>
