<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.0" type="incidents">

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.8.0 is available: 8.11.0. (There is also a newer version of 8.8.𝑥 available, if upgrading to 8.11.0 is difficult: 8.8.2)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.11.0"
                family="Update versions"
                oldString="8.8.0"
                replacement="8.11.0"
                priority="0"/>
            <fix-replace
                description="Change to 8.8.2"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.8.0"
                replacement="8.8.2"
                priority="0"/>
        </fix-alternatives>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="53"
            endLine="4"
            endColumn="19"
            endOffset="61"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="94"
            endLine="6"
            endColumn="23"
            endOffset="101"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="117"
            endLine="7"
            endColumn="23"
            endOffset="124"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="13"
            startOffset="137"
            endLine="8"
            endColumn="20"
            endOffset="144"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.10.0"
            replacement="1.12.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="12"
            startOffset="156"
            endLine="9"
            endColumn="20"
            endOffset="164"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity than 1.8.0 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="12"
            startOffset="176"
            endLine="10"
            endColumn="19"
            endOffset="183"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="20"
            startOffset="203"
            endLine="11"
            endColumn="27"
            endOffset="210"/>
    </incident>

</incidents>
