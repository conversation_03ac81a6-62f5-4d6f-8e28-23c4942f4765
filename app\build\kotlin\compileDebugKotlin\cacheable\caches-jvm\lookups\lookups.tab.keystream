  Manifest android  R android  
permission android.Manifest  ACCESS_FINE_LOCATION android.Manifest.permission  ACCESS_NETWORK_STATE android.Manifest.permission  ACCESS_WIFI_STATE android.Manifest.permission  READ_PHONE_STATE android.Manifest.permission  color 	android.R  darker_gray android.R.color  holo_green_dark android.R.color  holo_red_light android.R.color  AccessibilityService android.accessibilityservice  AccessibilityEvent 1android.accessibilityservice.AccessibilityService  AccessibilityNodeInfo 1android.accessibilityservice.AccessibilityService  	ApiClient 1android.accessibilityservice.AccessibilityService  
ApiService 1android.accessibilityservice.AccessibilityService  AppConstant 1android.accessibilityservice.AccessibilityService  Boolean 1android.accessibilityservice.AccessibilityService  BufferedReader 1android.accessibilityservice.AccessibilityService  Build 1android.accessibilityservice.AccessibilityService  Call 1android.accessibilityservice.AccessibilityService  Callback 1android.accessibilityservice.AccessibilityService  Context 1android.accessibilityservice.AccessibilityService  
EmailResponse 1android.accessibilityservice.AccessibilityService  	Exception 1android.accessibilityservice.AccessibilityService  File 1android.accessibilityservice.AccessibilityService  FileOutputStream 1android.accessibilityservice.AccessibilityService  
FileReader 1android.accessibilityservice.AccessibilityService  Handler 1android.accessibilityservice.AccessibilityService  HashResponse 1android.accessibilityservice.AccessibilityService  IOException 1android.accessibilityservice.AccessibilityService  Intent 1android.accessibilityservice.AccessibilityService  LAYOUT_INFLATER_SERVICE 1android.accessibilityservice.AccessibilityService  LayoutInflater 1android.accessibilityservice.AccessibilityService  LayoutProgressBinding 1android.accessibilityservice.AccessibilityService  LayoutValidationBinding 1android.accessibilityservice.AccessibilityService  List 1android.accessibilityservice.AccessibilityService  Log 1android.accessibilityservice.AccessibilityService  Looper 1android.accessibilityservice.AccessibilityService  Map 1android.accessibilityservice.AccessibilityService  
MultipartBody 1android.accessibilityservice.AccessibilityService  MutableList 1android.accessibilityservice.AccessibilityService  OkHttpClient 1android.accessibilityservice.AccessibilityService  
PedingMailRes 1android.accessibilityservice.AccessibilityService  PendingMailRequest 1android.accessibilityservice.AccessibilityService  PixelFormat 1android.accessibilityservice.AccessibilityService  R 1android.accessibilityservice.AccessibilityService  Regex 1android.accessibilityservice.AccessibilityService  Request 1android.accessibilityservice.AccessibilityService  RequestBody 1android.accessibilityservice.AccessibilityService  Response 1android.accessibilityservice.AccessibilityService  Settings 1android.accessibilityservice.AccessibilityService  SharedPrefManager 1android.accessibilityservice.AccessibilityService  String 1android.accessibilityservice.AccessibilityService  
StringBuilder 1android.accessibilityservice.AccessibilityService  	Throwable 1android.accessibilityservice.AccessibilityService  Toast 1android.accessibilityservice.AccessibilityService  Uri 1android.accessibilityservice.AccessibilityService  View 1android.accessibilityservice.AccessibilityService  WINDOW_SERVICE 1android.accessibilityservice.AccessibilityService  
WindowManager 1android.accessibilityservice.AccessibilityService  also 1android.accessibilityservice.AccessibilityService  android 1android.accessibilityservice.AccessibilityService  
appendLine 1android.accessibilityservice.AccessibilityService  applicationContext 1android.accessibilityservice.AccessibilityService  checkAiResponse 1android.accessibilityservice.AccessibilityService  contains 1android.accessibilityservice.AccessibilityService  endsWith 1android.accessibilityservice.AccessibilityService  extractEmail 1android.accessibilityservice.AccessibilityService  extractEmailDetails 1android.accessibilityservice.AccessibilityService  
extractEmails 1android.accessibilityservice.AccessibilityService  findEmailNodes 1android.accessibilityservice.AccessibilityService  first 1android.accessibilityservice.AccessibilityService  firstOrNull 1android.accessibilityservice.AccessibilityService  	getHashId 1android.accessibilityservice.AccessibilityService  	getOrNull 1android.accessibilityservice.AccessibilityService  getSystemService 1android.accessibilityservice.AccessibilityService  getValue 1android.accessibilityservice.AccessibilityService  hasOverlayPermission 1android.accessibilityservice.AccessibilityService  invoke 1android.accessibilityservice.AccessibilityService  
isNotEmpty 1android.accessibilityservice.AccessibilityService  
isNullOrEmpty 1android.accessibilityservice.AccessibilityService  isValidEmail 1android.accessibilityservice.AccessibilityService  java 1android.accessibilityservice.AccessibilityService  joinToString 1android.accessibilityservice.AccessibilityService  lazy 1android.accessibilityservice.AccessibilityService  let 1android.accessibilityservice.AccessibilityService  	lowercase 1android.accessibilityservice.AccessibilityService  map 1android.accessibilityservice.AccessibilityService  matches 1android.accessibilityservice.AccessibilityService  
mutableListOf 1android.accessibilityservice.AccessibilityService  mutableMapOf 1android.accessibilityservice.AccessibilityService  mutableSetOf 1android.accessibilityservice.AccessibilityService  provideDelegate 1android.accessibilityservice.AccessibilityService  readEmlFile 1android.accessibilityservice.AccessibilityService  removePrefix 1android.accessibilityservice.AccessibilityService  requestOverlayPermission 1android.accessibilityservice.AccessibilityService  set 1android.accessibilityservice.AccessibilityService  setResponsePopup 1android.accessibilityservice.AccessibilityService  sharedPrefManager 1android.accessibilityservice.AccessibilityService  split 1android.accessibilityservice.AccessibilityService  
splitMailData 1android.accessibilityservice.AccessibilityService  splitOutlookEmailData 1android.accessibilityservice.AccessibilityService  
startsWith 1android.accessibilityservice.AccessibilityService  toByteArray 1android.accessibilityservice.AccessibilityService  toList 1android.accessibilityservice.AccessibilityService  toMediaType 1android.accessibilityservice.AccessibilityService  toMediaTypeOrNull 1android.accessibilityservice.AccessibilityService  toRegex 1android.accessibilityservice.AccessibilityService  
toRequestBody 1android.accessibilityservice.AccessibilityService  toString 1android.accessibilityservice.AccessibilityService  trim 1android.accessibilityservice.AccessibilityService  
trimIndent 1android.accessibilityservice.AccessibilityService  until 1android.accessibilityservice.AccessibilityService  
uploadFile 1android.accessibilityservice.AccessibilityService  use 1android.accessibilityservice.AccessibilityService  AccountManager android.accounts  ObjectAnimator android.animation  start android.animation.Animator  duration  android.animation.ObjectAnimator  getDURATION  android.animation.ObjectAnimator  getDuration  android.animation.ObjectAnimator  ofFloat  android.animation.ObjectAnimator  setDuration  android.animation.ObjectAnimator  start  android.animation.ObjectAnimator  start android.animation.ValueAnimator  SuppressLint android.annotation  ActivityManager android.app  Application android.app  Dialog android.app  AccessibilityService android.app.Activity  ActivityDashboardBinding android.app.Activity  ActivityMainBinding android.app.Activity  ActivityYahooAuthBinding android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  Call android.app.Activity  Callback android.app.Activity  Class android.app.Activity  
ComponentName android.app.Activity  Context android.app.Activity  FirebaseAuth android.app.Activity  HomeFragment android.app.Activity  IOException android.app.Activity  Intent android.app.Activity  Log android.app.Activity  ObjectAnimator android.app.Activity  OkHttpClient android.app.Activity  R android.app.Activity  Request android.app.Activity  Response android.app.Activity  Settings android.app.Activity  SharedPrefManager android.app.Activity  String android.app.Activity  	TextUtils android.app.Activity  androidx android.app.Activity  equals android.app.Activity  getValue android.app.Activity  invoke android.app.Activity  
isNullOrEmpty android.app.Activity  lazy android.app.Activity  
onBackPressed android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  setContentView android.app.Activity  showFragment android.app.Activity  
MemoryInfo android.app.ActivityManager  
getMemoryInfo android.app.ActivityManager  availMem &android.app.ActivityManager.MemoryInfo  	lowMemory &android.app.ActivityManager.MemoryInfo  totalMem &android.app.ActivityManager.MemoryInfo  SharedPrefManager android.app.Application  onCreate android.app.Application  Window android.app.Dialog  apply android.app.Dialog  dismiss android.app.Dialog  getAPPLY android.app.Dialog  getApply android.app.Dialog  getISShowing android.app.Dialog  getIsShowing android.app.Dialog  	getTAKEIf android.app.Dialog  	getTakeIf android.app.Dialog  	getWINDOW android.app.Dialog  	getWindow android.app.Dialog  	isShowing android.app.Dialog  requestWindowFeature android.app.Dialog  
setCancelable android.app.Dialog  setContentView android.app.Dialog  
setShowing android.app.Dialog  	setWindow android.app.Dialog  show android.app.Dialog  takeIf android.app.Dialog  window android.app.Dialog  AccessibilityEvent android.app.Service  AccessibilityNodeInfo android.app.Service  	ApiClient android.app.Service  
ApiService android.app.Service  AppConstant android.app.Service  Boolean android.app.Service  BufferedReader android.app.Service  Build android.app.Service  Call android.app.Service  Callback android.app.Service  Context android.app.Service  
EmailResponse android.app.Service  	Exception android.app.Service  File android.app.Service  FileOutputStream android.app.Service  
FileReader android.app.Service  Handler android.app.Service  HashResponse android.app.Service  IOException android.app.Service  Intent android.app.Service  LAYOUT_INFLATER_SERVICE android.app.Service  LayoutInflater android.app.Service  LayoutProgressBinding android.app.Service  LayoutValidationBinding android.app.Service  List android.app.Service  Log android.app.Service  Looper android.app.Service  Map android.app.Service  
MultipartBody android.app.Service  MutableList android.app.Service  OkHttpClient android.app.Service  
PedingMailRes android.app.Service  PendingMailRequest android.app.Service  PixelFormat android.app.Service  R android.app.Service  Regex android.app.Service  Request android.app.Service  RequestBody android.app.Service  Response android.app.Service  Settings android.app.Service  SharedPrefManager android.app.Service  String android.app.Service  
StringBuilder android.app.Service  	Throwable android.app.Service  Toast android.app.Service  Uri android.app.Service  View android.app.Service  WINDOW_SERVICE android.app.Service  
WindowManager android.app.Service  also android.app.Service  android android.app.Service  
appendLine android.app.Service  applicationContext android.app.Service  checkAiResponse android.app.Service  contains android.app.Service  endsWith android.app.Service  extractEmail android.app.Service  extractEmailDetails android.app.Service  
extractEmails android.app.Service  findEmailNodes android.app.Service  first android.app.Service  firstOrNull android.app.Service  	getHashId android.app.Service  	getOrNull android.app.Service  getSystemService android.app.Service  getValue android.app.Service  hasOverlayPermission android.app.Service  invoke android.app.Service  
isNotEmpty android.app.Service  
isNullOrEmpty android.app.Service  isValidEmail android.app.Service  java android.app.Service  joinToString android.app.Service  lazy android.app.Service  let android.app.Service  	lowercase android.app.Service  map android.app.Service  matches android.app.Service  
mutableListOf android.app.Service  mutableMapOf android.app.Service  mutableSetOf android.app.Service  provideDelegate android.app.Service  readEmlFile android.app.Service  removePrefix android.app.Service  requestOverlayPermission android.app.Service  set android.app.Service  setResponsePopup android.app.Service  sharedPrefManager android.app.Service  split android.app.Service  
splitMailData android.app.Service  splitOutlookEmailData android.app.Service  
startsWith android.app.Service  toByteArray android.app.Service  toList android.app.Service  toMediaType android.app.Service  toMediaTypeOrNull android.app.Service  toRegex android.app.Service  
toRequestBody android.app.Service  toString android.app.Service  trim android.app.Service  
trimIndent android.app.Service  until android.app.Service  
uploadFile android.app.Service  use android.app.Service  
ComponentName android.content  ContentResolver android.content  
ContentValues android.content  Context android.content  Intent android.content  flattenToString android.content.ComponentName  TAG android.content.ContentValues  ACTIVITY_SERVICE android.content.Context  AccessibilityEvent android.content.Context  AccessibilityNodeInfo android.content.Context  AccessibilityService android.content.Context  ActivityDashboardBinding android.content.Context  ActivityMainBinding android.content.Context  ActivityYahooAuthBinding android.content.Context  	ApiClient android.content.Context  
ApiService android.content.Context  AppConstant android.content.Context  BATTERY_SERVICE android.content.Context  Boolean android.content.Context  BufferedReader android.content.Context  Build android.content.Context  Bundle android.content.Context  CONNECTIVITY_SERVICE android.content.Context  Call android.content.Context  Callback android.content.Context  Class android.content.Context  
ComponentName android.content.Context  Context android.content.Context  
EmailResponse android.content.Context  	Exception android.content.Context  File android.content.Context  FileOutputStream android.content.Context  
FileReader android.content.Context  FirebaseAuth android.content.Context  Handler android.content.Context  HashResponse android.content.Context  HomeFragment android.content.Context  IOException android.content.Context  Intent android.content.Context  LAYOUT_INFLATER_SERVICE android.content.Context  LayoutInflater android.content.Context  LayoutProgressBinding android.content.Context  LayoutValidationBinding android.content.Context  List android.content.Context  Log android.content.Context  Looper android.content.Context  Map android.content.Context  
MultipartBody android.content.Context  MutableList android.content.Context  ObjectAnimator android.content.Context  OkHttpClient android.content.Context  
PedingMailRes android.content.Context  PendingMailRequest android.content.Context  PixelFormat android.content.Context  R android.content.Context  Regex android.content.Context  Request android.content.Context  RequestBody android.content.Context  Response android.content.Context  Settings android.content.Context  SharedPrefManager android.content.Context  String android.content.Context  
StringBuilder android.content.Context  TELEPHONY_SERVICE android.content.Context  	TextUtils android.content.Context  	Throwable android.content.Context  Toast android.content.Context  Uri android.content.Context  View android.content.Context  WIFI_SERVICE android.content.Context  WINDOW_SERVICE android.content.Context  
WindowManager android.content.Context  also android.content.Context  android android.content.Context  androidx android.content.Context  
appendLine android.content.Context  applicationContext android.content.Context  checkAiResponse android.content.Context  contains android.content.Context  contentResolver android.content.Context  endsWith android.content.Context  equals android.content.Context  extractEmail android.content.Context  extractEmailDetails android.content.Context  
extractEmails android.content.Context  filesDir android.content.Context  findEmailNodes android.content.Context  first android.content.Context  firstOrNull android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getFILESDir android.content.Context  getFilesDir android.content.Context  	getHashId android.content.Context  	getOrNull android.content.Context  getPACKAGEManager android.content.Context  getPACKAGEName android.content.Context  getPackageManager android.content.Context  getPackageName android.content.Context  getSystemService android.content.Context  getValue android.content.Context  hasOverlayPermission android.content.Context  invoke android.content.Context  
isNotEmpty android.content.Context  
isNullOrEmpty android.content.Context  isValidEmail android.content.Context  java android.content.Context  joinToString android.content.Context  lazy android.content.Context  let android.content.Context  	lowercase android.content.Context  map android.content.Context  matches android.content.Context  
mutableListOf android.content.Context  mutableMapOf android.content.Context  mutableSetOf android.content.Context  
onBackPressed android.content.Context  onCreate android.content.Context  packageManager android.content.Context  packageName android.content.Context  provideDelegate android.content.Context  readEmlFile android.content.Context  removePrefix android.content.Context  requestOverlayPermission android.content.Context  set android.content.Context  setApplicationContext android.content.Context  setContentResolver android.content.Context  setContentView android.content.Context  setFilesDir android.content.Context  setPackageManager android.content.Context  setPackageName android.content.Context  setResponsePopup android.content.Context  sharedPrefManager android.content.Context  showFragment android.content.Context  split android.content.Context  
splitMailData android.content.Context  splitOutlookEmailData android.content.Context  
startActivity android.content.Context  
startsWith android.content.Context  toByteArray android.content.Context  toList android.content.Context  toMediaType android.content.Context  toMediaTypeOrNull android.content.Context  toRegex android.content.Context  
toRequestBody android.content.Context  toString android.content.Context  trim android.content.Context  
trimIndent android.content.Context  until android.content.Context  
uploadFile android.content.Context  use android.content.Context  AccessibilityEvent android.content.ContextWrapper  AccessibilityNodeInfo android.content.ContextWrapper  AccessibilityService android.content.ContextWrapper  ActivityDashboardBinding android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityYahooAuthBinding android.content.ContextWrapper  	ApiClient android.content.ContextWrapper  
ApiService android.content.ContextWrapper  AppConstant android.content.ContextWrapper  Boolean android.content.ContextWrapper  BufferedReader android.content.ContextWrapper  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  Call android.content.ContextWrapper  Callback android.content.ContextWrapper  Class android.content.ContextWrapper  
ComponentName android.content.ContextWrapper  Context android.content.ContextWrapper  
EmailResponse android.content.ContextWrapper  	Exception android.content.ContextWrapper  File android.content.ContextWrapper  FileOutputStream android.content.ContextWrapper  
FileReader android.content.ContextWrapper  FirebaseAuth android.content.ContextWrapper  Handler android.content.ContextWrapper  HashResponse android.content.ContextWrapper  HomeFragment android.content.ContextWrapper  IOException android.content.ContextWrapper  Intent android.content.ContextWrapper  LAYOUT_INFLATER_SERVICE android.content.ContextWrapper  LayoutInflater android.content.ContextWrapper  LayoutProgressBinding android.content.ContextWrapper  LayoutValidationBinding android.content.ContextWrapper  List android.content.ContextWrapper  Log android.content.ContextWrapper  Looper android.content.ContextWrapper  Map android.content.ContextWrapper  
MultipartBody android.content.ContextWrapper  MutableList android.content.ContextWrapper  ObjectAnimator android.content.ContextWrapper  OkHttpClient android.content.ContextWrapper  
PedingMailRes android.content.ContextWrapper  PendingMailRequest android.content.ContextWrapper  PixelFormat android.content.ContextWrapper  R android.content.ContextWrapper  Regex android.content.ContextWrapper  Request android.content.ContextWrapper  RequestBody android.content.ContextWrapper  Response android.content.ContextWrapper  Settings android.content.ContextWrapper  SharedPrefManager android.content.ContextWrapper  String android.content.ContextWrapper  
StringBuilder android.content.ContextWrapper  	TextUtils android.content.ContextWrapper  	Throwable android.content.ContextWrapper  Toast android.content.ContextWrapper  Uri android.content.ContextWrapper  View android.content.ContextWrapper  WINDOW_SERVICE android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  also android.content.ContextWrapper  android android.content.ContextWrapper  androidx android.content.ContextWrapper  
appendLine android.content.ContextWrapper  applicationContext android.content.ContextWrapper  checkAiResponse android.content.ContextWrapper  contains android.content.ContextWrapper  endsWith android.content.ContextWrapper  equals android.content.ContextWrapper  extractEmail android.content.ContextWrapper  extractEmailDetails android.content.ContextWrapper  
extractEmails android.content.ContextWrapper  findEmailNodes android.content.ContextWrapper  first android.content.ContextWrapper  firstOrNull android.content.ContextWrapper  	getHashId android.content.ContextWrapper  	getOrNull android.content.ContextWrapper  getSystemService android.content.ContextWrapper  getValue android.content.ContextWrapper  hasOverlayPermission android.content.ContextWrapper  invoke android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  isValidEmail android.content.ContextWrapper  java android.content.ContextWrapper  joinToString android.content.ContextWrapper  lazy android.content.ContextWrapper  let android.content.ContextWrapper  	lowercase android.content.ContextWrapper  map android.content.ContextWrapper  matches android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  mutableMapOf android.content.ContextWrapper  mutableSetOf android.content.ContextWrapper  
onBackPressed android.content.ContextWrapper  onCreate android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  readEmlFile android.content.ContextWrapper  removePrefix android.content.ContextWrapper  requestOverlayPermission android.content.ContextWrapper  set android.content.ContextWrapper  setContentView android.content.ContextWrapper  setResponsePopup android.content.ContextWrapper  sharedPrefManager android.content.ContextWrapper  showFragment android.content.ContextWrapper  split android.content.ContextWrapper  
splitMailData android.content.ContextWrapper  splitOutlookEmailData android.content.ContextWrapper  
startsWith android.content.ContextWrapper  toByteArray android.content.ContextWrapper  toList android.content.ContextWrapper  toMediaType android.content.ContextWrapper  toMediaTypeOrNull android.content.ContextWrapper  toRegex android.content.ContextWrapper  
toRequestBody android.content.ContextWrapper  toString android.content.ContextWrapper  trim android.content.ContextWrapper  
trimIndent android.content.ContextWrapper  until android.content.ContextWrapper  
uploadFile android.content.ContextWrapper  use android.content.ContextWrapper  FLAG_ACTIVITY_NEW_TASK android.content.Intent  addFlags android.content.Intent  equals android.content.Intent  flags android.content.Intent  getFLAGS android.content.Intent  getFlags android.content.Intent  setFlags android.content.Intent  PackageInfo android.content.pm  PackageManager android.content.pm  
minSdkVersion "android.content.pm.ApplicationInfo  targetSdkVersion "android.content.pm.ApplicationInfo  applicationInfo android.content.pm.PackageInfo  getLONGVersionCode android.content.pm.PackageInfo  getLongVersionCode android.content.pm.PackageInfo  longVersionCode android.content.pm.PackageInfo  setLongVersionCode android.content.pm.PackageInfo  versionCode android.content.pm.PackageInfo  versionName android.content.pm.PackageInfo  PERMISSION_GRANTED !android.content.pm.PackageManager  getLaunchIntentForPackage !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  Color android.graphics  PixelFormat android.graphics  BLACK android.graphics.Color  WHITE android.graphics.Color  TRANSLUCENT android.graphics.PixelFormat  Drawable android.graphics.drawable  ConnectivityManager android.net  Network android.net  NetworkCapabilities android.net  NetworkInfo android.net  Uri android.net  
activeNetwork android.net.ConnectivityManager  activeNetworkInfo android.net.ConnectivityManager  getACTIVENetwork android.net.ConnectivityManager  getACTIVENetworkInfo android.net.ConnectivityManager  getActiveNetwork android.net.ConnectivityManager  getActiveNetworkInfo android.net.ConnectivityManager  getNetworkCapabilities android.net.ConnectivityManager  setActiveNetwork android.net.ConnectivityManager  setActiveNetworkInfo android.net.ConnectivityManager  NET_CAPABILITY_INTERNET android.net.NetworkCapabilities  TRANSPORT_CELLULAR android.net.NetworkCapabilities  TRANSPORT_ETHERNET android.net.NetworkCapabilities  TRANSPORT_WIFI android.net.NetworkCapabilities  equals android.net.NetworkCapabilities  
hasCapability android.net.NetworkCapabilities  hasTransport android.net.NetworkCapabilities  equals android.net.NetworkInfo  getISConnected android.net.NetworkInfo  getIsConnected android.net.NetworkInfo  getTYPEName android.net.NetworkInfo  getTypeName android.net.NetworkInfo  isConnected android.net.NetworkInfo  setConnected android.net.NetworkInfo  setTypeName android.net.NetworkInfo  typeName android.net.NetworkInfo  parse android.net.Uri  WifiManager android.net.wifi  getISWifiEnabled android.net.wifi.WifiManager  getIsWifiEnabled android.net.wifi.WifiManager  
isWifiEnabled android.net.wifi.WifiManager  setWifiEnabled android.net.wifi.WifiManager  BatteryManager 
android.os  Build 
android.os  Bundle 
android.os  Handler 
android.os  Looper 
android.os  BATTERY_PROPERTY_CAPACITY android.os.BatteryManager   BATTERY_PROPERTY_CURRENT_AVERAGE android.os.BatteryManager  BATTERY_PROPERTY_CURRENT_NOW android.os.BatteryManager  
getISCharging android.os.BatteryManager  getIntProperty android.os.BatteryManager  
getIsCharging android.os.BatteryManager  
isCharging android.os.BatteryManager  setCharging android.os.BatteryManager  
BOOTLOADER android.os.Build  BRAND android.os.Build  DISPLAY android.os.Build  FINGERPRINT android.os.Build  HARDWARE android.os.Build  HOST android.os.Build  ID android.os.Build  MANUFACTURER android.os.Build  MODEL android.os.Build  PRODUCT android.os.Build  SERIAL android.os.Build  TAGS android.os.Build  TYPE android.os.Build  USER android.os.Build  VERSION android.os.Build  
VERSION_CODES android.os.Build  	getSerial android.os.Build  RELEASE android.os.Build.VERSION  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  N android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  P android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  equals android.os.Bundle  postDelayed android.os.Handler  
getMainLooper android.os.Looper  Settings android.provider  ACTION_ACCESSIBILITY_SETTINGS android.provider.Settings   ACTION_MANAGE_OVERLAY_PERMISSION android.provider.Settings  Global android.provider.Settings  Secure android.provider.Settings  canDrawOverlays android.provider.Settings  	getString  android.provider.Settings.Global  
ANDROID_ID  android.provider.Settings.Secure  ENABLED_ACCESSIBILITY_SERVICES  android.provider.Settings.Secure  	getString  android.provider.Settings.Secure  TelephonyManager android.telephony  NETWORK_TYPE_EDGE "android.telephony.TelephonyManager  NETWORK_TYPE_GPRS "android.telephony.TelephonyManager  NETWORK_TYPE_HSDPA "android.telephony.TelephonyManager  NETWORK_TYPE_HSPA "android.telephony.TelephonyManager  NETWORK_TYPE_HSUPA "android.telephony.TelephonyManager  NETWORK_TYPE_LTE "android.telephony.TelephonyManager  NETWORK_TYPE_NR "android.telephony.TelephonyManager  NETWORK_TYPE_UMTS "android.telephony.TelephonyManager  PHONE_TYPE_CDMA "android.telephony.TelephonyManager  PHONE_TYPE_GSM "android.telephony.TelephonyManager  PHONE_TYPE_SIP "android.telephony.TelephonyManager  getNETWORKCountryIso "android.telephony.TelephonyManager  getNETWORKOperatorName "android.telephony.TelephonyManager  getNETWORKType "android.telephony.TelephonyManager  getNetworkCountryIso "android.telephony.TelephonyManager  getNetworkOperatorName "android.telephony.TelephonyManager  getNetworkType "android.telephony.TelephonyManager  getPHONEType "android.telephony.TelephonyManager  getPhoneType "android.telephony.TelephonyManager  networkCountryIso "android.telephony.TelephonyManager  networkOperatorName "android.telephony.TelephonyManager  networkType "android.telephony.TelephonyManager  	phoneType "android.telephony.TelephonyManager  setNetworkCountryIso "android.telephony.TelephonyManager  setNetworkOperatorName "android.telephony.TelephonyManager  setNetworkType "android.telephony.TelephonyManager  setPhoneType "android.telephony.TelephonyManager  Editable android.text  Html android.text  Spanned android.text  	TextUtils android.text  TextWatcher android.text  getISNullOrBlank android.text.Editable  getIsNullOrBlank android.text.Editable  getTOString android.text.Editable  getToString android.text.Editable  
isNullOrBlank android.text.Editable  toString android.text.Editable  FROM_HTML_MODE_LEGACY android.text.Html  fromHtml android.text.Html  SimpleStringSplitter android.text.TextUtils  hasNext +android.text.TextUtils.SimpleStringSplitter  next +android.text.TextUtils.SimpleStringSplitter  	setString +android.text.TextUtils.SimpleStringSplitter  DisplayMetrics android.util  Log android.util  density android.util.DisplayMetrics  
densityDpi android.util.DisplayMetrics  heightPixels android.util.DisplayMetrics  
scaledDensity android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  w android.util.Log  Display android.view  LayoutInflater android.view  View android.view  	ViewGroup android.view  Window android.view  
WindowManager android.view  AccessibilityService  android.view.ContextThemeWrapper  ActivityDashboardBinding  android.view.ContextThemeWrapper  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityYahooAuthBinding  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Call  android.view.ContextThemeWrapper  Callback  android.view.ContextThemeWrapper  Class  android.view.ContextThemeWrapper  
ComponentName  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  FirebaseAuth  android.view.ContextThemeWrapper  HomeFragment  android.view.ContextThemeWrapper  IOException  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  ObjectAnimator  android.view.ContextThemeWrapper  OkHttpClient  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  Request  android.view.ContextThemeWrapper  Response  android.view.ContextThemeWrapper  Settings  android.view.ContextThemeWrapper  SharedPrefManager  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  	TextUtils  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  equals  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  
isNullOrEmpty  android.view.ContextThemeWrapper  lazy  android.view.ContextThemeWrapper  
onBackPressed  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  showFragment  android.view.ContextThemeWrapper  
getMetrics android.view.Display  getRealMetrics android.view.Display  from android.view.LayoutInflater  VISIBLE android.view.View  addTextChangedListener android.view.View  animateY android.view.View  context android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  getLET android.view.View  getLet android.view.View  
invalidate android.view.View  let android.view.View  setAnimation android.view.View  setBackgroundResource android.view.View  setCardBackgroundColor android.view.View  
setContext android.view.View  setDrawEntryLabels android.view.View  setEntryLabelColor android.view.View  setImageResource android.view.View  setOnClickListener android.view.View  
setPadding android.view.View  setText android.view.View  setTextColor android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  animateY android.view.ViewGroup  context android.view.ViewGroup  
getCONTEXT android.view.ViewGroup  
getContext android.view.ViewGroup  
invalidate android.view.ViewGroup  setBackgroundResource android.view.ViewGroup  setCardBackgroundColor android.view.ViewGroup  
setContext android.view.ViewGroup  setDrawEntryLabels android.view.ViewGroup  setEntryLabelColor android.view.ViewGroup  setOnClickListener android.view.ViewGroup  FEATURE_NO_TITLE android.view.Window  setBackgroundDrawableResource android.view.Window  setDimAmount android.view.Window  LayoutParams android.view.WindowManager  addView android.view.WindowManager  defaultDisplay android.view.WindowManager  getDEFAULTDisplay android.view.WindowManager  getDefaultDisplay android.view.WindowManager  
removeView android.view.WindowManager  setDefaultDisplay android.view.WindowManager  FLAG_DIM_BEHIND 'android.view.WindowManager.LayoutParams  FLAG_NOT_FOCUSABLE 'android.view.WindowManager.LayoutParams  MATCH_PARENT 'android.view.WindowManager.LayoutParams  TYPE_APPLICATION_OVERLAY 'android.view.WindowManager.LayoutParams  
TYPE_PHONE 'android.view.WindowManager.LayoutParams  	dimAmount 'android.view.WindowManager.LayoutParams  AccessibilityEvent android.view.accessibility  AccessibilityNodeInfo android.view.accessibility  TYPE_VIEW_CLICKED -android.view.accessibility.AccessibilityEvent  TYPE_WINDOW_CONTENT_CHANGED -android.view.accessibility.AccessibilityEvent  TYPE_WINDOW_STATE_CHANGED -android.view.accessibility.AccessibilityEvent  equals -android.view.accessibility.AccessibilityEvent  	eventType -android.view.accessibility.AccessibilityEvent  getEVENTType -android.view.accessibility.AccessibilityEvent  getEventType -android.view.accessibility.AccessibilityEvent  getPACKAGEName -android.view.accessibility.AccessibilityEvent  getPackageName -android.view.accessibility.AccessibilityEvent  	getSOURCE -android.view.accessibility.AccessibilityEvent  	getSource -android.view.accessibility.AccessibilityEvent  getTEXT -android.view.accessibility.AccessibilityEvent  getText -android.view.accessibility.AccessibilityEvent  packageName -android.view.accessibility.AccessibilityEvent  setEventType -android.view.accessibility.AccessibilityEvent  setPackageName -android.view.accessibility.AccessibilityEvent  	setSource -android.view.accessibility.AccessibilityEvent  setText -android.view.accessibility.AccessibilityEvent  source -android.view.accessibility.AccessibilityEvent  text -android.view.accessibility.AccessibilityEvent  
childCount 0android.view.accessibility.AccessibilityNodeInfo  	className 0android.view.accessibility.AccessibilityNodeInfo  contentDescription 0android.view.accessibility.AccessibilityNodeInfo  equals 0android.view.accessibility.AccessibilityNodeInfo  
getCHILDCount 0android.view.accessibility.AccessibilityNodeInfo  getCLASSName 0android.view.accessibility.AccessibilityNodeInfo  getCONTENTDescription 0android.view.accessibility.AccessibilityNodeInfo  getChild 0android.view.accessibility.AccessibilityNodeInfo  
getChildCount 0android.view.accessibility.AccessibilityNodeInfo  getClassName 0android.view.accessibility.AccessibilityNodeInfo  getContentDescription 0android.view.accessibility.AccessibilityNodeInfo  getTEXT 0android.view.accessibility.AccessibilityNodeInfo  getText 0android.view.accessibility.AccessibilityNodeInfo  getVIEWIdResourceName 0android.view.accessibility.AccessibilityNodeInfo  getViewIdResourceName 0android.view.accessibility.AccessibilityNodeInfo  
setChildCount 0android.view.accessibility.AccessibilityNodeInfo  setClassName 0android.view.accessibility.AccessibilityNodeInfo  setContentDescription 0android.view.accessibility.AccessibilityNodeInfo  setText 0android.view.accessibility.AccessibilityNodeInfo  setViewIdResourceName 0android.view.accessibility.AccessibilityNodeInfo  text 0android.view.accessibility.AccessibilityNodeInfo  viewIdResourceName 0android.view.accessibility.AccessibilityNodeInfo  Button android.widget  
ScrollView android.widget  TextView android.widget  Toast android.widget  setOnClickListener android.widget.Button  addTextChangedListener android.widget.EditText  setText android.widget.EditText  setCardBackgroundColor android.widget.FrameLayout  setOnClickListener android.widget.FrameLayout  setAnimation android.widget.ImageView  setImageResource android.widget.ImageView  addTextChangedListener android.widget.TextView  getTEXT android.widget.TextView  getText android.widget.TextView  setOnClickListener android.widget.TextView  setText android.widget.TextView  setTextColor android.widget.TextView  text android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  AccessibilityService #androidx.activity.ComponentActivity  ActivityDashboardBinding #androidx.activity.ComponentActivity  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityYahooAuthBinding #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Call #androidx.activity.ComponentActivity  Callback #androidx.activity.ComponentActivity  Class #androidx.activity.ComponentActivity  
ComponentName #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  FirebaseAuth #androidx.activity.ComponentActivity  HomeFragment #androidx.activity.ComponentActivity  IOException #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  ObjectAnimator #androidx.activity.ComponentActivity  OkHttpClient #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  Request #androidx.activity.ComponentActivity  Response #androidx.activity.ComponentActivity  Settings #androidx.activity.ComponentActivity  SharedPrefManager #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  	TextUtils #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  equals #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  invoke #androidx.activity.ComponentActivity  
isNullOrEmpty #androidx.activity.ComponentActivity  lazy #androidx.activity.ComponentActivity  
onBackPressed #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  showFragment #androidx.activity.ComponentActivity  DrawableRes androidx.annotation  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  
setCancelable *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setView *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  AccessibilityService (androidx.appcompat.app.AppCompatActivity  ActivityDashboardBinding (androidx.appcompat.app.AppCompatActivity  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityYahooAuthBinding (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  Call (androidx.appcompat.app.AppCompatActivity  Callback (androidx.appcompat.app.AppCompatActivity  Class (androidx.appcompat.app.AppCompatActivity  
ComponentName (androidx.appcompat.app.AppCompatActivity  Context (androidx.appcompat.app.AppCompatActivity  FirebaseAuth (androidx.appcompat.app.AppCompatActivity  HomeFragment (androidx.appcompat.app.AppCompatActivity  IOException (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  ObjectAnimator (androidx.appcompat.app.AppCompatActivity  OkHttpClient (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  Request (androidx.appcompat.app.AppCompatActivity  Response (androidx.appcompat.app.AppCompatActivity  Settings (androidx.appcompat.app.AppCompatActivity  SharedPrefManager (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  	TextUtils (androidx.appcompat.app.AppCompatActivity  androidx (androidx.appcompat.app.AppCompatActivity  equals (androidx.appcompat.app.AppCompatActivity  getValue (androidx.appcompat.app.AppCompatActivity  invoke (androidx.appcompat.app.AppCompatActivity  
isNullOrEmpty (androidx.appcompat.app.AppCompatActivity  lazy (androidx.appcompat.app.AppCompatActivity  
onBackPressed (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  provideDelegate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  showFragment (androidx.appcompat.app.AppCompatActivity  AccessibilityService .androidx.appcompat.app.AppCompatDialogFragment  	ApiClient .androidx.appcompat.app.AppCompatDialogFragment  
ApiService .androidx.appcompat.app.AppCompatDialogFragment  AppConstant .androidx.appcompat.app.AppCompatDialogFragment  Boolean .androidx.appcompat.app.AppCompatDialogFragment  Bundle .androidx.appcompat.app.AppCompatDialogFragment  Call .androidx.appcompat.app.AppCompatDialogFragment  Callback .androidx.appcompat.app.AppCompatDialogFragment  CharSequence .androidx.appcompat.app.AppCompatDialogFragment  Class .androidx.appcompat.app.AppCompatDialogFragment  
CommonUtil .androidx.appcompat.app.AppCompatDialogFragment  
ComponentName .androidx.appcompat.app.AppCompatDialogFragment  Context .androidx.appcompat.app.AppCompatDialogFragment  
ContextCompat .androidx.appcompat.app.AppCompatDialogFragment  DisputeRaiseRequest .androidx.appcompat.app.AppCompatDialogFragment  DisputeRaiseResponse .androidx.appcompat.app.AppCompatDialogFragment  Editable .androidx.appcompat.app.AppCompatDialogFragment  	Exception .androidx.appcompat.app.AppCompatDialogFragment  FragmentRaiseBottomSheetBinding .androidx.appcompat.app.AppCompatDialogFragment  FragmentSuggetionBottomBinding .androidx.appcompat.app.AppCompatDialogFragment  !FragmentWarningBottomSheetBinding .androidx.appcompat.app.AppCompatDialogFragment  Html .androidx.appcompat.app.AppCompatDialogFragment  Int .androidx.appcompat.app.AppCompatDialogFragment  Intent .androidx.appcompat.app.AppCompatDialogFragment  LayoutInflater .androidx.appcompat.app.AppCompatDialogFragment  Log .androidx.appcompat.app.AppCompatDialogFragment  NewGmailAccessibilityService .androidx.appcompat.app.AppCompatDialogFragment  
PedingMailRes .androidx.appcompat.app.AppCompatDialogFragment  PendingMailRequest .androidx.appcompat.app.AppCompatDialogFragment  R .androidx.appcompat.app.AppCompatDialogFragment  Response .androidx.appcompat.app.AppCompatDialogFragment  Settings .androidx.appcompat.app.AppCompatDialogFragment  SharedPrefManager .androidx.appcompat.app.AppCompatDialogFragment  TAG .androidx.appcompat.app.AppCompatDialogFragment  	TextUtils .androidx.appcompat.app.AppCompatDialogFragment  TextWatcher .androidx.appcompat.app.AppCompatDialogFragment  	Throwable .androidx.appcompat.app.AppCompatDialogFragment  Toast .androidx.appcompat.app.AppCompatDialogFragment  View .androidx.appcompat.app.AppCompatDialogFragment  	ViewGroup .androidx.appcompat.app.AppCompatDialogFragment  binding .androidx.appcompat.app.AppCompatDialogFragment  equals .androidx.appcompat.app.AppCompatDialogFragment  filter .androidx.appcompat.app.AppCompatDialogFragment  getValue .androidx.appcompat.app.AppCompatDialogFragment  isAccessibilityServiceEnabled .androidx.appcompat.app.AppCompatDialogFragment  isBlank .androidx.appcompat.app.AppCompatDialogFragment  
isNotEmpty .androidx.appcompat.app.AppCompatDialogFragment  
isNullOrBlank .androidx.appcompat.app.AppCompatDialogFragment  
isNullOrEmpty .androidx.appcompat.app.AppCompatDialogFragment  java .androidx.appcompat.app.AppCompatDialogFragment  lazy .androidx.appcompat.app.AppCompatDialogFragment  
onDestroyView .androidx.appcompat.app.AppCompatDialogFragment  
onViewCreated .androidx.appcompat.app.AppCompatDialogFragment  openAccessibilitySettings .androidx.appcompat.app.AppCompatDialogFragment  openGmailApp .androidx.appcompat.app.AppCompatDialogFragment  provideDelegate .androidx.appcompat.app.AppCompatDialogFragment  requireActivity .androidx.appcompat.app.AppCompatDialogFragment  requireContext .androidx.appcompat.app.AppCompatDialogFragment  
setCancelable .androidx.appcompat.app.AppCompatDialogFragment  setFormData .androidx.appcompat.app.AppCompatDialogFragment  show .androidx.appcompat.app.AppCompatDialogFragment  showSnackbar .androidx.appcompat.app.AppCompatDialogFragment  split .androidx.appcompat.app.AppCompatDialogFragment  
startActivity .androidx.appcompat.app.AppCompatDialogFragment  
submitForm .androidx.appcompat.app.AppCompatDialogFragment  toRegex .androidx.appcompat.app.AppCompatDialogFragment  toString .androidx.appcompat.app.AppCompatDialogFragment  trim .androidx.appcompat.app.AppCompatDialogFragment  
validation .androidx.appcompat.app.AppCompatDialogFragment  AppCompatButton androidx.appcompat.widget  
background )androidx.appcompat.widget.AppCompatButton  
getBACKGROUND )androidx.appcompat.widget.AppCompatButton  
getBackground )androidx.appcompat.widget.AppCompatButton  
getVISIBILITY )androidx.appcompat.widget.AppCompatButton  
getVisibility )androidx.appcompat.widget.AppCompatButton  
setBackground )androidx.appcompat.widget.AppCompatButton  setOnClickListener )androidx.appcompat.widget.AppCompatButton  
setVisibility )androidx.appcompat.widget.AppCompatButton  
visibility )androidx.appcompat.widget.AppCompatButton  addTextChangedListener +androidx.appcompat.widget.AppCompatEditText  getTEXT +androidx.appcompat.widget.AppCompatEditText  getText +androidx.appcompat.widget.AppCompatEditText  setText +androidx.appcompat.widget.AppCompatEditText  text +androidx.appcompat.widget.AppCompatEditText  setAnimation ,androidx.appcompat.widget.AppCompatImageView  setImageResource ,androidx.appcompat.widget.AppCompatImageView  getTEXT +androidx.appcompat.widget.AppCompatTextView  getText +androidx.appcompat.widget.AppCompatTextView  setText +androidx.appcompat.widget.AppCompatTextView  setTextColor +androidx.appcompat.widget.AppCompatTextView  text +androidx.appcompat.widget.AppCompatTextView  CardView androidx.cardview.widget  setCardBackgroundColor !androidx.cardview.widget.CardView  setOnClickListener !androidx.cardview.widget.CardView  ConstraintLayout  androidx.constraintlayout.widget  setBackgroundResource 1androidx.constraintlayout.widget.ConstraintLayout  ActivityCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  requestPermissions  androidx.core.app.ActivityCompat  AccessibilityService #androidx.core.app.ComponentActivity  ActivityDashboardBinding #androidx.core.app.ComponentActivity  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityYahooAuthBinding #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Call #androidx.core.app.ComponentActivity  Callback #androidx.core.app.ComponentActivity  Class #androidx.core.app.ComponentActivity  
ComponentName #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  FirebaseAuth #androidx.core.app.ComponentActivity  HomeFragment #androidx.core.app.ComponentActivity  IOException #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  ObjectAnimator #androidx.core.app.ComponentActivity  OkHttpClient #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  Request #androidx.core.app.ComponentActivity  Response #androidx.core.app.ComponentActivity  Settings #androidx.core.app.ComponentActivity  SharedPrefManager #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  	TextUtils #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  equals #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  invoke #androidx.core.app.ComponentActivity  
isNullOrEmpty #androidx.core.app.ComponentActivity  lazy #androidx.core.app.ComponentActivity  
onBackPressed #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  showFragment #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getColor #androidx.core.content.ContextCompat  getDrawable #androidx.core.content.ContextCompat  Insets androidx.core.graphics  bottom androidx.core.graphics.Insets  left androidx.core.graphics.Insets  right androidx.core.graphics.Insets  top androidx.core.graphics.Insets  
ViewCompat androidx.core.view  WindowInsetsCompat androidx.core.view  <SAM-CONSTRUCTOR> .androidx.core.view.OnApplyWindowInsetsListener  setOnApplyWindowInsetsListener androidx.core.view.ViewCompat  Type %androidx.core.view.WindowInsetsCompat  	getInsets %androidx.core.view.WindowInsetsCompat  
systemBars *androidx.core.view.WindowInsetsCompat.Type  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  FragmentManager androidx.fragment.app  AccessibilityService $androidx.fragment.app.DialogFragment  	ApiClient $androidx.fragment.app.DialogFragment  
ApiService $androidx.fragment.app.DialogFragment  AppConstant $androidx.fragment.app.DialogFragment  Boolean $androidx.fragment.app.DialogFragment  Bundle $androidx.fragment.app.DialogFragment  Call $androidx.fragment.app.DialogFragment  Callback $androidx.fragment.app.DialogFragment  CharSequence $androidx.fragment.app.DialogFragment  Class $androidx.fragment.app.DialogFragment  
CommonUtil $androidx.fragment.app.DialogFragment  
ComponentName $androidx.fragment.app.DialogFragment  Context $androidx.fragment.app.DialogFragment  
ContextCompat $androidx.fragment.app.DialogFragment  DisputeRaiseRequest $androidx.fragment.app.DialogFragment  DisputeRaiseResponse $androidx.fragment.app.DialogFragment  Editable $androidx.fragment.app.DialogFragment  	Exception $androidx.fragment.app.DialogFragment  FragmentRaiseBottomSheetBinding $androidx.fragment.app.DialogFragment  FragmentSuggetionBottomBinding $androidx.fragment.app.DialogFragment  !FragmentWarningBottomSheetBinding $androidx.fragment.app.DialogFragment  Html $androidx.fragment.app.DialogFragment  Int $androidx.fragment.app.DialogFragment  Intent $androidx.fragment.app.DialogFragment  LayoutInflater $androidx.fragment.app.DialogFragment  Log $androidx.fragment.app.DialogFragment  NewGmailAccessibilityService $androidx.fragment.app.DialogFragment  
PedingMailRes $androidx.fragment.app.DialogFragment  PendingMailRequest $androidx.fragment.app.DialogFragment  R $androidx.fragment.app.DialogFragment  Response $androidx.fragment.app.DialogFragment  Settings $androidx.fragment.app.DialogFragment  SharedPrefManager $androidx.fragment.app.DialogFragment  TAG $androidx.fragment.app.DialogFragment  	TextUtils $androidx.fragment.app.DialogFragment  TextWatcher $androidx.fragment.app.DialogFragment  	Throwable $androidx.fragment.app.DialogFragment  Toast $androidx.fragment.app.DialogFragment  View $androidx.fragment.app.DialogFragment  	ViewGroup $androidx.fragment.app.DialogFragment  binding $androidx.fragment.app.DialogFragment  equals $androidx.fragment.app.DialogFragment  filter $androidx.fragment.app.DialogFragment  getValue $androidx.fragment.app.DialogFragment  isAccessibilityServiceEnabled $androidx.fragment.app.DialogFragment  isBlank $androidx.fragment.app.DialogFragment  
isNotEmpty $androidx.fragment.app.DialogFragment  
isNullOrBlank $androidx.fragment.app.DialogFragment  
isNullOrEmpty $androidx.fragment.app.DialogFragment  java $androidx.fragment.app.DialogFragment  lazy $androidx.fragment.app.DialogFragment  
onDestroyView $androidx.fragment.app.DialogFragment  
onViewCreated $androidx.fragment.app.DialogFragment  openAccessibilitySettings $androidx.fragment.app.DialogFragment  openGmailApp $androidx.fragment.app.DialogFragment  provideDelegate $androidx.fragment.app.DialogFragment  requireActivity $androidx.fragment.app.DialogFragment  requireContext $androidx.fragment.app.DialogFragment  
setCancelable $androidx.fragment.app.DialogFragment  setFormData $androidx.fragment.app.DialogFragment  show $androidx.fragment.app.DialogFragment  showSnackbar $androidx.fragment.app.DialogFragment  split $androidx.fragment.app.DialogFragment  
startActivity $androidx.fragment.app.DialogFragment  
submitForm $androidx.fragment.app.DialogFragment  toRegex $androidx.fragment.app.DialogFragment  toString $androidx.fragment.app.DialogFragment  trim $androidx.fragment.app.DialogFragment  
validation $androidx.fragment.app.DialogFragment  AccessibilityService androidx.fragment.app.Fragment  ActivityCompat androidx.fragment.app.Fragment  ActivityGraphFragment androidx.fragment.app.Fragment  ActivityGraphResponse androidx.fragment.app.Fragment  AlertDialog androidx.fragment.app.Fragment  	ApiClient androidx.fragment.app.Fragment  
ApiService androidx.fragment.app.Fragment  AppConstant androidx.fragment.app.Fragment  Array androidx.fragment.app.Fragment  Boolean androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  Call androidx.fragment.app.Fragment  Callback androidx.fragment.app.Fragment  CharSequence androidx.fragment.app.Fragment  Class androidx.fragment.app.Fragment  Color androidx.fragment.app.Fragment  
ColorTemplate androidx.fragment.app.Fragment  
CommonRequest androidx.fragment.app.Fragment  
CommonUtil androidx.fragment.app.Fragment  
ComponentName androidx.fragment.app.Fragment  Context androidx.fragment.app.Fragment  
ContextCompat androidx.fragment.app.Fragment  DashboardActivity androidx.fragment.app.Fragment  DashboardMenuAdapter androidx.fragment.app.Fragment  DashboardMenuModel androidx.fragment.app.Fragment  DeviceDetailsFragment androidx.fragment.app.Fragment  
DeviceInfo androidx.fragment.app.Fragment  DeviceInfoHelper androidx.fragment.app.Fragment  Dispatchers androidx.fragment.app.Fragment  DisputeListAdapter androidx.fragment.app.Fragment  DisputeMailistResponse androidx.fragment.app.Fragment  DisputeMaillistFragment androidx.fragment.app.Fragment  DisputeRaiseRequest androidx.fragment.app.Fragment  DisputeRaiseResponse androidx.fragment.app.Fragment  Editable androidx.fragment.app.Fragment  	Exception androidx.fragment.app.Fragment  FragmentActivityGraphBinding androidx.fragment.app.Fragment  FragmentDeviceDetailsBinding androidx.fragment.app.Fragment  FragmentDisputeMaillistBinding androidx.fragment.app.Fragment  FragmentHomeBinding androidx.fragment.app.Fragment  FragmentRaiseBottomSheetBinding androidx.fragment.app.Fragment  FragmentSpamMailBinding androidx.fragment.app.Fragment  FragmentSuggetionBottomBinding androidx.fragment.app.Fragment  !FragmentWarningBottomSheetBinding androidx.fragment.app.Fragment  GridLayoutManager androidx.fragment.app.Fragment  Html androidx.fragment.app.Fragment  Int androidx.fragment.app.Fragment  IntArray androidx.fragment.app.Fragment  Intent androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  LayoutSpamDetailBinding androidx.fragment.app.Fragment  Log androidx.fragment.app.Fragment  Long androidx.fragment.app.Fragment  Manifest androidx.fragment.app.Fragment  NewGmailAccessibilityService androidx.fragment.app.Fragment  PackageManager androidx.fragment.app.Fragment  
PedingMailRes androidx.fragment.app.Fragment  PendingMailRequest androidx.fragment.app.Fragment  PieData androidx.fragment.app.Fragment  
PieDataSet androidx.fragment.app.Fragment  PieEntry androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  RaiseBottomSheetFragment androidx.fragment.app.Fragment  Response androidx.fragment.app.Fragment  Results androidx.fragment.app.Fragment  Settings androidx.fragment.app.Fragment  SharedPrefManager androidx.fragment.app.Fragment  SpamMailAdapter androidx.fragment.app.Fragment  SpamMailFragment androidx.fragment.app.Fragment  SpamMailResponse androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  SuggetionBottomFragment androidx.fragment.app.Fragment  TAG androidx.fragment.app.Fragment  	TextUtils androidx.fragment.app.Fragment  TextWatcher androidx.fragment.app.Fragment  	Throwable androidx.fragment.app.Fragment  Toast androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  
ViewCompat androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  WarningBottomSheetFragment androidx.fragment.app.Fragment  WindowInsetsCompat androidx.fragment.app.Fragment  
appendLine androidx.fragment.app.Fragment  arrayOf androidx.fragment.app.Fragment  binding androidx.fragment.app.Fragment  buildString androidx.fragment.app.Fragment  checkAndRequestPermissions androidx.fragment.app.Fragment  com androidx.fragment.app.Fragment  deviceInfoHelper androidx.fragment.app.Fragment  displayDeviceInfo androidx.fragment.app.Fragment  equals androidx.fragment.app.Fragment  filter androidx.fragment.app.Fragment  
filterIndexed androidx.fragment.app.Fragment  format androidx.fragment.app.Fragment  formatBytes androidx.fragment.app.Fragment  
getAcityGraph androidx.fragment.app.Fragment  getDisputelist androidx.fragment.app.Fragment  getSpamMail androidx.fragment.app.Fragment  getValue androidx.fragment.app.Fragment  isAccessibilityServiceEnabled androidx.fragment.app.Fragment  isBlank androidx.fragment.app.Fragment  
isNotEmpty androidx.fragment.app.Fragment  
isNullOrBlank androidx.fragment.app.Fragment  
isNullOrEmpty androidx.fragment.app.Fragment  java androidx.fragment.app.Fragment  launch androidx.fragment.app.Fragment  lazy androidx.fragment.app.Fragment  lifecycleScope androidx.fragment.app.Fragment  listOf androidx.fragment.app.Fragment  loadDeviceInfo androidx.fragment.app.Fragment  map androidx.fragment.app.Fragment  mapOf androidx.fragment.app.Fragment  menus androidx.fragment.app.Fragment  
onDestroyView androidx.fragment.app.Fragment  onRequestPermissionsResult androidx.fragment.app.Fragment  onResume androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  openAccessibilitySettings androidx.fragment.app.Fragment  openGmailApp androidx.fragment.app.Fragment  provideDelegate androidx.fragment.app.Fragment  repeat androidx.fragment.app.Fragment  requireActivity androidx.fragment.app.Fragment  requireContext androidx.fragment.app.Fragment  
setCancelable androidx.fragment.app.Fragment  setFormData androidx.fragment.app.Fragment  show androidx.fragment.app.Fragment  showCustomEmailDialog androidx.fragment.app.Fragment  showSnackbar androidx.fragment.app.Fragment  split androidx.fragment.app.Fragment  
startActivity androidx.fragment.app.Fragment  
submitForm androidx.fragment.app.Fragment  to androidx.fragment.app.Fragment  toList androidx.fragment.app.Fragment  toRegex androidx.fragment.app.Fragment  toString androidx.fragment.app.Fragment  toTypedArray androidx.fragment.app.Fragment  trim androidx.fragment.app.Fragment  
validation androidx.fragment.app.Fragment  withContext androidx.fragment.app.Fragment  AccessibilityService &androidx.fragment.app.FragmentActivity  ActivityDashboardBinding &androidx.fragment.app.FragmentActivity  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityYahooAuthBinding &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  Call &androidx.fragment.app.FragmentActivity  Callback &androidx.fragment.app.FragmentActivity  Class &androidx.fragment.app.FragmentActivity  
ComponentName &androidx.fragment.app.FragmentActivity  Context &androidx.fragment.app.FragmentActivity  FirebaseAuth &androidx.fragment.app.FragmentActivity  HomeFragment &androidx.fragment.app.FragmentActivity  IOException &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  ObjectAnimator &androidx.fragment.app.FragmentActivity  OkHttpClient &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  Request &androidx.fragment.app.FragmentActivity  Response &androidx.fragment.app.FragmentActivity  Settings &androidx.fragment.app.FragmentActivity  SharedPrefManager &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  	TextUtils &androidx.fragment.app.FragmentActivity  androidx &androidx.fragment.app.FragmentActivity  equals &androidx.fragment.app.FragmentActivity  getSUPPORTFragmentManager &androidx.fragment.app.FragmentActivity  getSupportFragmentManager &androidx.fragment.app.FragmentActivity  getValue &androidx.fragment.app.FragmentActivity  invoke &androidx.fragment.app.FragmentActivity  
isNullOrEmpty &androidx.fragment.app.FragmentActivity  lazy &androidx.fragment.app.FragmentActivity  
onBackPressed &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  provideDelegate &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  setSupportFragmentManager &androidx.fragment.app.FragmentActivity  showFragment &androidx.fragment.app.FragmentActivity  supportFragmentManager &androidx.fragment.app.FragmentActivity  backStackEntryCount %androidx.fragment.app.FragmentManager  beginTransaction %androidx.fragment.app.FragmentManager  getBACKStackEntryCount %androidx.fragment.app.FragmentManager  getBackStackEntryCount %androidx.fragment.app.FragmentManager  popBackStack %androidx.fragment.app.FragmentManager  setBackStackEntryCount %androidx.fragment.app.FragmentManager  addToBackStack )androidx.fragment.app.FragmentTransaction  commit )androidx.fragment.app.FragmentTransaction  replace )androidx.fragment.app.FragmentTransaction  LifecycleCoroutineScope androidx.lifecycle  lifecycleScope androidx.lifecycle  	getLAUNCH *androidx.lifecycle.LifecycleCoroutineScope  	getLaunch *androidx.lifecycle.LifecycleCoroutineScope  launch *androidx.lifecycle.LifecycleCoroutineScope  GridLayoutManager androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
LayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  
getADAPTER )androidx.recyclerview.widget.RecyclerView  
getAdapter )androidx.recyclerview.widget.RecyclerView  getLAYOUTManager )androidx.recyclerview.widget.RecyclerView  getLayoutManager )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  
setAdapter )androidx.recyclerview.widget.RecyclerView  setLayoutManager )androidx.recyclerview.widget.RecyclerView  
ContextCompat 1androidx.recyclerview.widget.RecyclerView.Adapter  DashboardMenuModel 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemDashboardMenuBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemDisputeListBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemSpamMailBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  List 1androidx.recyclerview.widget.RecyclerView.Adapter  Log 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  Results 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyItemChanged 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemDashboardMenuBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemDisputeListBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemSpamMailBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  setAnimation %com.airbnb.lottie.LottieAnimationView  animateY )com.github.mikephil.charting.charts.Chart  
invalidate )com.github.mikephil.charting.charts.Chart  setDrawEntryLabels )com.github.mikephil.charting.charts.Chart  setEntryLabelColor )com.github.mikephil.charting.charts.Chart  animateY ,com.github.mikephil.charting.charts.PieChart  
centerText ,com.github.mikephil.charting.charts.PieChart  data ,com.github.mikephil.charting.charts.PieChart  description ,com.github.mikephil.charting.charts.PieChart  
getCENTERText ,com.github.mikephil.charting.charts.PieChart  
getCenterText ,com.github.mikephil.charting.charts.PieChart  getDATA ,com.github.mikephil.charting.charts.PieChart  getDESCRIPTION ,com.github.mikephil.charting.charts.PieChart  getData ,com.github.mikephil.charting.charts.PieChart  getDescription ,com.github.mikephil.charting.charts.PieChart  	getLEGEND ,com.github.mikephil.charting.charts.PieChart  	getLegend ,com.github.mikephil.charting.charts.PieChart  
invalidate ,com.github.mikephil.charting.charts.PieChart  legend ,com.github.mikephil.charting.charts.PieChart  
setCenterText ,com.github.mikephil.charting.charts.PieChart  setData ,com.github.mikephil.charting.charts.PieChart  setDescription ,com.github.mikephil.charting.charts.PieChart  setDrawEntryLabels ,com.github.mikephil.charting.charts.PieChart  setEntryLabelColor ,com.github.mikephil.charting.charts.PieChart  	setLegend ,com.github.mikephil.charting.charts.PieChart  animateY 5com.github.mikephil.charting.charts.PieRadarChartBase  
invalidate 5com.github.mikephil.charting.charts.PieRadarChartBase  setDrawEntryLabels 5com.github.mikephil.charting.charts.PieRadarChartBase  setEntryLabelColor 5com.github.mikephil.charting.charts.PieRadarChartBase  getISEnabled 3com.github.mikephil.charting.components.Description  getIsEnabled 3com.github.mikephil.charting.components.Description  	isEnabled 3com.github.mikephil.charting.components.Description  
setEnabled 3com.github.mikephil.charting.components.Description  getTEXTColor .com.github.mikephil.charting.components.Legend  getTextColor .com.github.mikephil.charting.components.Legend  setTextColor .com.github.mikephil.charting.components.Legend  	textColor .com.github.mikephil.charting.components.Legend  PieData !com.github.mikephil.charting.data  
PieDataSet !com.github.mikephil.charting.data  PieEntry !com.github.mikephil.charting.data  colors ,com.github.mikephil.charting.data.PieDataSet  	getCOLORS ,com.github.mikephil.charting.data.PieDataSet  	getColors ,com.github.mikephil.charting.data.PieDataSet  getVALUETextColor ,com.github.mikephil.charting.data.PieDataSet  getVALUETextSize ,com.github.mikephil.charting.data.PieDataSet  getValueTextColor ,com.github.mikephil.charting.data.PieDataSet  getValueTextSize ,com.github.mikephil.charting.data.PieDataSet  	setColors ,com.github.mikephil.charting.data.PieDataSet  setValueTextColor ,com.github.mikephil.charting.data.PieDataSet  setValueTextSize ,com.github.mikephil.charting.data.PieDataSet  valueTextColor ,com.github.mikephil.charting.data.PieDataSet  
valueTextSize ,com.github.mikephil.charting.data.PieDataSet  
ColorTemplate "com.github.mikephil.charting.utils  MATERIAL_COLORS 0com.github.mikephil.charting.utils.ColorTemplate  Wrappers &com.google.android.gms.common.wrappers  packageManager /com.google.android.gms.common.wrappers.Wrappers  BottomSheetDialogFragment 'com.google.android.material.bottomsheet  AccessibilityService Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  	ApiClient Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
ApiService Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  AppConstant Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Boolean Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Bundle Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Call Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Callback Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  CharSequence Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Class Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
CommonUtil Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
ComponentName Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Context Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
ContextCompat Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  DisputeRaiseRequest Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  DisputeRaiseResponse Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Editable Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  	Exception Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  FragmentRaiseBottomSheetBinding Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  FragmentSuggetionBottomBinding Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  !FragmentWarningBottomSheetBinding Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Html Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Int Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Intent Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  LayoutInflater Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Log Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  NewGmailAccessibilityService Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
PedingMailRes Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  PendingMailRequest Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  R Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Response Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Settings Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  SharedPrefManager Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  TAG Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  	TextUtils Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  TextWatcher Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  	Throwable Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Toast Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  View Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  	ViewGroup Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  binding Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  equals Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  filter Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  getValue Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  isAccessibilityServiceEnabled Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  isBlank Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
isNotEmpty Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
isNullOrBlank Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
isNullOrEmpty Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  java Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  lazy Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
onDestroyView Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
onViewCreated Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  openAccessibilitySettings Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  openGmailApp Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  provideDelegate Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  requireActivity Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  requireContext Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
setCancelable Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  setFormData Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  show Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  showSnackbar Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  split Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
startActivity Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
submitForm Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  toRegex Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  toString Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  trim Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  
validation Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  Snackbar $com.google.android.material.snackbar  	setAction ;com.google.android.material.snackbar.BaseTransientBottomBar  setBackgroundTint ;com.google.android.material.snackbar.BaseTransientBottomBar  show ;com.google.android.material.snackbar.BaseTransientBottomBar  LENGTH_LONG -com.google.android.material.snackbar.Snackbar  make -com.google.android.material.snackbar.Snackbar  	setAction -com.google.android.material.snackbar.Snackbar  setBackgroundTint -com.google.android.material.snackbar.Snackbar  show -com.google.android.material.snackbar.Snackbar  FirebaseAuth com.google.firebase.auth  OAuthCredential com.google.firebase.auth  
OAuthProvider com.google.firebase.auth  getInstance %com.google.firebase.auth.FirebaseAuth  SerializedName com.google.gson.annotations  CommonUtils #com.google.mlkit.common.sdkinternal  R com.tech.ekvayu  ActivityDashboardBinding com.tech.ekvayu.Activities  ActivityMainBinding com.tech.ekvayu.Activities  ActivityYahooAuthBinding com.tech.ekvayu.Activities  Boolean com.tech.ekvayu.Activities  Class com.tech.ekvayu.Activities  
ComponentName com.tech.ekvayu.Activities  DashboardActivity com.tech.ekvayu.Activities  FirebaseAuth com.tech.ekvayu.Activities  HomeFragment com.tech.ekvayu.Activities  Intent com.tech.ekvayu.Activities  Log com.tech.ekvayu.Activities  MainActivity com.tech.ekvayu.Activities  ObjectAnimator com.tech.ekvayu.Activities  OkHttpClient com.tech.ekvayu.Activities  R com.tech.ekvayu.Activities  Request com.tech.ekvayu.Activities  Settings com.tech.ekvayu.Activities  SharedPrefManager com.tech.ekvayu.Activities  String com.tech.ekvayu.Activities  	TextUtils com.tech.ekvayu.Activities  	YahooAuth com.tech.ekvayu.Activities  androidx com.tech.ekvayu.Activities  equals com.tech.ekvayu.Activities  getValue com.tech.ekvayu.Activities  
isNullOrEmpty com.tech.ekvayu.Activities  lazy com.tech.ekvayu.Activities  provideDelegate com.tech.ekvayu.Activities  ActivityDashboardBinding ,com.tech.ekvayu.Activities.DashboardActivity  Bundle ,com.tech.ekvayu.Activities.DashboardActivity  HomeFragment ,com.tech.ekvayu.Activities.DashboardActivity  R ,com.tech.ekvayu.Activities.DashboardActivity  SharedPrefManager ,com.tech.ekvayu.Activities.DashboardActivity  androidx ,com.tech.ekvayu.Activities.DashboardActivity  binding ,com.tech.ekvayu.Activities.DashboardActivity  getGETValue ,com.tech.ekvayu.Activities.DashboardActivity  getGetValue ,com.tech.ekvayu.Activities.DashboardActivity  getLAYOUTInflater ,com.tech.ekvayu.Activities.DashboardActivity  getLAZY ,com.tech.ekvayu.Activities.DashboardActivity  getLayoutInflater ,com.tech.ekvayu.Activities.DashboardActivity  getLazy ,com.tech.ekvayu.Activities.DashboardActivity  getPROVIDEDelegate ,com.tech.ekvayu.Activities.DashboardActivity  getProvideDelegate ,com.tech.ekvayu.Activities.DashboardActivity  getSUPPORTFragmentManager ,com.tech.ekvayu.Activities.DashboardActivity  getSupportFragmentManager ,com.tech.ekvayu.Activities.DashboardActivity  getValue ,com.tech.ekvayu.Activities.DashboardActivity  layoutInflater ,com.tech.ekvayu.Activities.DashboardActivity  lazy ,com.tech.ekvayu.Activities.DashboardActivity  
onBackPressed ,com.tech.ekvayu.Activities.DashboardActivity  provideDelegate ,com.tech.ekvayu.Activities.DashboardActivity  setContentView ,com.tech.ekvayu.Activities.DashboardActivity  setLayoutInflater ,com.tech.ekvayu.Activities.DashboardActivity  setSupportFragmentManager ,com.tech.ekvayu.Activities.DashboardActivity  showFragment ,com.tech.ekvayu.Activities.DashboardActivity  supportFragmentManager ,com.tech.ekvayu.Activities.DashboardActivity  AccessibilityService 'com.tech.ekvayu.Activities.MainActivity  ActivityMainBinding 'com.tech.ekvayu.Activities.MainActivity  Boolean 'com.tech.ekvayu.Activities.MainActivity  Bundle 'com.tech.ekvayu.Activities.MainActivity  Class 'com.tech.ekvayu.Activities.MainActivity  
ComponentName 'com.tech.ekvayu.Activities.MainActivity  Context 'com.tech.ekvayu.Activities.MainActivity  Intent 'com.tech.ekvayu.Activities.MainActivity  ObjectAnimator 'com.tech.ekvayu.Activities.MainActivity  Settings 'com.tech.ekvayu.Activities.MainActivity  	TextUtils 'com.tech.ekvayu.Activities.MainActivity  binding 'com.tech.ekvayu.Activities.MainActivity  equals 'com.tech.ekvayu.Activities.MainActivity  	getEQUALS 'com.tech.ekvayu.Activities.MainActivity  	getEquals 'com.tech.ekvayu.Activities.MainActivity  getISNullOrEmpty 'com.tech.ekvayu.Activities.MainActivity  getIsNullOrEmpty 'com.tech.ekvayu.Activities.MainActivity  getLAYOUTInflater 'com.tech.ekvayu.Activities.MainActivity  getLayoutInflater 'com.tech.ekvayu.Activities.MainActivity  
isNullOrEmpty 'com.tech.ekvayu.Activities.MainActivity  layoutInflater 'com.tech.ekvayu.Activities.MainActivity  setContentView 'com.tech.ekvayu.Activities.MainActivity  setLayoutInflater 'com.tech.ekvayu.Activities.MainActivity  ActivityYahooAuthBinding $com.tech.ekvayu.Activities.YahooAuth  Bundle $com.tech.ekvayu.Activities.YahooAuth  Call $com.tech.ekvayu.Activities.YahooAuth  Callback $com.tech.ekvayu.Activities.YahooAuth  FirebaseAuth $com.tech.ekvayu.Activities.YahooAuth  IOException $com.tech.ekvayu.Activities.YahooAuth  Log $com.tech.ekvayu.Activities.YahooAuth  OkHttpClient $com.tech.ekvayu.Activities.YahooAuth  Request $com.tech.ekvayu.Activities.YahooAuth  Response $com.tech.ekvayu.Activities.YahooAuth  String $com.tech.ekvayu.Activities.YahooAuth  binding $com.tech.ekvayu.Activities.YahooAuth  getLAYOUTInflater $com.tech.ekvayu.Activities.YahooAuth  getLayoutInflater $com.tech.ekvayu.Activities.YahooAuth  invoke $com.tech.ekvayu.Activities.YahooAuth  layoutInflater $com.tech.ekvayu.Activities.YahooAuth  setContentView $com.tech.ekvayu.Activities.YahooAuth  setLayoutInflater $com.tech.ekvayu.Activities.YahooAuth  ActivityGraphResponse com.tech.ekvayu.ActivityGraph  Data com.tech.ekvayu.ActivityGraph  Int com.tech.ekvayu.ActivityGraph  String com.tech.ekvayu.ActivityGraph  Data 3com.tech.ekvayu.ActivityGraph.ActivityGraphResponse  Int 3com.tech.ekvayu.ActivityGraph.ActivityGraphResponse  SerializedName 3com.tech.ekvayu.ActivityGraph.ActivityGraphResponse  String 3com.tech.ekvayu.ActivityGraph.ActivityGraphResponse  data 3com.tech.ekvayu.ActivityGraph.ActivityGraphResponse  Int "com.tech.ekvayu.ActivityGraph.Data  SerializedName "com.tech.ekvayu.ActivityGraph.Data  
totalDisputes "com.tech.ekvayu.ActivityGraph.Data  totalProcessedEmails "com.tech.ekvayu.ActivityGraph.Data  totalSpamEmails "com.tech.ekvayu.ActivityGraph.Data  
ContextCompat com.tech.ekvayu.Adapter  DashboardMenuAdapter com.tech.ekvayu.Adapter  DisputeListAdapter com.tech.ekvayu.Adapter  Int com.tech.ekvayu.Adapter  ItemDashboardMenuBinding com.tech.ekvayu.Adapter  ItemDisputeListBinding com.tech.ekvayu.Adapter  ItemSpamMailBinding com.tech.ekvayu.Adapter  LayoutInflater com.tech.ekvayu.Adapter  List com.tech.ekvayu.Adapter  Log com.tech.ekvayu.Adapter  R com.tech.ekvayu.Adapter  SpamMailAdapter com.tech.ekvayu.Adapter  
ContextCompat ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  DashboardMenuModel ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  Int ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  ItemDashboardMenuBinding ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  LayoutInflater ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  List ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  Log ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  MenuViewHolder ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  R ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  RecyclerView ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  	ViewGroup ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  items ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  listener ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  notifyItemChanged ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  onClickEventListner ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  selectedPosition ,com.tech.ekvayu.Adapter.DashboardMenuAdapter  ItemDashboardMenuBinding ;com.tech.ekvayu.Adapter.DashboardMenuAdapter.MenuViewHolder  binding ;com.tech.ekvayu.Adapter.DashboardMenuAdapter.MenuViewHolder  itemView ;com.tech.ekvayu.Adapter.DashboardMenuAdapter.MenuViewHolder  DashboardMenuModel @com.tech.ekvayu.Adapter.DashboardMenuAdapter.onClickEventListner  onMenuClick @com.tech.ekvayu.Adapter.DashboardMenuAdapter.onClickEventListner  
ContextCompat *com.tech.ekvayu.Adapter.DisputeListAdapter  Int *com.tech.ekvayu.Adapter.DisputeListAdapter  ItemDisputeListBinding *com.tech.ekvayu.Adapter.DisputeListAdapter  LayoutInflater *com.tech.ekvayu.Adapter.DisputeListAdapter  List *com.tech.ekvayu.Adapter.DisputeListAdapter  MenuViewHolder *com.tech.ekvayu.Adapter.DisputeListAdapter  OnDisputeMailListClickListener *com.tech.ekvayu.Adapter.DisputeListAdapter  R *com.tech.ekvayu.Adapter.DisputeListAdapter  RecyclerView *com.tech.ekvayu.Adapter.DisputeListAdapter  Results *com.tech.ekvayu.Adapter.DisputeListAdapter  	ViewGroup *com.tech.ekvayu.Adapter.DisputeListAdapter  items *com.tech.ekvayu.Adapter.DisputeListAdapter  listener *com.tech.ekvayu.Adapter.DisputeListAdapter  ItemDisputeListBinding 9com.tech.ekvayu.Adapter.DisputeListAdapter.MenuViewHolder  binding 9com.tech.ekvayu.Adapter.DisputeListAdapter.MenuViewHolder  itemView 9com.tech.ekvayu.Adapter.DisputeListAdapter.MenuViewHolder  Results Icom.tech.ekvayu.Adapter.DisputeListAdapter.OnDisputeMailListClickListener  onDisputeClicked Icom.tech.ekvayu.Adapter.DisputeListAdapter.OnDisputeMailListClickListener  
ContextCompat 'com.tech.ekvayu.Adapter.SpamMailAdapter  Int 'com.tech.ekvayu.Adapter.SpamMailAdapter  ItemSpamMailBinding 'com.tech.ekvayu.Adapter.SpamMailAdapter  LayoutInflater 'com.tech.ekvayu.Adapter.SpamMailAdapter  List 'com.tech.ekvayu.Adapter.SpamMailAdapter  MenuViewHolder 'com.tech.ekvayu.Adapter.SpamMailAdapter  OnSpamMailClickListener 'com.tech.ekvayu.Adapter.SpamMailAdapter  R 'com.tech.ekvayu.Adapter.SpamMailAdapter  RecyclerView 'com.tech.ekvayu.Adapter.SpamMailAdapter  Results 'com.tech.ekvayu.Adapter.SpamMailAdapter  	ViewGroup 'com.tech.ekvayu.Adapter.SpamMailAdapter  items 'com.tech.ekvayu.Adapter.SpamMailAdapter  listener 'com.tech.ekvayu.Adapter.SpamMailAdapter  ItemSpamMailBinding 6com.tech.ekvayu.Adapter.SpamMailAdapter.MenuViewHolder  binding 6com.tech.ekvayu.Adapter.SpamMailAdapter.MenuViewHolder  itemView 6com.tech.ekvayu.Adapter.SpamMailAdapter.MenuViewHolder  Results ?com.tech.ekvayu.Adapter.SpamMailAdapter.OnSpamMailClickListener  
onSpamClicked ?com.tech.ekvayu.Adapter.SpamMailAdapter.OnSpamMailClickListener  	ApiClient com.tech.ekvayu.ApiConfig  
ApiService com.tech.ekvayu.ApiConfig  
CommonUtil com.tech.ekvayu.ApiConfig  GsonConverterFactory com.tech.ekvayu.ApiConfig  HttpLoggingInterceptor com.tech.ekvayu.ApiConfig  OkHttpClient com.tech.ekvayu.ApiConfig  Retrofit com.tech.ekvayu.ApiConfig  Toast com.tech.ekvayu.ApiConfig  
CommonUtil #com.tech.ekvayu.ApiConfig.ApiClient  Context #com.tech.ekvayu.ApiConfig.ApiClient  GsonConverterFactory #com.tech.ekvayu.ApiConfig.ApiClient  HttpLoggingInterceptor #com.tech.ekvayu.ApiConfig.ApiClient  OkHttpClient #com.tech.ekvayu.ApiConfig.ApiClient  Retrofit #com.tech.ekvayu.ApiConfig.ApiClient  Toast #com.tech.ekvayu.ApiConfig.ApiClient  getRetrofitInstance #com.tech.ekvayu.ApiConfig.ApiClient  BASE_URL -com.tech.ekvayu.ApiConfig.ApiClient.Companion  
CommonUtil -com.tech.ekvayu.ApiConfig.ApiClient.Companion  Context -com.tech.ekvayu.ApiConfig.ApiClient.Companion  GsonConverterFactory -com.tech.ekvayu.ApiConfig.ApiClient.Companion  HttpLoggingInterceptor -com.tech.ekvayu.ApiConfig.ApiClient.Companion  OkHttpClient -com.tech.ekvayu.ApiConfig.ApiClient.Companion  Retrofit -com.tech.ekvayu.ApiConfig.ApiClient.Companion  Toast -com.tech.ekvayu.ApiConfig.ApiClient.Companion  createLoggingInterceptor -com.tech.ekvayu.ApiConfig.ApiClient.Companion  createOkHttpClient -com.tech.ekvayu.ApiConfig.ApiClient.Companion  getRetrofitInstance -com.tech.ekvayu.ApiConfig.ApiClient.Companion  ActivityGraphResponse $com.tech.ekvayu.ApiConfig.ApiService  Body $com.tech.ekvayu.ApiConfig.ApiService  Call $com.tech.ekvayu.ApiConfig.ApiService  
CommonRequest $com.tech.ekvayu.ApiConfig.ApiService  DisputeMailistResponse $com.tech.ekvayu.ApiConfig.ApiService  DisputeRaiseRequest $com.tech.ekvayu.ApiConfig.ApiService  DisputeRaiseResponse $com.tech.ekvayu.ApiConfig.ApiService  
EmailResponse $com.tech.ekvayu.ApiConfig.ApiService  GET $com.tech.ekvayu.ApiConfig.ApiService  HashResponse $com.tech.ekvayu.ApiConfig.ApiService  Headers $com.tech.ekvayu.ApiConfig.ApiService  	Multipart $com.tech.ekvayu.ApiConfig.ApiService  POST $com.tech.ekvayu.ApiConfig.ApiService  Part $com.tech.ekvayu.ApiConfig.ApiService  
PedingMailRes $com.tech.ekvayu.ApiConfig.ApiService  PendingMailRequest $com.tech.ekvayu.ApiConfig.ApiService  RequestBody $com.tech.ekvayu.ApiConfig.ApiService  SpamMailResponse $com.tech.ekvayu.ApiConfig.ApiService  disputeRaise $com.tech.ekvayu.ApiConfig.ApiService  getActivtyGraph $com.tech.ekvayu.ApiConfig.ApiService  getDisputelist $com.tech.ekvayu.ApiConfig.ApiService  
getHashKey $com.tech.ekvayu.ApiConfig.ApiService  getSpamMail $com.tech.ekvayu.ApiConfig.ApiService  pendingMailStatusAi $com.tech.ekvayu.ApiConfig.ApiService  
uploadFile $com.tech.ekvayu.ApiConfig.ApiService  ActivityCompat com.tech.ekvayu.BaseClass  ActivityManager com.tech.ekvayu.BaseClass  AppConstant com.tech.ekvayu.BaseClass  AppInfo com.tech.ekvayu.BaseClass  BatteryInfo com.tech.ekvayu.BaseClass  BatteryManager com.tech.ekvayu.BaseClass  Boolean com.tech.ekvayu.BaseClass  Build com.tech.ekvayu.BaseClass  Class com.tech.ekvayu.BaseClass  
CommonUtil com.tech.ekvayu.BaseClass  
ComponentName com.tech.ekvayu.BaseClass  Context com.tech.ekvayu.BaseClass  
ContextCompat com.tech.ekvayu.BaseClass  Date com.tech.ekvayu.BaseClass  
DeviceInfo com.tech.ekvayu.BaseClass  DeviceInfoHelper com.tech.ekvayu.BaseClass  Dialog com.tech.ekvayu.BaseClass  DisplayMetrics com.tech.ekvayu.BaseClass  	Exception com.tech.ekvayu.BaseClass  Int com.tech.ekvayu.BaseClass  LayoutInflater com.tech.ekvayu.BaseClass  LayoutProgressDialogBinding com.tech.ekvayu.BaseClass  Locale com.tech.ekvayu.BaseClass  Manifest com.tech.ekvayu.BaseClass  
MemoryInfo com.tech.ekvayu.BaseClass  
MyApplication com.tech.ekvayu.BaseClass  NetworkCapabilities com.tech.ekvayu.BaseClass  NetworkInfo com.tech.ekvayu.BaseClass  NetworkInterface com.tech.ekvayu.BaseClass  PackageManager com.tech.ekvayu.BaseClass  R com.tech.ekvayu.BaseClass  Runtime com.tech.ekvayu.BaseClass  
ScreenInfo com.tech.ekvayu.BaseClass  SecurityException com.tech.ekvayu.BaseClass  Settings com.tech.ekvayu.BaseClass  SharedPrefManager com.tech.ekvayu.BaseClass  SimpleDateFormat com.tech.ekvayu.BaseClass  Snackbar com.tech.ekvayu.BaseClass  StorageInfo com.tech.ekvayu.BaseClass  String com.tech.ekvayu.BaseClass  Suppress com.tech.ekvayu.BaseClass  System com.tech.ekvayu.BaseClass  
SystemInfo com.tech.ekvayu.BaseClass  
TelephonyInfo com.tech.ekvayu.BaseClass  TelephonyManager com.tech.ekvayu.BaseClass  	TextUtils com.tech.ekvayu.BaseClass  Window com.tech.ekvayu.BaseClass  apply com.tech.ekvayu.BaseClass  contains com.tech.ekvayu.BaseClass  equals com.tech.ekvayu.BaseClass  
isNullOrEmpty com.tech.ekvayu.BaseClass  iterator com.tech.ekvayu.BaseClass  takeIf com.tech.ekvayu.BaseClass  hashId %com.tech.ekvayu.BaseClass.AppConstant  receiverMail %com.tech.ekvayu.BaseClass.AppConstant  AccessibilityService $com.tech.ekvayu.BaseClass.CommonUtil  Boolean $com.tech.ekvayu.BaseClass.CommonUtil  Build $com.tech.ekvayu.BaseClass.CommonUtil  Class $com.tech.ekvayu.BaseClass.CommonUtil  
ComponentName $com.tech.ekvayu.BaseClass.CommonUtil  ConnectivityManager $com.tech.ekvayu.BaseClass.CommonUtil  Context $com.tech.ekvayu.BaseClass.CommonUtil  
ContextCompat $com.tech.ekvayu.BaseClass.CommonUtil  Dialog $com.tech.ekvayu.BaseClass.CommonUtil  LayoutInflater $com.tech.ekvayu.BaseClass.CommonUtil  LayoutProgressDialogBinding $com.tech.ekvayu.BaseClass.CommonUtil  NetworkCapabilities $com.tech.ekvayu.BaseClass.CommonUtil  R $com.tech.ekvayu.BaseClass.CommonUtil  Settings $com.tech.ekvayu.BaseClass.CommonUtil  Snackbar $com.tech.ekvayu.BaseClass.CommonUtil  String $com.tech.ekvayu.BaseClass.CommonUtil  	TextUtils $com.tech.ekvayu.BaseClass.CommonUtil  View $com.tech.ekvayu.BaseClass.CommonUtil  Window $com.tech.ekvayu.BaseClass.CommonUtil  apply $com.tech.ekvayu.BaseClass.CommonUtil  equals $com.tech.ekvayu.BaseClass.CommonUtil  getAPPLY $com.tech.ekvayu.BaseClass.CommonUtil  getApply $com.tech.ekvayu.BaseClass.CommonUtil  	getEQUALS $com.tech.ekvayu.BaseClass.CommonUtil  	getEquals $com.tech.ekvayu.BaseClass.CommonUtil  getISNullOrEmpty $com.tech.ekvayu.BaseClass.CommonUtil  getIsNullOrEmpty $com.tech.ekvayu.BaseClass.CommonUtil  	getTAKEIf $com.tech.ekvayu.BaseClass.CommonUtil  	getTakeIf $com.tech.ekvayu.BaseClass.CommonUtil  hideProgressDialog $com.tech.ekvayu.BaseClass.CommonUtil  isAccessibilityServiceEnabled $com.tech.ekvayu.BaseClass.CommonUtil  isNetworkAvailable $com.tech.ekvayu.BaseClass.CommonUtil  
isNullOrEmpty $com.tech.ekvayu.BaseClass.CommonUtil  progressDialog $com.tech.ekvayu.BaseClass.CommonUtil  showProgressDialog $com.tech.ekvayu.BaseClass.CommonUtil  showSnackbar $com.tech.ekvayu.BaseClass.CommonUtil  takeIf $com.tech.ekvayu.BaseClass.CommonUtil  ActivityCompat *com.tech.ekvayu.BaseClass.DeviceInfoHelper  ActivityManager *com.tech.ekvayu.BaseClass.DeviceInfoHelper  AppInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  BatteryInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  BatteryManager *com.tech.ekvayu.BaseClass.DeviceInfoHelper  Build *com.tech.ekvayu.BaseClass.DeviceInfoHelper  ConnectivityManager *com.tech.ekvayu.BaseClass.DeviceInfoHelper  Context *com.tech.ekvayu.BaseClass.DeviceInfoHelper  Date *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
DeviceInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  DisplayMetrics *com.tech.ekvayu.BaseClass.DeviceInfoHelper  	Exception *com.tech.ekvayu.BaseClass.DeviceInfoHelper  Int *com.tech.ekvayu.BaseClass.DeviceInfoHelper  Locale *com.tech.ekvayu.BaseClass.DeviceInfoHelper  Manifest *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
MemoryInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  NetworkCapabilities *com.tech.ekvayu.BaseClass.DeviceInfoHelper  NetworkInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  NetworkInterface *com.tech.ekvayu.BaseClass.DeviceInfoHelper  PackageManager *com.tech.ekvayu.BaseClass.DeviceInfoHelper  Runtime *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
ScreenInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  SecurityException *com.tech.ekvayu.BaseClass.DeviceInfoHelper  Settings *com.tech.ekvayu.BaseClass.DeviceInfoHelper  SimpleDateFormat *com.tech.ekvayu.BaseClass.DeviceInfoHelper  StorageInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  String *com.tech.ekvayu.BaseClass.DeviceInfoHelper  Suppress *com.tech.ekvayu.BaseClass.DeviceInfoHelper  SuppressLint *com.tech.ekvayu.BaseClass.DeviceInfoHelper  System *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
SystemInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
TelephonyInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  TelephonyManager *com.tech.ekvayu.BaseClass.DeviceInfoHelper  WifiManager *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
WindowManager *com.tech.ekvayu.BaseClass.DeviceInfoHelper  contains *com.tech.ekvayu.BaseClass.DeviceInfoHelper  context *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getAndroidId *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
getAppInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getBatteryInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getCONTAINS *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getContains *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getCurrentTimestamp *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
getDeviceInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
getDeviceName *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getIPAddress *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getITERATOR *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getIterator *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
getMemoryInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getNetworkInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getNetworkType *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getPhoneType *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
getScreenInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getSerialNumber *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getStorageInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  
getSystemInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  getTelephonyInfo *com.tech.ekvayu.BaseClass.DeviceInfoHelper  iterator *com.tech.ekvayu.BaseClass.DeviceInfoHelper  SharedPrefManager 'com.tech.ekvayu.BaseClass.MyApplication  applicationContext 'com.tech.ekvayu.BaseClass.MyApplication  getAPPLICATIONContext 'com.tech.ekvayu.BaseClass.MyApplication  getApplicationContext 'com.tech.ekvayu.BaseClass.MyApplication  setApplicationContext 'com.tech.ekvayu.BaseClass.MyApplication  getInstance +com.tech.ekvayu.BaseClass.SharedPrefManager  	getString +com.tech.ekvayu.BaseClass.SharedPrefManager  initInstance +com.tech.ekvayu.BaseClass.SharedPrefManager  	putString +com.tech.ekvayu.BaseClass.SharedPrefManager  	ApiClient com.tech.ekvayu.BottomSheet  
ApiService com.tech.ekvayu.BottomSheet  AppConstant com.tech.ekvayu.BottomSheet  Boolean com.tech.ekvayu.BottomSheet  CharSequence com.tech.ekvayu.BottomSheet  Class com.tech.ekvayu.BottomSheet  
CommonUtil com.tech.ekvayu.BottomSheet  
ComponentName com.tech.ekvayu.BottomSheet  
ContextCompat com.tech.ekvayu.BottomSheet  DisputeRaiseRequest com.tech.ekvayu.BottomSheet  	Exception com.tech.ekvayu.BottomSheet  FragmentRaiseBottomSheetBinding com.tech.ekvayu.BottomSheet  FragmentSuggetionBottomBinding com.tech.ekvayu.BottomSheet  !FragmentWarningBottomSheetBinding com.tech.ekvayu.BottomSheet  Html com.tech.ekvayu.BottomSheet  Int com.tech.ekvayu.BottomSheet  Intent com.tech.ekvayu.BottomSheet  Log com.tech.ekvayu.BottomSheet  NewGmailAccessibilityService com.tech.ekvayu.BottomSheet  PendingMailRequest com.tech.ekvayu.BottomSheet  R com.tech.ekvayu.BottomSheet  RaiseBottomSheetFragment com.tech.ekvayu.BottomSheet  Settings com.tech.ekvayu.BottomSheet  SharedPrefManager com.tech.ekvayu.BottomSheet  SuggetionBottomFragment com.tech.ekvayu.BottomSheet  TAG com.tech.ekvayu.BottomSheet  	TextUtils com.tech.ekvayu.BottomSheet  	Throwable com.tech.ekvayu.BottomSheet  Toast com.tech.ekvayu.BottomSheet  WarningBottomSheetFragment com.tech.ekvayu.BottomSheet  binding com.tech.ekvayu.BottomSheet  equals com.tech.ekvayu.BottomSheet  filter com.tech.ekvayu.BottomSheet  getValue com.tech.ekvayu.BottomSheet  isBlank com.tech.ekvayu.BottomSheet  
isNotEmpty com.tech.ekvayu.BottomSheet  
isNullOrBlank com.tech.ekvayu.BottomSheet  
isNullOrEmpty com.tech.ekvayu.BottomSheet  java com.tech.ekvayu.BottomSheet  lazy com.tech.ekvayu.BottomSheet  provideDelegate com.tech.ekvayu.BottomSheet  requireContext com.tech.ekvayu.BottomSheet  setFormData com.tech.ekvayu.BottomSheet  showSnackbar com.tech.ekvayu.BottomSheet  split com.tech.ekvayu.BottomSheet  toRegex com.tech.ekvayu.BottomSheet  toString com.tech.ekvayu.BottomSheet  trim com.tech.ekvayu.BottomSheet  	ApiClient 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
ApiService 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  AppConstant 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  Bundle 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  Call 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  Callback 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  CharSequence 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
CommonUtil 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
ContextCompat 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  DisputeRaiseRequest 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  DisputeRaiseResponse 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  Editable 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  FragmentRaiseBottomSheetBinding 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  Html 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  Int 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  LayoutInflater 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  Log 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
PedingMailRes 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  PendingMailRequest 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  R 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  Response 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  SharedPrefManager 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  TextWatcher 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  	Throwable 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  View 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  	ViewGroup 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  binding 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  filter 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  	getFILTER 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  	getFilter 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getGETValue 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getGetValue 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
getISBlank 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
getISNotEmpty 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getISNullOrBlank 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
getIsBlank 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
getIsNotEmpty 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getIsNullOrBlank 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getLAZY 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getLazy 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getPROVIDEDelegate 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getProvideDelegate 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getSHOWSnackbar 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getSPLIT 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getShowSnackbar 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getSplit 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getTAG 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
getTORegex 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getTOString 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getTRIM 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getTag 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
getToRegex 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getToString 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getTrim 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  getValue 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  isBlank 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
isNotEmpty 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
isNullOrBlank 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  java 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  lazy 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  provideDelegate 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  requireContext 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  setFormData 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  setTag 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  sharedPrefManager 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  show 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  showSnackbar 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  split 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
submitForm 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  tag 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  toRegex 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  toString 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  trim 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
validation 4com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment  
getBINDING Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  
getBinding Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  	getFILTER Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  	getFilter Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  
getISNotEmpty Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  getISNullOrBlank Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  
getIsNotEmpty Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  getIsNullOrBlank Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  getREQUIREContext Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  getRequireContext Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  getSPLIT Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  getSplit Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  
getTORegex Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  getTOString Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  getTRIM Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  
getToRegex Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  getToString Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  getTrim Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  
isNotEmpty Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  
isNullOrBlank Tcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.onCreateView.<no name provided>  
getBINDING Scom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.setFormData.<no name provided>  
getBinding Scom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.setFormData.<no name provided>  getSHOWSnackbar Scom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.setFormData.<no name provided>  getShowSnackbar Scom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.setFormData.<no name provided>  getTOString Scom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.setFormData.<no name provided>  getToString Scom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.setFormData.<no name provided>  
getBINDING Rcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.submitForm.<no name provided>  
getBinding Rcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.submitForm.<no name provided>  getSETFormData Rcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.submitForm.<no name provided>  getSHOWSnackbar Rcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.submitForm.<no name provided>  getSetFormData Rcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.submitForm.<no name provided>  getShowSnackbar Rcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.submitForm.<no name provided>  getTOString Rcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.submitForm.<no name provided>  getToString Rcom.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment.submitForm.<no name provided>  Bundle 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  	Exception 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  FragmentSuggetionBottomBinding 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  Intent 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  LayoutInflater 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  Log 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  TAG 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  Toast 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  View 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  	ViewGroup 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  binding 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  getTAG 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  getTag 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  openGmailApp 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  requireActivity 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  requireContext 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  
setCancelable 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  setTag 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  show 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  
startActivity 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  tag 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  AccessibilityService 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  Boolean 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  Bundle 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  Class 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  
ComponentName 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  Context 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  !FragmentWarningBottomSheetBinding 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  Intent 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  LayoutInflater 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  NewGmailAccessibilityService 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  Settings 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  	TextUtils 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  View 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  	ViewGroup 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  binding 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  equals 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  	getEQUALS 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  	getEquals 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getISNullOrEmpty 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getIsNullOrEmpty 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getTAG 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getTag 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  isAccessibilityServiceEnabled 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  
isNullOrEmpty 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  java 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  openAccessibilitySettings 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  requireActivity 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  
setCancelable 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  setTag 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  show 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  tag 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  	ArrayList com.tech.ekvayu.Dispute  Comments com.tech.ekvayu.Dispute  DisputeCounterRequest com.tech.ekvayu.Dispute  DisputeCounterResponse com.tech.ekvayu.Dispute  DisputeMailistResponse com.tech.ekvayu.Dispute  DisputeRaiseRequest com.tech.ekvayu.Dispute  DisputeRaiseResponse com.tech.ekvayu.Dispute  Int com.tech.ekvayu.Dispute  Results com.tech.ekvayu.Dispute  String com.tech.ekvayu.Dispute  arrayListOf com.tech.ekvayu.Dispute  SerializedName  com.tech.ekvayu.Dispute.Comments  String  com.tech.ekvayu.Dispute.Comments  SerializedName -com.tech.ekvayu.Dispute.DisputeCounterRequest  String -com.tech.ekvayu.Dispute.DisputeCounterRequest  Int .com.tech.ekvayu.Dispute.DisputeCounterResponse  SerializedName .com.tech.ekvayu.Dispute.DisputeCounterResponse  String .com.tech.ekvayu.Dispute.DisputeCounterResponse  	ArrayList .com.tech.ekvayu.Dispute.DisputeMailistResponse  Int .com.tech.ekvayu.Dispute.DisputeMailistResponse  Results .com.tech.ekvayu.Dispute.DisputeMailistResponse  SerializedName .com.tech.ekvayu.Dispute.DisputeMailistResponse  String .com.tech.ekvayu.Dispute.DisputeMailistResponse  arrayListOf .com.tech.ekvayu.Dispute.DisputeMailistResponse  results .com.tech.ekvayu.Dispute.DisputeMailistResponse  SerializedName +com.tech.ekvayu.Dispute.DisputeRaiseRequest  String +com.tech.ekvayu.Dispute.DisputeRaiseRequest  Int ,com.tech.ekvayu.Dispute.DisputeRaiseResponse  SerializedName ,com.tech.ekvayu.Dispute.DisputeRaiseResponse  String ,com.tech.ekvayu.Dispute.DisputeRaiseResponse  code ,com.tech.ekvayu.Dispute.DisputeRaiseResponse  
disputeInfoId ,com.tech.ekvayu.Dispute.DisputeRaiseResponse  message ,com.tech.ekvayu.Dispute.DisputeRaiseResponse  	ArrayList com.tech.ekvayu.Dispute.Results  Comments com.tech.ekvayu.Dispute.Results  Int com.tech.ekvayu.Dispute.Results  SerializedName com.tech.ekvayu.Dispute.Results  String com.tech.ekvayu.Dispute.Results  arrayListOf com.tech.ekvayu.Dispute.Results  overallAiStatus com.tech.ekvayu.Dispute.Results  recieversEmail com.tech.ekvayu.Dispute.Results  sendersEmail com.tech.ekvayu.Dispute.Results  status com.tech.ekvayu.Dispute.Results  subject com.tech.ekvayu.Dispute.Results  AccessibilityEvent com.tech.ekvayu.EkService  	ApiClient com.tech.ekvayu.EkService  
ApiService com.tech.ekvayu.EkService  AppConstant com.tech.ekvayu.EkService  Boolean com.tech.ekvayu.EkService  BufferedReader com.tech.ekvayu.EkService  Build com.tech.ekvayu.EkService  	Exception com.tech.ekvayu.EkService  File com.tech.ekvayu.EkService  FileOutputStream com.tech.ekvayu.EkService  
FileReader com.tech.ekvayu.EkService  GmailAccessibilityService com.tech.ekvayu.EkService  Handler com.tech.ekvayu.EkService  Intent com.tech.ekvayu.EkService  LAYOUT_INFLATER_SERVICE com.tech.ekvayu.EkService  LayoutProgressBinding com.tech.ekvayu.EkService  LayoutValidationBinding com.tech.ekvayu.EkService  List com.tech.ekvayu.EkService  Log com.tech.ekvayu.EkService  Looper com.tech.ekvayu.EkService  Map com.tech.ekvayu.EkService  
MultipartBody com.tech.ekvayu.EkService  MutableList com.tech.ekvayu.EkService  NewGmailAccessibilityService com.tech.ekvayu.EkService  OkHttpClient com.tech.ekvayu.EkService  OutlookAccessibilityService com.tech.ekvayu.EkService  PendingMailRequest com.tech.ekvayu.EkService  PixelFormat com.tech.ekvayu.EkService  R com.tech.ekvayu.EkService  Regex com.tech.ekvayu.EkService  Request com.tech.ekvayu.EkService  RequestBody com.tech.ekvayu.EkService  Settings com.tech.ekvayu.EkService  SharedPrefManager com.tech.ekvayu.EkService  String com.tech.ekvayu.EkService  
StringBuilder com.tech.ekvayu.EkService  	Throwable com.tech.ekvayu.EkService  Toast com.tech.ekvayu.EkService  Uri com.tech.ekvayu.EkService  View com.tech.ekvayu.EkService  WINDOW_SERVICE com.tech.ekvayu.EkService  
WindowManager com.tech.ekvayu.EkService  YahooAccessibilityService com.tech.ekvayu.EkService  also com.tech.ekvayu.EkService  android com.tech.ekvayu.EkService  
appendLine com.tech.ekvayu.EkService  applicationContext com.tech.ekvayu.EkService  checkAiResponse com.tech.ekvayu.EkService  contains com.tech.ekvayu.EkService  endsWith com.tech.ekvayu.EkService  
extractEmails com.tech.ekvayu.EkService  first com.tech.ekvayu.EkService  firstOrNull com.tech.ekvayu.EkService  	getOrNull com.tech.ekvayu.EkService  getValue com.tech.ekvayu.EkService  invoke com.tech.ekvayu.EkService  
isNotEmpty com.tech.ekvayu.EkService  
isNullOrEmpty com.tech.ekvayu.EkService  java com.tech.ekvayu.EkService  joinToString com.tech.ekvayu.EkService  lazy com.tech.ekvayu.EkService  let com.tech.ekvayu.EkService  	lowercase com.tech.ekvayu.EkService  map com.tech.ekvayu.EkService  matches com.tech.ekvayu.EkService  
mutableListOf com.tech.ekvayu.EkService  mutableMapOf com.tech.ekvayu.EkService  mutableSetOf com.tech.ekvayu.EkService  provideDelegate com.tech.ekvayu.EkService  removePrefix com.tech.ekvayu.EkService  set com.tech.ekvayu.EkService  setResponsePopup com.tech.ekvayu.EkService  sharedPrefManager com.tech.ekvayu.EkService  split com.tech.ekvayu.EkService  
startsWith com.tech.ekvayu.EkService  toByteArray com.tech.ekvayu.EkService  toList com.tech.ekvayu.EkService  toMediaType com.tech.ekvayu.EkService  toMediaTypeOrNull com.tech.ekvayu.EkService  toRegex com.tech.ekvayu.EkService  
toRequestBody com.tech.ekvayu.EkService  toString com.tech.ekvayu.EkService  trim com.tech.ekvayu.EkService  
trimIndent com.tech.ekvayu.EkService  until com.tech.ekvayu.EkService  
uploadFile com.tech.ekvayu.EkService  use com.tech.ekvayu.EkService  AccessibilityEvent 3com.tech.ekvayu.EkService.GmailAccessibilityService  AccessibilityNodeInfo 3com.tech.ekvayu.EkService.GmailAccessibilityService  	ApiClient 3com.tech.ekvayu.EkService.GmailAccessibilityService  
ApiService 3com.tech.ekvayu.EkService.GmailAccessibilityService  AppConstant 3com.tech.ekvayu.EkService.GmailAccessibilityService  Boolean 3com.tech.ekvayu.EkService.GmailAccessibilityService  BufferedReader 3com.tech.ekvayu.EkService.GmailAccessibilityService  Build 3com.tech.ekvayu.EkService.GmailAccessibilityService  Call 3com.tech.ekvayu.EkService.GmailAccessibilityService  Callback 3com.tech.ekvayu.EkService.GmailAccessibilityService  Context 3com.tech.ekvayu.EkService.GmailAccessibilityService  
EmailResponse 3com.tech.ekvayu.EkService.GmailAccessibilityService  	Exception 3com.tech.ekvayu.EkService.GmailAccessibilityService  File 3com.tech.ekvayu.EkService.GmailAccessibilityService  FileOutputStream 3com.tech.ekvayu.EkService.GmailAccessibilityService  
FileReader 3com.tech.ekvayu.EkService.GmailAccessibilityService  Handler 3com.tech.ekvayu.EkService.GmailAccessibilityService  HashResponse 3com.tech.ekvayu.EkService.GmailAccessibilityService  IOException 3com.tech.ekvayu.EkService.GmailAccessibilityService  Intent 3com.tech.ekvayu.EkService.GmailAccessibilityService  LAYOUT_INFLATER_SERVICE 3com.tech.ekvayu.EkService.GmailAccessibilityService  LayoutInflater 3com.tech.ekvayu.EkService.GmailAccessibilityService  LayoutProgressBinding 3com.tech.ekvayu.EkService.GmailAccessibilityService  LayoutValidationBinding 3com.tech.ekvayu.EkService.GmailAccessibilityService  List 3com.tech.ekvayu.EkService.GmailAccessibilityService  Log 3com.tech.ekvayu.EkService.GmailAccessibilityService  Looper 3com.tech.ekvayu.EkService.GmailAccessibilityService  
MultipartBody 3com.tech.ekvayu.EkService.GmailAccessibilityService  MutableList 3com.tech.ekvayu.EkService.GmailAccessibilityService  
PedingMailRes 3com.tech.ekvayu.EkService.GmailAccessibilityService  PendingMailRequest 3com.tech.ekvayu.EkService.GmailAccessibilityService  PixelFormat 3com.tech.ekvayu.EkService.GmailAccessibilityService  R 3com.tech.ekvayu.EkService.GmailAccessibilityService  Regex 3com.tech.ekvayu.EkService.GmailAccessibilityService  RequestBody 3com.tech.ekvayu.EkService.GmailAccessibilityService  Response 3com.tech.ekvayu.EkService.GmailAccessibilityService  Settings 3com.tech.ekvayu.EkService.GmailAccessibilityService  SharedPrefManager 3com.tech.ekvayu.EkService.GmailAccessibilityService  String 3com.tech.ekvayu.EkService.GmailAccessibilityService  
StringBuilder 3com.tech.ekvayu.EkService.GmailAccessibilityService  	Throwable 3com.tech.ekvayu.EkService.GmailAccessibilityService  Toast 3com.tech.ekvayu.EkService.GmailAccessibilityService  Uri 3com.tech.ekvayu.EkService.GmailAccessibilityService  View 3com.tech.ekvayu.EkService.GmailAccessibilityService  WINDOW_SERVICE 3com.tech.ekvayu.EkService.GmailAccessibilityService  
WindowManager 3com.tech.ekvayu.EkService.GmailAccessibilityService  also 3com.tech.ekvayu.EkService.GmailAccessibilityService  
appendLine 3com.tech.ekvayu.EkService.GmailAccessibilityService  applicationContext 3com.tech.ekvayu.EkService.GmailAccessibilityService  checkAiResponse 3com.tech.ekvayu.EkService.GmailAccessibilityService  contains 3com.tech.ekvayu.EkService.GmailAccessibilityService  contentResolver 3com.tech.ekvayu.EkService.GmailAccessibilityService  currentPopupView 3com.tech.ekvayu.EkService.GmailAccessibilityService  endsWith 3com.tech.ekvayu.EkService.GmailAccessibilityService  extractEmail 3com.tech.ekvayu.EkService.GmailAccessibilityService  extractEmailDetails 3com.tech.ekvayu.EkService.GmailAccessibilityService  
extractEmails 3com.tech.ekvayu.EkService.GmailAccessibilityService  findEmailNodes 3com.tech.ekvayu.EkService.GmailAccessibilityService  firstOrNull 3com.tech.ekvayu.EkService.GmailAccessibilityService  getALSO 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getAPPENDLine 3com.tech.ekvayu.EkService.GmailAccessibilityService  getAPPLICATIONContext 3com.tech.ekvayu.EkService.GmailAccessibilityService  getAlso 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getAppendLine 3com.tech.ekvayu.EkService.GmailAccessibilityService  getApplicationContext 3com.tech.ekvayu.EkService.GmailAccessibilityService  getCONTAINS 3com.tech.ekvayu.EkService.GmailAccessibilityService  getCONTENTResolver 3com.tech.ekvayu.EkService.GmailAccessibilityService  getContains 3com.tech.ekvayu.EkService.GmailAccessibilityService  getContentResolver 3com.tech.ekvayu.EkService.GmailAccessibilityService  getENDSWith 3com.tech.ekvayu.EkService.GmailAccessibilityService  getEndsWith 3com.tech.ekvayu.EkService.GmailAccessibilityService  getFIRSTOrNull 3com.tech.ekvayu.EkService.GmailAccessibilityService  getFirstOrNull 3com.tech.ekvayu.EkService.GmailAccessibilityService  getGETValue 3com.tech.ekvayu.EkService.GmailAccessibilityService  getGetValue 3com.tech.ekvayu.EkService.GmailAccessibilityService  	getHashId 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getISNotEmpty 3com.tech.ekvayu.EkService.GmailAccessibilityService  getISNullOrEmpty 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getIsNotEmpty 3com.tech.ekvayu.EkService.GmailAccessibilityService  getIsNullOrEmpty 3com.tech.ekvayu.EkService.GmailAccessibilityService  getJOINToString 3com.tech.ekvayu.EkService.GmailAccessibilityService  getJoinToString 3com.tech.ekvayu.EkService.GmailAccessibilityService  getLAZY 3com.tech.ekvayu.EkService.GmailAccessibilityService  getLET 3com.tech.ekvayu.EkService.GmailAccessibilityService  getLOWERCASE 3com.tech.ekvayu.EkService.GmailAccessibilityService  getLazy 3com.tech.ekvayu.EkService.GmailAccessibilityService  getLet 3com.tech.ekvayu.EkService.GmailAccessibilityService  getLowercase 3com.tech.ekvayu.EkService.GmailAccessibilityService  getMAP 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getMATCHES 3com.tech.ekvayu.EkService.GmailAccessibilityService  getMUTABLEListOf 3com.tech.ekvayu.EkService.GmailAccessibilityService  getMap 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getMatches 3com.tech.ekvayu.EkService.GmailAccessibilityService  getMutableListOf 3com.tech.ekvayu.EkService.GmailAccessibilityService  getPROVIDEDelegate 3com.tech.ekvayu.EkService.GmailAccessibilityService  getProvideDelegate 3com.tech.ekvayu.EkService.GmailAccessibilityService  getREMOVEPrefix 3com.tech.ekvayu.EkService.GmailAccessibilityService  getROOTInActiveWindow 3com.tech.ekvayu.EkService.GmailAccessibilityService  getRemovePrefix 3com.tech.ekvayu.EkService.GmailAccessibilityService  getRootInActiveWindow 3com.tech.ekvayu.EkService.GmailAccessibilityService  getSPLIT 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getSTARTSWith 3com.tech.ekvayu.EkService.GmailAccessibilityService  getSplit 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getStartsWith 3com.tech.ekvayu.EkService.GmailAccessibilityService  getSystemService 3com.tech.ekvayu.EkService.GmailAccessibilityService  getTOByteArray 3com.tech.ekvayu.EkService.GmailAccessibilityService  	getTOList 3com.tech.ekvayu.EkService.GmailAccessibilityService  getTOMediaTypeOrNull 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getTORegex 3com.tech.ekvayu.EkService.GmailAccessibilityService  getTOString 3com.tech.ekvayu.EkService.GmailAccessibilityService  getTRIM 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getTRIMIndent 3com.tech.ekvayu.EkService.GmailAccessibilityService  getToByteArray 3com.tech.ekvayu.EkService.GmailAccessibilityService  	getToList 3com.tech.ekvayu.EkService.GmailAccessibilityService  getToMediaTypeOrNull 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getToRegex 3com.tech.ekvayu.EkService.GmailAccessibilityService  getToString 3com.tech.ekvayu.EkService.GmailAccessibilityService  getTrim 3com.tech.ekvayu.EkService.GmailAccessibilityService  
getTrimIndent 3com.tech.ekvayu.EkService.GmailAccessibilityService  getUNTIL 3com.tech.ekvayu.EkService.GmailAccessibilityService  getUntil 3com.tech.ekvayu.EkService.GmailAccessibilityService  getValue 3com.tech.ekvayu.EkService.GmailAccessibilityService  hasOverlayPermission 3com.tech.ekvayu.EkService.GmailAccessibilityService  invoke 3com.tech.ekvayu.EkService.GmailAccessibilityService  
isNotEmpty 3com.tech.ekvayu.EkService.GmailAccessibilityService  
isNullOrEmpty 3com.tech.ekvayu.EkService.GmailAccessibilityService  isValidEmail 3com.tech.ekvayu.EkService.GmailAccessibilityService  java 3com.tech.ekvayu.EkService.GmailAccessibilityService  joinToString 3com.tech.ekvayu.EkService.GmailAccessibilityService  lazy 3com.tech.ekvayu.EkService.GmailAccessibilityService  let 3com.tech.ekvayu.EkService.GmailAccessibilityService  	lowercase 3com.tech.ekvayu.EkService.GmailAccessibilityService  map 3com.tech.ekvayu.EkService.GmailAccessibilityService  matches 3com.tech.ekvayu.EkService.GmailAccessibilityService  
mutableListOf 3com.tech.ekvayu.EkService.GmailAccessibilityService  provideDelegate 3com.tech.ekvayu.EkService.GmailAccessibilityService  readEmlFile 3com.tech.ekvayu.EkService.GmailAccessibilityService  removePrefix 3com.tech.ekvayu.EkService.GmailAccessibilityService  requestOverlayPermission 3com.tech.ekvayu.EkService.GmailAccessibilityService  rootInActiveWindow 3com.tech.ekvayu.EkService.GmailAccessibilityService  setApplicationContext 3com.tech.ekvayu.EkService.GmailAccessibilityService  setContentResolver 3com.tech.ekvayu.EkService.GmailAccessibilityService  setResponsePopup 3com.tech.ekvayu.EkService.GmailAccessibilityService  setRootInActiveWindow 3com.tech.ekvayu.EkService.GmailAccessibilityService  sharedPrefManager 3com.tech.ekvayu.EkService.GmailAccessibilityService  split 3com.tech.ekvayu.EkService.GmailAccessibilityService  
startsWith 3com.tech.ekvayu.EkService.GmailAccessibilityService  toByteArray 3com.tech.ekvayu.EkService.GmailAccessibilityService  toList 3com.tech.ekvayu.EkService.GmailAccessibilityService  toMediaTypeOrNull 3com.tech.ekvayu.EkService.GmailAccessibilityService  toRegex 3com.tech.ekvayu.EkService.GmailAccessibilityService  toString 3com.tech.ekvayu.EkService.GmailAccessibilityService  trim 3com.tech.ekvayu.EkService.GmailAccessibilityService  
trimIndent 3com.tech.ekvayu.EkService.GmailAccessibilityService  until 3com.tech.ekvayu.EkService.GmailAccessibilityService  
uploadFile 3com.tech.ekvayu.EkService.GmailAccessibilityService  getAPPLICATIONContext acom.tech.ekvayu.EkService.GmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getApplicationContext acom.tech.ekvayu.EkService.GmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getLOWERCASE acom.tech.ekvayu.EkService.GmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getLowercase acom.tech.ekvayu.EkService.GmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getSETResponsePopup acom.tech.ekvayu.EkService.GmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getSetResponsePopup acom.tech.ekvayu.EkService.GmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getTRIM acom.tech.ekvayu.EkService.GmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getTrim acom.tech.ekvayu.EkService.GmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getCHECKAiResponse Pcom.tech.ekvayu.EkService.GmailAccessibilityService.getHashId.<no name provided>  getCheckAiResponse Pcom.tech.ekvayu.EkService.GmailAccessibilityService.getHashId.<no name provided>  getSHAREDPrefManager Pcom.tech.ekvayu.EkService.GmailAccessibilityService.getHashId.<no name provided>  getSharedPrefManager Pcom.tech.ekvayu.EkService.GmailAccessibilityService.getHashId.<no name provided>  getTOString Pcom.tech.ekvayu.EkService.GmailAccessibilityService.getHashId.<no name provided>  getToString Pcom.tech.ekvayu.EkService.GmailAccessibilityService.getHashId.<no name provided>  
getUPLOADFile Pcom.tech.ekvayu.EkService.GmailAccessibilityService.getHashId.<no name provided>  
getUploadFile Pcom.tech.ekvayu.EkService.GmailAccessibilityService.getHashId.<no name provided>  getAPPLICATIONContext Qcom.tech.ekvayu.EkService.GmailAccessibilityService.uploadFile.<no name provided>  getApplicationContext Qcom.tech.ekvayu.EkService.GmailAccessibilityService.uploadFile.<no name provided>  getCHECKAiResponse Qcom.tech.ekvayu.EkService.GmailAccessibilityService.uploadFile.<no name provided>  getCheckAiResponse Qcom.tech.ekvayu.EkService.GmailAccessibilityService.uploadFile.<no name provided>  getSHAREDPrefManager Qcom.tech.ekvayu.EkService.GmailAccessibilityService.uploadFile.<no name provided>  getSharedPrefManager Qcom.tech.ekvayu.EkService.GmailAccessibilityService.uploadFile.<no name provided>  getTOString Qcom.tech.ekvayu.EkService.GmailAccessibilityService.uploadFile.<no name provided>  getToString Qcom.tech.ekvayu.EkService.GmailAccessibilityService.uploadFile.<no name provided>  AccessibilityEvent 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  AccessibilityNodeInfo 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  	ApiClient 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
ApiService 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  AppConstant 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Boolean 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  BufferedReader 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Build 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Call 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Callback 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Context 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
EmailResponse 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  	Exception 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  File 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  FileOutputStream 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
FileReader 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Handler 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  HashResponse 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  IOException 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Intent 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  LAYOUT_INFLATER_SERVICE 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  LayoutInflater 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  LayoutProgressBinding 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  LayoutValidationBinding 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  List 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Log 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Looper 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
MultipartBody 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  MutableList 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
PedingMailRes 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  PendingMailRequest 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  PixelFormat 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  R 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Regex 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  RequestBody 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Response 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Settings 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  SharedPrefManager 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  String 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
StringBuilder 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  	Throwable 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Toast 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  Uri 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  View 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  WINDOW_SERVICE 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
WindowManager 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  also 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  android 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
appendLine 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  applicationContext 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  checkAiResponse 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  contains 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  contentResolver 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  currentPopupView 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  extractEmail 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  extractEmailDetails 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
extractEmails 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  findEmailNodes 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  first 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  firstOrNull 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getALSO 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getANDROID 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getAPPENDLine 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getAPPLICATIONContext 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getAlso 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getAndroid 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getAppendLine 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getApplicationContext 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getCONTAINS 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getCONTENTResolver 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getContains 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getContentResolver 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getFIRST 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getFIRSTOrNull 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getFirst 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getFirstOrNull 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getGETValue 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getGetValue 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  	getHashId 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getISNotEmpty 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getIsNotEmpty 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getJOINToString 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getJoinToString 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getLAZY 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getLET 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getLOWERCASE 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getLazy 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getLet 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getLowercase 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getMAP 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getMATCHES 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getMUTABLEListOf 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getMUTABLESetOf 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getMap 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getMatches 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getMutableListOf 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getMutableSetOf 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getPROVIDEDelegate 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getProvideDelegate 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getREMOVEPrefix 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getROOTInActiveWindow 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getRemovePrefix 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getRootInActiveWindow 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getSPLIT 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getSTARTSWith 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getSplit 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getStartsWith 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getSystemService 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getTOByteArray 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  	getTOList 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getTOMediaTypeOrNull 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getTOString 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getTRIM 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getTRIMIndent 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getToByteArray 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  	getToList 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getToMediaTypeOrNull 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getToString 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getTrim 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
getTrimIndent 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getUNTIL 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getUSE 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getUntil 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getUse 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getValue 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  hasOverlayPermission 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  invoke 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
isNotEmpty 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  isValidEmail 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  java 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  joinToString 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  lazy 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  let 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  	lowercase 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  map 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  matches 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
mutableListOf 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  mutableSetOf 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  provideDelegate 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  readEmlFile 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  removePrefix 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  requestOverlayPermission 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  rootInActiveWindow 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  setApplicationContext 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  setContentResolver 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  setResponsePopup 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  setRootInActiveWindow 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  sharedPrefManager 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  split 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
startsWith 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  toByteArray 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  toList 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  toMediaTypeOrNull 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  toString 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  trim 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
trimIndent 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  until 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  
uploadFile 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  use 6com.tech.ekvayu.EkService.NewGmailAccessibilityService  getAPPLICATIONContext dcom.tech.ekvayu.EkService.NewGmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getApplicationContext dcom.tech.ekvayu.EkService.NewGmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getLOWERCASE dcom.tech.ekvayu.EkService.NewGmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getLowercase dcom.tech.ekvayu.EkService.NewGmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getSETResponsePopup dcom.tech.ekvayu.EkService.NewGmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getSetResponsePopup dcom.tech.ekvayu.EkService.NewGmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getTRIM dcom.tech.ekvayu.EkService.NewGmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getTrim dcom.tech.ekvayu.EkService.NewGmailAccessibilityService.checkAiResponse.pollStatus.<no name provided>  getCHECKAiResponse Scom.tech.ekvayu.EkService.NewGmailAccessibilityService.getHashId.<no name provided>  getCheckAiResponse Scom.tech.ekvayu.EkService.NewGmailAccessibilityService.getHashId.<no name provided>  getSHAREDPrefManager Scom.tech.ekvayu.EkService.NewGmailAccessibilityService.getHashId.<no name provided>  getSharedPrefManager Scom.tech.ekvayu.EkService.NewGmailAccessibilityService.getHashId.<no name provided>  getTOString Scom.tech.ekvayu.EkService.NewGmailAccessibilityService.getHashId.<no name provided>  getToString Scom.tech.ekvayu.EkService.NewGmailAccessibilityService.getHashId.<no name provided>  
getUPLOADFile Scom.tech.ekvayu.EkService.NewGmailAccessibilityService.getHashId.<no name provided>  
getUploadFile Scom.tech.ekvayu.EkService.NewGmailAccessibilityService.getHashId.<no name provided>  getAPPLICATIONContext Tcom.tech.ekvayu.EkService.NewGmailAccessibilityService.uploadFile.<no name provided>  getApplicationContext Tcom.tech.ekvayu.EkService.NewGmailAccessibilityService.uploadFile.<no name provided>  getCHECKAiResponse Tcom.tech.ekvayu.EkService.NewGmailAccessibilityService.uploadFile.<no name provided>  getCheckAiResponse Tcom.tech.ekvayu.EkService.NewGmailAccessibilityService.uploadFile.<no name provided>  getSHAREDPrefManager Tcom.tech.ekvayu.EkService.NewGmailAccessibilityService.uploadFile.<no name provided>  getSharedPrefManager Tcom.tech.ekvayu.EkService.NewGmailAccessibilityService.uploadFile.<no name provided>  getTOString Tcom.tech.ekvayu.EkService.NewGmailAccessibilityService.uploadFile.<no name provided>  getToString Tcom.tech.ekvayu.EkService.NewGmailAccessibilityService.uploadFile.<no name provided>  AccessibilityEvent 5com.tech.ekvayu.EkService.OutlookAccessibilityService  Map 5com.tech.ekvayu.EkService.OutlookAccessibilityService  Regex 5com.tech.ekvayu.EkService.OutlookAccessibilityService  String 5com.tech.ekvayu.EkService.OutlookAccessibilityService  body 5com.tech.ekvayu.EkService.OutlookAccessibilityService  date 5com.tech.ekvayu.EkService.OutlookAccessibilityService  	fromEmail 5com.tech.ekvayu.EkService.OutlookAccessibilityService  
getISNotEmpty 5com.tech.ekvayu.EkService.OutlookAccessibilityService  
getIsNotEmpty 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getLET 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getLet 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getMAP 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getMUTABLEMapOf 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getMap 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getMutableMapOf 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getSET 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getSPLIT 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getSet 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getSplit 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getTRIM 5com.tech.ekvayu.EkService.OutlookAccessibilityService  getTrim 5com.tech.ekvayu.EkService.OutlookAccessibilityService  invoke 5com.tech.ekvayu.EkService.OutlookAccessibilityService  
isNotEmpty 5com.tech.ekvayu.EkService.OutlookAccessibilityService  let 5com.tech.ekvayu.EkService.OutlookAccessibilityService  map 5com.tech.ekvayu.EkService.OutlookAccessibilityService  mutableMapOf 5com.tech.ekvayu.EkService.OutlookAccessibilityService  set 5com.tech.ekvayu.EkService.OutlookAccessibilityService  split 5com.tech.ekvayu.EkService.OutlookAccessibilityService  
splitMailData 5com.tech.ekvayu.EkService.OutlookAccessibilityService  splitOutlookEmailData 5com.tech.ekvayu.EkService.OutlookAccessibilityService  trim 5com.tech.ekvayu.EkService.OutlookAccessibilityService  userName 5com.tech.ekvayu.EkService.OutlookAccessibilityService  AccessibilityEvent 3com.tech.ekvayu.EkService.YahooAccessibilityService  Call 3com.tech.ekvayu.EkService.YahooAccessibilityService  Callback 3com.tech.ekvayu.EkService.YahooAccessibilityService  IOException 3com.tech.ekvayu.EkService.YahooAccessibilityService  Log 3com.tech.ekvayu.EkService.YahooAccessibilityService  OkHttpClient 3com.tech.ekvayu.EkService.YahooAccessibilityService  Request 3com.tech.ekvayu.EkService.YahooAccessibilityService  Response 3com.tech.ekvayu.EkService.YahooAccessibilityService  String 3com.tech.ekvayu.EkService.YahooAccessibilityService  getGETOrNull 3com.tech.ekvayu.EkService.YahooAccessibilityService  getGetOrNull 3com.tech.ekvayu.EkService.YahooAccessibilityService  getMAP 3com.tech.ekvayu.EkService.YahooAccessibilityService  getMap 3com.tech.ekvayu.EkService.YahooAccessibilityService  	getOrNull 3com.tech.ekvayu.EkService.YahooAccessibilityService  getSPLIT 3com.tech.ekvayu.EkService.YahooAccessibilityService  getSplit 3com.tech.ekvayu.EkService.YahooAccessibilityService  getTOMediaType 3com.tech.ekvayu.EkService.YahooAccessibilityService  getTORequestBody 3com.tech.ekvayu.EkService.YahooAccessibilityService  getTRIM 3com.tech.ekvayu.EkService.YahooAccessibilityService  
getTRIMIndent 3com.tech.ekvayu.EkService.YahooAccessibilityService  getToMediaType 3com.tech.ekvayu.EkService.YahooAccessibilityService  getToRequestBody 3com.tech.ekvayu.EkService.YahooAccessibilityService  getTrim 3com.tech.ekvayu.EkService.YahooAccessibilityService  
getTrimIndent 3com.tech.ekvayu.EkService.YahooAccessibilityService  invoke 3com.tech.ekvayu.EkService.YahooAccessibilityService  map 3com.tech.ekvayu.EkService.YahooAccessibilityService  split 3com.tech.ekvayu.EkService.YahooAccessibilityService  toMediaType 3com.tech.ekvayu.EkService.YahooAccessibilityService  
toRequestBody 3com.tech.ekvayu.EkService.YahooAccessibilityService  trim 3com.tech.ekvayu.EkService.YahooAccessibilityService  
trimIndent 3com.tech.ekvayu.EkService.YahooAccessibilityService  ActivityCompat com.tech.ekvayu.Fragments  ActivityGraphFragment com.tech.ekvayu.Fragments  AlertDialog com.tech.ekvayu.Fragments  	ApiClient com.tech.ekvayu.Fragments  
ApiService com.tech.ekvayu.Fragments  AppConstant com.tech.ekvayu.Fragments  Array com.tech.ekvayu.Fragments  Color com.tech.ekvayu.Fragments  
ColorTemplate com.tech.ekvayu.Fragments  
CommonRequest com.tech.ekvayu.Fragments  
CommonUtil com.tech.ekvayu.Fragments  
ContextCompat com.tech.ekvayu.Fragments  DashboardMenuAdapter com.tech.ekvayu.Fragments  DashboardMenuModel com.tech.ekvayu.Fragments  DeviceDetailsFragment com.tech.ekvayu.Fragments  DeviceInfoHelper com.tech.ekvayu.Fragments  Dispatchers com.tech.ekvayu.Fragments  DisputeListAdapter com.tech.ekvayu.Fragments  DisputeMaillistFragment com.tech.ekvayu.Fragments  	Exception com.tech.ekvayu.Fragments  FragmentActivityGraphBinding com.tech.ekvayu.Fragments  FragmentDeviceDetailsBinding com.tech.ekvayu.Fragments  FragmentDisputeMaillistBinding com.tech.ekvayu.Fragments  FragmentHomeBinding com.tech.ekvayu.Fragments  FragmentSpamMailBinding com.tech.ekvayu.Fragments  GridLayoutManager com.tech.ekvayu.Fragments  HomeFragment com.tech.ekvayu.Fragments  Int com.tech.ekvayu.Fragments  IntArray com.tech.ekvayu.Fragments  LayoutInflater com.tech.ekvayu.Fragments  LayoutSpamDetailBinding com.tech.ekvayu.Fragments  Log com.tech.ekvayu.Fragments  Long com.tech.ekvayu.Fragments  Manifest com.tech.ekvayu.Fragments  NewGmailAccessibilityService com.tech.ekvayu.Fragments  PackageManager com.tech.ekvayu.Fragments  PieData com.tech.ekvayu.Fragments  
PieDataSet com.tech.ekvayu.Fragments  PieEntry com.tech.ekvayu.Fragments  R com.tech.ekvayu.Fragments  RaiseBottomSheetFragment com.tech.ekvayu.Fragments  SharedPrefManager com.tech.ekvayu.Fragments  SpamMailAdapter com.tech.ekvayu.Fragments  SpamMailFragment com.tech.ekvayu.Fragments  String com.tech.ekvayu.Fragments  SuggetionBottomFragment com.tech.ekvayu.Fragments  	Throwable com.tech.ekvayu.Fragments  Toast com.tech.ekvayu.Fragments  
ViewCompat com.tech.ekvayu.Fragments  WarningBottomSheetFragment com.tech.ekvayu.Fragments  WindowInsetsCompat com.tech.ekvayu.Fragments  
appendLine com.tech.ekvayu.Fragments  arrayOf com.tech.ekvayu.Fragments  binding com.tech.ekvayu.Fragments  buildString com.tech.ekvayu.Fragments  com com.tech.ekvayu.Fragments  deviceInfoHelper com.tech.ekvayu.Fragments  displayDeviceInfo com.tech.ekvayu.Fragments  filter com.tech.ekvayu.Fragments  
filterIndexed com.tech.ekvayu.Fragments  format com.tech.ekvayu.Fragments  formatBytes com.tech.ekvayu.Fragments  getValue com.tech.ekvayu.Fragments  
isNotEmpty com.tech.ekvayu.Fragments  java com.tech.ekvayu.Fragments  launch com.tech.ekvayu.Fragments  lazy com.tech.ekvayu.Fragments  lifecycleScope com.tech.ekvayu.Fragments  listOf com.tech.ekvayu.Fragments  map com.tech.ekvayu.Fragments  mapOf com.tech.ekvayu.Fragments  provideDelegate com.tech.ekvayu.Fragments  repeat com.tech.ekvayu.Fragments  requireActivity com.tech.ekvayu.Fragments  requireContext com.tech.ekvayu.Fragments  to com.tech.ekvayu.Fragments  toList com.tech.ekvayu.Fragments  toString com.tech.ekvayu.Fragments  toTypedArray com.tech.ekvayu.Fragments  withContext com.tech.ekvayu.Fragments  ActivityGraphResponse /com.tech.ekvayu.Fragments.ActivityGraphFragment  	ApiClient /com.tech.ekvayu.Fragments.ActivityGraphFragment  
ApiService /com.tech.ekvayu.Fragments.ActivityGraphFragment  AppConstant /com.tech.ekvayu.Fragments.ActivityGraphFragment  Bundle /com.tech.ekvayu.Fragments.ActivityGraphFragment  Call /com.tech.ekvayu.Fragments.ActivityGraphFragment  Callback /com.tech.ekvayu.Fragments.ActivityGraphFragment  Color /com.tech.ekvayu.Fragments.ActivityGraphFragment  
ColorTemplate /com.tech.ekvayu.Fragments.ActivityGraphFragment  
CommonRequest /com.tech.ekvayu.Fragments.ActivityGraphFragment  
CommonUtil /com.tech.ekvayu.Fragments.ActivityGraphFragment  FragmentActivityGraphBinding /com.tech.ekvayu.Fragments.ActivityGraphFragment  LayoutInflater /com.tech.ekvayu.Fragments.ActivityGraphFragment  PieData /com.tech.ekvayu.Fragments.ActivityGraphFragment  
PieDataSet /com.tech.ekvayu.Fragments.ActivityGraphFragment  PieEntry /com.tech.ekvayu.Fragments.ActivityGraphFragment  Response /com.tech.ekvayu.Fragments.ActivityGraphFragment  SharedPrefManager /com.tech.ekvayu.Fragments.ActivityGraphFragment  String /com.tech.ekvayu.Fragments.ActivityGraphFragment  	Throwable /com.tech.ekvayu.Fragments.ActivityGraphFragment  Toast /com.tech.ekvayu.Fragments.ActivityGraphFragment  View /com.tech.ekvayu.Fragments.ActivityGraphFragment  
ViewCompat /com.tech.ekvayu.Fragments.ActivityGraphFragment  	ViewGroup /com.tech.ekvayu.Fragments.ActivityGraphFragment  WindowInsetsCompat /com.tech.ekvayu.Fragments.ActivityGraphFragment  binding /com.tech.ekvayu.Fragments.ActivityGraphFragment  
getAcityGraph /com.tech.ekvayu.Fragments.ActivityGraphFragment  getGETValue /com.tech.ekvayu.Fragments.ActivityGraphFragment  getGetValue /com.tech.ekvayu.Fragments.ActivityGraphFragment  getLAZY /com.tech.ekvayu.Fragments.ActivityGraphFragment  getLazy /com.tech.ekvayu.Fragments.ActivityGraphFragment  getMAP /com.tech.ekvayu.Fragments.ActivityGraphFragment  getMAPOf /com.tech.ekvayu.Fragments.ActivityGraphFragment  getMap /com.tech.ekvayu.Fragments.ActivityGraphFragment  getMapOf /com.tech.ekvayu.Fragments.ActivityGraphFragment  getPROVIDEDelegate /com.tech.ekvayu.Fragments.ActivityGraphFragment  getProvideDelegate /com.tech.ekvayu.Fragments.ActivityGraphFragment  getTO /com.tech.ekvayu.Fragments.ActivityGraphFragment  	getTOList /com.tech.ekvayu.Fragments.ActivityGraphFragment  getTOString /com.tech.ekvayu.Fragments.ActivityGraphFragment  getTo /com.tech.ekvayu.Fragments.ActivityGraphFragment  	getToList /com.tech.ekvayu.Fragments.ActivityGraphFragment  getToString /com.tech.ekvayu.Fragments.ActivityGraphFragment  getValue /com.tech.ekvayu.Fragments.ActivityGraphFragment  java /com.tech.ekvayu.Fragments.ActivityGraphFragment  lazy /com.tech.ekvayu.Fragments.ActivityGraphFragment  map /com.tech.ekvayu.Fragments.ActivityGraphFragment  mapOf /com.tech.ekvayu.Fragments.ActivityGraphFragment  provideDelegate /com.tech.ekvayu.Fragments.ActivityGraphFragment  requireContext /com.tech.ekvayu.Fragments.ActivityGraphFragment  sharedPrefManager /com.tech.ekvayu.Fragments.ActivityGraphFragment  to /com.tech.ekvayu.Fragments.ActivityGraphFragment  toList /com.tech.ekvayu.Fragments.ActivityGraphFragment  toString /com.tech.ekvayu.Fragments.ActivityGraphFragment  
getBINDING Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  
getBinding Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  getMAP Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  getMAPOf Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  getMap Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  getMapOf Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  getREQUIREContext Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  getRequireContext Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  getTO Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  	getTOList Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  getTOString Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  getTo Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  	getToList Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  getToString Pcom.tech.ekvayu.Fragments.ActivityGraphFragment.getAcityGraph.<no name provided>  ActivityCompat /com.tech.ekvayu.Fragments.DeviceDetailsFragment  Array /com.tech.ekvayu.Fragments.DeviceDetailsFragment  Bundle /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
ContextCompat /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
DeviceInfo /com.tech.ekvayu.Fragments.DeviceDetailsFragment  DeviceInfoHelper /com.tech.ekvayu.Fragments.DeviceDetailsFragment  Dispatchers /com.tech.ekvayu.Fragments.DeviceDetailsFragment  	Exception /com.tech.ekvayu.Fragments.DeviceDetailsFragment  FragmentDeviceDetailsBinding /com.tech.ekvayu.Fragments.DeviceDetailsFragment  Int /com.tech.ekvayu.Fragments.DeviceDetailsFragment  IntArray /com.tech.ekvayu.Fragments.DeviceDetailsFragment  LayoutInflater /com.tech.ekvayu.Fragments.DeviceDetailsFragment  Log /com.tech.ekvayu.Fragments.DeviceDetailsFragment  Long /com.tech.ekvayu.Fragments.DeviceDetailsFragment  Manifest /com.tech.ekvayu.Fragments.DeviceDetailsFragment  PackageManager /com.tech.ekvayu.Fragments.DeviceDetailsFragment  REQUEST_PERMISSIONS /com.tech.ekvayu.Fragments.DeviceDetailsFragment  String /com.tech.ekvayu.Fragments.DeviceDetailsFragment  Toast /com.tech.ekvayu.Fragments.DeviceDetailsFragment  View /com.tech.ekvayu.Fragments.DeviceDetailsFragment  	ViewGroup /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
appendLine /com.tech.ekvayu.Fragments.DeviceDetailsFragment  arrayOf /com.tech.ekvayu.Fragments.DeviceDetailsFragment  binding /com.tech.ekvayu.Fragments.DeviceDetailsFragment  buildString /com.tech.ekvayu.Fragments.DeviceDetailsFragment  checkAndRequestPermissions /com.tech.ekvayu.Fragments.DeviceDetailsFragment  context /com.tech.ekvayu.Fragments.DeviceDetailsFragment  deviceInfoHelper /com.tech.ekvayu.Fragments.DeviceDetailsFragment  displayDeviceInfo /com.tech.ekvayu.Fragments.DeviceDetailsFragment  filter /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
filterIndexed /com.tech.ekvayu.Fragments.DeviceDetailsFragment  format /com.tech.ekvayu.Fragments.DeviceDetailsFragment  formatBytes /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
getARRAYOf /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
getArrayOf /com.tech.ekvayu.Fragments.DeviceDetailsFragment  getBUILDString /com.tech.ekvayu.Fragments.DeviceDetailsFragment  getBuildString /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
getCONTEXT /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
getContext /com.tech.ekvayu.Fragments.DeviceDetailsFragment  	getFILTER /com.tech.ekvayu.Fragments.DeviceDetailsFragment  getFILTERIndexed /com.tech.ekvayu.Fragments.DeviceDetailsFragment  	getFORMAT /com.tech.ekvayu.Fragments.DeviceDetailsFragment  	getFilter /com.tech.ekvayu.Fragments.DeviceDetailsFragment  getFilterIndexed /com.tech.ekvayu.Fragments.DeviceDetailsFragment  	getFormat /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
getISNotEmpty /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
getIsNotEmpty /com.tech.ekvayu.Fragments.DeviceDetailsFragment  	getLAUNCH /com.tech.ekvayu.Fragments.DeviceDetailsFragment  getLIFECYCLEScope /com.tech.ekvayu.Fragments.DeviceDetailsFragment  	getLaunch /com.tech.ekvayu.Fragments.DeviceDetailsFragment  getLifecycleScope /com.tech.ekvayu.Fragments.DeviceDetailsFragment  	getREPEAT /com.tech.ekvayu.Fragments.DeviceDetailsFragment  	getRepeat /com.tech.ekvayu.Fragments.DeviceDetailsFragment  getTOTypedArray /com.tech.ekvayu.Fragments.DeviceDetailsFragment  getToTypedArray /com.tech.ekvayu.Fragments.DeviceDetailsFragment  getWITHContext /com.tech.ekvayu.Fragments.DeviceDetailsFragment  getWithContext /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
isNotEmpty /com.tech.ekvayu.Fragments.DeviceDetailsFragment  launch /com.tech.ekvayu.Fragments.DeviceDetailsFragment  lifecycleScope /com.tech.ekvayu.Fragments.DeviceDetailsFragment  loadDeviceInfo /com.tech.ekvayu.Fragments.DeviceDetailsFragment  repeat /com.tech.ekvayu.Fragments.DeviceDetailsFragment  requireActivity /com.tech.ekvayu.Fragments.DeviceDetailsFragment  requireContext /com.tech.ekvayu.Fragments.DeviceDetailsFragment  
setContext /com.tech.ekvayu.Fragments.DeviceDetailsFragment  toTypedArray /com.tech.ekvayu.Fragments.DeviceDetailsFragment  withContext /com.tech.ekvayu.Fragments.DeviceDetailsFragment  AlertDialog 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  	ApiClient 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  
ApiService 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  Bundle 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  Call 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  Callback 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  
CommonUtil 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  Context 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  DisputeListAdapter 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  DisputeMailistResponse 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  FragmentDisputeMaillistBinding 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  GridLayoutManager 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  LayoutInflater 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  LayoutSpamDetailBinding 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  RaiseBottomSheetFragment 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  Response 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  Results 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  	Throwable 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  Toast 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  View 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  	ViewGroup 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  binding 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  com 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  getDisputelist 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  getTOString 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  getToString 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  java 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  requireActivity 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  requireContext 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  showCustomEmailDialog 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  toString 1com.tech.ekvayu.Fragments.DisputeMaillistFragment  
getBINDING Scom.tech.ekvayu.Fragments.DisputeMaillistFragment.getDisputelist.<no name provided>  
getBinding Scom.tech.ekvayu.Fragments.DisputeMaillistFragment.getDisputelist.<no name provided>  getREQUIREActivity Scom.tech.ekvayu.Fragments.DisputeMaillistFragment.getDisputelist.<no name provided>  getREQUIREContext Scom.tech.ekvayu.Fragments.DisputeMaillistFragment.getDisputelist.<no name provided>  getRequireActivity Scom.tech.ekvayu.Fragments.DisputeMaillistFragment.getDisputelist.<no name provided>  getRequireContext Scom.tech.ekvayu.Fragments.DisputeMaillistFragment.getDisputelist.<no name provided>  ActivityGraphFragment &com.tech.ekvayu.Fragments.HomeFragment  ActivityGraphResponse &com.tech.ekvayu.Fragments.HomeFragment  	ApiClient &com.tech.ekvayu.Fragments.HomeFragment  
ApiService &com.tech.ekvayu.Fragments.HomeFragment  AppConstant &com.tech.ekvayu.Fragments.HomeFragment  Bundle &com.tech.ekvayu.Fragments.HomeFragment  Call &com.tech.ekvayu.Fragments.HomeFragment  Callback &com.tech.ekvayu.Fragments.HomeFragment  Color &com.tech.ekvayu.Fragments.HomeFragment  
ColorTemplate &com.tech.ekvayu.Fragments.HomeFragment  
CommonRequest &com.tech.ekvayu.Fragments.HomeFragment  
CommonUtil &com.tech.ekvayu.Fragments.HomeFragment  DashboardActivity &com.tech.ekvayu.Fragments.HomeFragment  DashboardMenuAdapter &com.tech.ekvayu.Fragments.HomeFragment  DashboardMenuModel &com.tech.ekvayu.Fragments.HomeFragment  DeviceDetailsFragment &com.tech.ekvayu.Fragments.HomeFragment  DisputeMaillistFragment &com.tech.ekvayu.Fragments.HomeFragment  FragmentHomeBinding &com.tech.ekvayu.Fragments.HomeFragment  GridLayoutManager &com.tech.ekvayu.Fragments.HomeFragment  LayoutInflater &com.tech.ekvayu.Fragments.HomeFragment  Log &com.tech.ekvayu.Fragments.HomeFragment  NewGmailAccessibilityService &com.tech.ekvayu.Fragments.HomeFragment  PieData &com.tech.ekvayu.Fragments.HomeFragment  
PieDataSet &com.tech.ekvayu.Fragments.HomeFragment  PieEntry &com.tech.ekvayu.Fragments.HomeFragment  R &com.tech.ekvayu.Fragments.HomeFragment  Response &com.tech.ekvayu.Fragments.HomeFragment  SharedPrefManager &com.tech.ekvayu.Fragments.HomeFragment  SpamMailFragment &com.tech.ekvayu.Fragments.HomeFragment  String &com.tech.ekvayu.Fragments.HomeFragment  SuggetionBottomFragment &com.tech.ekvayu.Fragments.HomeFragment  	Throwable &com.tech.ekvayu.Fragments.HomeFragment  Toast &com.tech.ekvayu.Fragments.HomeFragment  View &com.tech.ekvayu.Fragments.HomeFragment  	ViewGroup &com.tech.ekvayu.Fragments.HomeFragment  WarningBottomSheetFragment &com.tech.ekvayu.Fragments.HomeFragment  activity &com.tech.ekvayu.Fragments.HomeFragment  binding &com.tech.ekvayu.Fragments.HomeFragment  getACTIVITY &com.tech.ekvayu.Fragments.HomeFragment  
getAcityGraph &com.tech.ekvayu.Fragments.HomeFragment  getActivity &com.tech.ekvayu.Fragments.HomeFragment  getGETValue &com.tech.ekvayu.Fragments.HomeFragment  getGetValue &com.tech.ekvayu.Fragments.HomeFragment  getLAZY &com.tech.ekvayu.Fragments.HomeFragment  	getLISTOf &com.tech.ekvayu.Fragments.HomeFragment  getLazy &com.tech.ekvayu.Fragments.HomeFragment  	getListOf &com.tech.ekvayu.Fragments.HomeFragment  getMAP &com.tech.ekvayu.Fragments.HomeFragment  getMAPOf &com.tech.ekvayu.Fragments.HomeFragment  getMap &com.tech.ekvayu.Fragments.HomeFragment  getMapOf &com.tech.ekvayu.Fragments.HomeFragment  getPROVIDEDelegate &com.tech.ekvayu.Fragments.HomeFragment  getProvideDelegate &com.tech.ekvayu.Fragments.HomeFragment  getTO &com.tech.ekvayu.Fragments.HomeFragment  	getTOList &com.tech.ekvayu.Fragments.HomeFragment  getTo &com.tech.ekvayu.Fragments.HomeFragment  	getToList &com.tech.ekvayu.Fragments.HomeFragment  getValue &com.tech.ekvayu.Fragments.HomeFragment  java &com.tech.ekvayu.Fragments.HomeFragment  lazy &com.tech.ekvayu.Fragments.HomeFragment  listOf &com.tech.ekvayu.Fragments.HomeFragment  map &com.tech.ekvayu.Fragments.HomeFragment  mapOf &com.tech.ekvayu.Fragments.HomeFragment  menus &com.tech.ekvayu.Fragments.HomeFragment  provideDelegate &com.tech.ekvayu.Fragments.HomeFragment  receiverMail &com.tech.ekvayu.Fragments.HomeFragment  requireActivity &com.tech.ekvayu.Fragments.HomeFragment  requireContext &com.tech.ekvayu.Fragments.HomeFragment  setActivity &com.tech.ekvayu.Fragments.HomeFragment  sharedPrefManager &com.tech.ekvayu.Fragments.HomeFragment  to &com.tech.ekvayu.Fragments.HomeFragment  toList &com.tech.ekvayu.Fragments.HomeFragment  
getBINDING Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  
getBinding Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  getMAP Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  getMAPOf Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  getMap Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  getMapOf Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  getREQUIREContext Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  getRequireContext Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  getTO Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  	getTOList Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  getTo Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  	getToList Gcom.tech.ekvayu.Fragments.HomeFragment.getAcityGraph.<no name provided>  AlertDialog *com.tech.ekvayu.Fragments.SpamMailFragment  	ApiClient *com.tech.ekvayu.Fragments.SpamMailFragment  
ApiService *com.tech.ekvayu.Fragments.SpamMailFragment  AppConstant *com.tech.ekvayu.Fragments.SpamMailFragment  Bundle *com.tech.ekvayu.Fragments.SpamMailFragment  Call *com.tech.ekvayu.Fragments.SpamMailFragment  Callback *com.tech.ekvayu.Fragments.SpamMailFragment  
CommonRequest *com.tech.ekvayu.Fragments.SpamMailFragment  
CommonUtil *com.tech.ekvayu.Fragments.SpamMailFragment  Context *com.tech.ekvayu.Fragments.SpamMailFragment  FragmentSpamMailBinding *com.tech.ekvayu.Fragments.SpamMailFragment  GridLayoutManager *com.tech.ekvayu.Fragments.SpamMailFragment  LayoutInflater *com.tech.ekvayu.Fragments.SpamMailFragment  LayoutSpamDetailBinding *com.tech.ekvayu.Fragments.SpamMailFragment  Response *com.tech.ekvayu.Fragments.SpamMailFragment  Results *com.tech.ekvayu.Fragments.SpamMailFragment  SharedPrefManager *com.tech.ekvayu.Fragments.SpamMailFragment  SpamMailAdapter *com.tech.ekvayu.Fragments.SpamMailFragment  SpamMailResponse *com.tech.ekvayu.Fragments.SpamMailFragment  String *com.tech.ekvayu.Fragments.SpamMailFragment  	Throwable *com.tech.ekvayu.Fragments.SpamMailFragment  Toast *com.tech.ekvayu.Fragments.SpamMailFragment  View *com.tech.ekvayu.Fragments.SpamMailFragment  	ViewGroup *com.tech.ekvayu.Fragments.SpamMailFragment  binding *com.tech.ekvayu.Fragments.SpamMailFragment  getGETValue *com.tech.ekvayu.Fragments.SpamMailFragment  getGetValue *com.tech.ekvayu.Fragments.SpamMailFragment  getLAZY *com.tech.ekvayu.Fragments.SpamMailFragment  getLazy *com.tech.ekvayu.Fragments.SpamMailFragment  getPROVIDEDelegate *com.tech.ekvayu.Fragments.SpamMailFragment  getProvideDelegate *com.tech.ekvayu.Fragments.SpamMailFragment  getSpamMail *com.tech.ekvayu.Fragments.SpamMailFragment  getTOString *com.tech.ekvayu.Fragments.SpamMailFragment  getToString *com.tech.ekvayu.Fragments.SpamMailFragment  getValue *com.tech.ekvayu.Fragments.SpamMailFragment  java *com.tech.ekvayu.Fragments.SpamMailFragment  lazy *com.tech.ekvayu.Fragments.SpamMailFragment  provideDelegate *com.tech.ekvayu.Fragments.SpamMailFragment  requireActivity *com.tech.ekvayu.Fragments.SpamMailFragment  requireContext *com.tech.ekvayu.Fragments.SpamMailFragment  sharedPrefManager *com.tech.ekvayu.Fragments.SpamMailFragment  showCustomEmailDialog *com.tech.ekvayu.Fragments.SpamMailFragment  toString *com.tech.ekvayu.Fragments.SpamMailFragment  
getBINDING Icom.tech.ekvayu.Fragments.SpamMailFragment.getSpamMail.<no name provided>  
getBinding Icom.tech.ekvayu.Fragments.SpamMailFragment.getSpamMail.<no name provided>  getREQUIREActivity Icom.tech.ekvayu.Fragments.SpamMailFragment.getSpamMail.<no name provided>  getREQUIREContext Icom.tech.ekvayu.Fragments.SpamMailFragment.getSpamMail.<no name provided>  getRequireActivity Icom.tech.ekvayu.Fragments.SpamMailFragment.getSpamMail.<no name provided>  getRequireContext Icom.tech.ekvayu.Fragments.SpamMailFragment.getSpamMail.<no name provided>  color com.tech.ekvayu.R  drawable com.tech.ekvayu.R  id com.tech.ekvayu.R  raw com.tech.ekvayu.R  	app_color com.tech.ekvayu.R.color  white com.tech.ekvayu.R.color  argument com.tech.ekvayu.R.drawable  bg_alert_dialog com.tech.ekvayu.R.drawable  bg_button_app_color com.tech.ekvayu.R.drawable  bg_button_grey_color com.tech.ekvayu.R.drawable  	bg_dialog com.tech.ekvayu.R.drawable  details com.tech.ekvayu.R.drawable  process com.tech.ekvayu.R.drawable  	spam_mail com.tech.ekvayu.R.drawable  fragmentContainer com.tech.ekvayu.R.id  safe com.tech.ekvayu.R.raw  unsafe com.tech.ekvayu.R.raw  wait com.tech.ekvayu.R.raw  
CommonRequest com.tech.ekvayu.Request  PendingMailRequest com.tech.ekvayu.Request  String com.tech.ekvayu.Request  String %com.tech.ekvayu.Request.CommonRequest  String *com.tech.ekvayu.Request.PendingMailRequest  	ArrayList com.tech.ekvayu.Response  Boolean com.tech.ekvayu.Response  Data com.tech.ekvayu.Response  
EmailResponse com.tech.ekvayu.Response  HashResponse com.tech.ekvayu.Response  Int com.tech.ekvayu.Response  
PedingMailRes com.tech.ekvayu.Response  Results com.tech.ekvayu.Response  SpamMailResponse com.tech.ekvayu.Response  String com.tech.ekvayu.Response  arrayListOf com.tech.ekvayu.Response  	ArrayList com.tech.ekvayu.Response.Data  Int com.tech.ekvayu.Response.Data  SerializedName com.tech.ekvayu.Response.Data  String com.tech.ekvayu.Response.Data  arrayListOf com.tech.ekvayu.Response.Data  code com.tech.ekvayu.Response.Data  dispute_counter com.tech.ekvayu.Response.Data  email com.tech.ekvayu.Response.Data  	emlStatus com.tech.ekvayu.Response.Data  hashId com.tech.ekvayu.Response.Data  
unsafeReasons com.tech.ekvayu.Response.Data  Code &com.tech.ekvayu.Response.EmailResponse  Int &com.tech.ekvayu.Response.EmailResponse  SerializedName &com.tech.ekvayu.Response.EmailResponse  String &com.tech.ekvayu.Response.EmailResponse  hashId &com.tech.ekvayu.Response.EmailResponse  message &com.tech.ekvayu.Response.EmailResponse  Code %com.tech.ekvayu.Response.HashResponse  Int %com.tech.ekvayu.Response.HashResponse  SerializedName %com.tech.ekvayu.Response.HashResponse  String %com.tech.ekvayu.Response.HashResponse  hashId %com.tech.ekvayu.Response.HashResponse  Data &com.tech.ekvayu.Response.PedingMailRes  Int &com.tech.ekvayu.Response.PedingMailRes  SerializedName &com.tech.ekvayu.Response.PedingMailRes  String &com.tech.ekvayu.Response.PedingMailRes  data &com.tech.ekvayu.Response.PedingMailRes  Boolean  com.tech.ekvayu.Response.Results  SerializedName  com.tech.ekvayu.Response.Results  String  com.tech.ekvayu.Response.Results  	emailBody  com.tech.ekvayu.Response.Results  recieversEmail  com.tech.ekvayu.Response.Results  sendersEmail  com.tech.ekvayu.Response.Results  status  com.tech.ekvayu.Response.Results  subject  com.tech.ekvayu.Response.Results  	ArrayList )com.tech.ekvayu.Response.SpamMailResponse  Int )com.tech.ekvayu.Response.SpamMailResponse  Results )com.tech.ekvayu.Response.SpamMailResponse  SerializedName )com.tech.ekvayu.Response.SpamMailResponse  String )com.tech.ekvayu.Response.SpamMailResponse  arrayListOf )com.tech.ekvayu.Response.SpamMailResponse  results )com.tech.ekvayu.Response.SpamMailResponse  ActivityDashboardBinding com.tech.ekvayu.databinding  ActivityMainBinding com.tech.ekvayu.databinding  ActivityYahooAuthBinding com.tech.ekvayu.databinding  FragmentActivityGraphBinding com.tech.ekvayu.databinding  FragmentDeviceDetailsBinding com.tech.ekvayu.databinding  FragmentDisputeMaillistBinding com.tech.ekvayu.databinding  FragmentHomeBinding com.tech.ekvayu.databinding  FragmentRaiseBottomSheetBinding com.tech.ekvayu.databinding  FragmentSpamMailBinding com.tech.ekvayu.databinding  FragmentSuggetionBottomBinding com.tech.ekvayu.databinding  !FragmentWarningBottomSheetBinding com.tech.ekvayu.databinding  ItemDashboardMenuBinding com.tech.ekvayu.databinding  ItemDisputeListBinding com.tech.ekvayu.databinding  ItemSpamMailBinding com.tech.ekvayu.databinding  LayoutProgressBinding com.tech.ekvayu.databinding  LayoutProgressDialogBinding com.tech.ekvayu.databinding  LayoutSpamDetailBinding com.tech.ekvayu.databinding  LayoutValidationBinding com.tech.ekvayu.databinding  getROOT 4com.tech.ekvayu.databinding.ActivityDashboardBinding  getRoot 4com.tech.ekvayu.databinding.ActivityDashboardBinding  header 4com.tech.ekvayu.databinding.ActivityDashboardBinding  inflate 4com.tech.ekvayu.databinding.ActivityDashboardBinding  root 4com.tech.ekvayu.databinding.ActivityDashboardBinding  setRoot 4com.tech.ekvayu.databinding.ActivityDashboardBinding  btPermission /com.tech.ekvayu.databinding.ActivityMainBinding  getROOT /com.tech.ekvayu.databinding.ActivityMainBinding  getRoot /com.tech.ekvayu.databinding.ActivityMainBinding  inflate /com.tech.ekvayu.databinding.ActivityMainBinding  root /com.tech.ekvayu.databinding.ActivityMainBinding  setRoot /com.tech.ekvayu.databinding.ActivityMainBinding  btAuth 4com.tech.ekvayu.databinding.ActivityYahooAuthBinding  getROOT 4com.tech.ekvayu.databinding.ActivityYahooAuthBinding  getRoot 4com.tech.ekvayu.databinding.ActivityYahooAuthBinding  inflate 4com.tech.ekvayu.databinding.ActivityYahooAuthBinding  root 4com.tech.ekvayu.databinding.ActivityYahooAuthBinding  setRoot 4com.tech.ekvayu.databinding.ActivityYahooAuthBinding  getROOT 8com.tech.ekvayu.databinding.FragmentActivityGraphBinding  getRoot 8com.tech.ekvayu.databinding.FragmentActivityGraphBinding  inflate 8com.tech.ekvayu.databinding.FragmentActivityGraphBinding  main 8com.tech.ekvayu.databinding.FragmentActivityGraphBinding  pieChart 8com.tech.ekvayu.databinding.FragmentActivityGraphBinding  root 8com.tech.ekvayu.databinding.FragmentActivityGraphBinding  setRoot 8com.tech.ekvayu.databinding.FragmentActivityGraphBinding  	tvDispute 8com.tech.ekvayu.databinding.FragmentActivityGraphBinding  tvProcessed 8com.tech.ekvayu.databinding.FragmentActivityGraphBinding  tvSpam 8com.tech.ekvayu.databinding.FragmentActivityGraphBinding  getROOT 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  getRoot 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  inflate 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  root 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  setRoot 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvAndroidId 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvAndroidVersion 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  
tvApiLevel 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  
tvBradName 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  
tvBuildNumber 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  	tvDensity 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvDensityFactor 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvDeviceName 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvManufacturer 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvMinSdk 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvModelName 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  	tvPackage 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  
tvProductName 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvResolution 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvScaleDensity 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvSerialNumber 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  tvTargetSdk 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  	tvVersion 8com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding  btRaiseDispute :com.tech.ekvayu.databinding.FragmentDisputeMaillistBinding  getROOT :com.tech.ekvayu.databinding.FragmentDisputeMaillistBinding  getRoot :com.tech.ekvayu.databinding.FragmentDisputeMaillistBinding  inflate :com.tech.ekvayu.databinding.FragmentDisputeMaillistBinding  root :com.tech.ekvayu.databinding.FragmentDisputeMaillistBinding  
rvDisputelist :com.tech.ekvayu.databinding.FragmentDisputeMaillistBinding  setRoot :com.tech.ekvayu.databinding.FragmentDisputeMaillistBinding  getROOT /com.tech.ekvayu.databinding.FragmentHomeBinding  getRoot /com.tech.ekvayu.databinding.FragmentHomeBinding  inflate /com.tech.ekvayu.databinding.FragmentHomeBinding  pieChart /com.tech.ekvayu.databinding.FragmentHomeBinding  root /com.tech.ekvayu.databinding.FragmentHomeBinding  rvMenu /com.tech.ekvayu.databinding.FragmentHomeBinding  setRoot /com.tech.ekvayu.databinding.FragmentHomeBinding  	tvDispute /com.tech.ekvayu.databinding.FragmentHomeBinding  	tvProcess /com.tech.ekvayu.databinding.FragmentHomeBinding  tvSpam /com.tech.ekvayu.databinding.FragmentHomeBinding  btSubmit ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  clMain ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  	etCounter ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  etMessageId ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  etReason ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  etSenderMail ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  etStatus ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  getROOT ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  getRoot ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  inflate ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  root ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  setRoot ;com.tech.ekvayu.databinding.FragmentRaiseBottomSheetBinding  getROOT 3com.tech.ekvayu.databinding.FragmentSpamMailBinding  getRoot 3com.tech.ekvayu.databinding.FragmentSpamMailBinding  inflate 3com.tech.ekvayu.databinding.FragmentSpamMailBinding  root 3com.tech.ekvayu.databinding.FragmentSpamMailBinding  
rvSpamMail 3com.tech.ekvayu.databinding.FragmentSpamMailBinding  setRoot 3com.tech.ekvayu.databinding.FragmentSpamMailBinding  btGotoGmail :com.tech.ekvayu.databinding.FragmentSuggetionBottomBinding  getROOT :com.tech.ekvayu.databinding.FragmentSuggetionBottomBinding  getRoot :com.tech.ekvayu.databinding.FragmentSuggetionBottomBinding  inflate :com.tech.ekvayu.databinding.FragmentSuggetionBottomBinding  root :com.tech.ekvayu.databinding.FragmentSuggetionBottomBinding  setRoot :com.tech.ekvayu.databinding.FragmentSuggetionBottomBinding  btPermission =com.tech.ekvayu.databinding.FragmentWarningBottomSheetBinding  getROOT =com.tech.ekvayu.databinding.FragmentWarningBottomSheetBinding  getRoot =com.tech.ekvayu.databinding.FragmentWarningBottomSheetBinding  inflate =com.tech.ekvayu.databinding.FragmentWarningBottomSheetBinding  root =com.tech.ekvayu.databinding.FragmentWarningBottomSheetBinding  setRoot =com.tech.ekvayu.databinding.FragmentWarningBottomSheetBinding  cvBack /com.tech.ekvayu.databinding.HeaderLayoutBinding  cvMain 4com.tech.ekvayu.databinding.ItemDashboardMenuBinding  getROOT 4com.tech.ekvayu.databinding.ItemDashboardMenuBinding  getRoot 4com.tech.ekvayu.databinding.ItemDashboardMenuBinding  inflate 4com.tech.ekvayu.databinding.ItemDashboardMenuBinding  ivIcon 4com.tech.ekvayu.databinding.ItemDashboardMenuBinding  root 4com.tech.ekvayu.databinding.ItemDashboardMenuBinding  setRoot 4com.tech.ekvayu.databinding.ItemDashboardMenuBinding  tvDescripton 4com.tech.ekvayu.databinding.ItemDashboardMenuBinding  
tvMenuName 4com.tech.ekvayu.databinding.ItemDashboardMenuBinding  getROOT 2com.tech.ekvayu.databinding.ItemDisputeListBinding  getRoot 2com.tech.ekvayu.databinding.ItemDisputeListBinding  inflate 2com.tech.ekvayu.databinding.ItemDisputeListBinding  root 2com.tech.ekvayu.databinding.ItemDisputeListBinding  setRoot 2com.tech.ekvayu.databinding.ItemDisputeListBinding  
tvReceiver 2com.tech.ekvayu.databinding.ItemDisputeListBinding  tvSender 2com.tech.ekvayu.databinding.ItemDisputeListBinding  tvStatus 2com.tech.ekvayu.databinding.ItemDisputeListBinding  getROOT /com.tech.ekvayu.databinding.ItemSpamMailBinding  getRoot /com.tech.ekvayu.databinding.ItemSpamMailBinding  inflate /com.tech.ekvayu.databinding.ItemSpamMailBinding  root /com.tech.ekvayu.databinding.ItemSpamMailBinding  setRoot /com.tech.ekvayu.databinding.ItemSpamMailBinding  
tvReceiver /com.tech.ekvayu.databinding.ItemSpamMailBinding  tvSender /com.tech.ekvayu.databinding.ItemSpamMailBinding  tvStatus /com.tech.ekvayu.databinding.ItemSpamMailBinding  getROOT 1com.tech.ekvayu.databinding.LayoutProgressBinding  getRoot 1com.tech.ekvayu.databinding.LayoutProgressBinding  inflate 1com.tech.ekvayu.databinding.LayoutProgressBinding  root 1com.tech.ekvayu.databinding.LayoutProgressBinding  setRoot 1com.tech.ekvayu.databinding.LayoutProgressBinding  getROOT 7com.tech.ekvayu.databinding.LayoutProgressDialogBinding  getRoot 7com.tech.ekvayu.databinding.LayoutProgressDialogBinding  inflate 7com.tech.ekvayu.databinding.LayoutProgressDialogBinding  root 7com.tech.ekvayu.databinding.LayoutProgressDialogBinding  setRoot 7com.tech.ekvayu.databinding.LayoutProgressDialogBinding  	tvMessage 7com.tech.ekvayu.databinding.LayoutProgressDialogBinding  getROOT 3com.tech.ekvayu.databinding.LayoutSpamDetailBinding  getRoot 3com.tech.ekvayu.databinding.LayoutSpamDetailBinding  inflate 3com.tech.ekvayu.databinding.LayoutSpamDetailBinding  root 3com.tech.ekvayu.databinding.LayoutSpamDetailBinding  setRoot 3com.tech.ekvayu.databinding.LayoutSpamDetailBinding  tvBody 3com.tech.ekvayu.databinding.LayoutSpamDetailBinding  
tvReceiver 3com.tech.ekvayu.databinding.LayoutSpamDetailBinding  tvSender 3com.tech.ekvayu.databinding.LayoutSpamDetailBinding  tvStatus 3com.tech.ekvayu.databinding.LayoutSpamDetailBinding  	tvSubject 3com.tech.ekvayu.databinding.LayoutSpamDetailBinding  btClose 3com.tech.ekvayu.databinding.LayoutValidationBinding  getROOT 3com.tech.ekvayu.databinding.LayoutValidationBinding  getRoot 3com.tech.ekvayu.databinding.LayoutValidationBinding  inflate 3com.tech.ekvayu.databinding.LayoutValidationBinding  	lvWarning 3com.tech.ekvayu.databinding.LayoutValidationBinding  root 3com.tech.ekvayu.databinding.LayoutValidationBinding  setRoot 3com.tech.ekvayu.databinding.LayoutValidationBinding  	tvMessage 3com.tech.ekvayu.databinding.LayoutValidationBinding  Any com.tech.ekvayu.models  AppInfo com.tech.ekvayu.models  BatteryInfo com.tech.ekvayu.models  Boolean com.tech.ekvayu.models  DashboardMenuModel com.tech.ekvayu.models  
DeviceInfo com.tech.ekvayu.models  Float com.tech.ekvayu.models  Int com.tech.ekvayu.models  Long com.tech.ekvayu.models  
MemoryInfo com.tech.ekvayu.models  NetworkInfo com.tech.ekvayu.models  
ScreenInfo com.tech.ekvayu.models  StorageInfo com.tech.ekvayu.models  String com.tech.ekvayu.models  
SystemInfo com.tech.ekvayu.models  
TelephonyInfo com.tech.ekvayu.models  format com.tech.ekvayu.models  Any com.tech.ekvayu.models.AppInfo  Int com.tech.ekvayu.models.AppInfo  Long com.tech.ekvayu.models.AppInfo  String com.tech.ekvayu.models.AppInfo  
minSdkVersion com.tech.ekvayu.models.AppInfo  packageName com.tech.ekvayu.models.AppInfo  targetSdkVersion com.tech.ekvayu.models.AppInfo  versionCode com.tech.ekvayu.models.AppInfo  versionName com.tech.ekvayu.models.AppInfo  Boolean "com.tech.ekvayu.models.BatteryInfo  Int "com.tech.ekvayu.models.BatteryInfo  String "com.tech.ekvayu.models.BatteryInfo  getBatteryHealth "com.tech.ekvayu.models.BatteryInfo  getBatteryStatus "com.tech.ekvayu.models.BatteryInfo  
isCharging "com.tech.ekvayu.models.BatteryInfo  level "com.tech.ekvayu.models.BatteryInfo  temperature "com.tech.ekvayu.models.BatteryInfo  voltage "com.tech.ekvayu.models.BatteryInfo  DrawableRes )com.tech.ekvayu.models.DashboardMenuModel  Int )com.tech.ekvayu.models.DashboardMenuModel  String )com.tech.ekvayu.models.DashboardMenuModel  desc )com.tech.ekvayu.models.DashboardMenuModel  	iconResId )com.tech.ekvayu.models.DashboardMenuModel  title )com.tech.ekvayu.models.DashboardMenuModel  AppInfo !com.tech.ekvayu.models.DeviceInfo  BatteryInfo !com.tech.ekvayu.models.DeviceInfo  Int !com.tech.ekvayu.models.DeviceInfo  
MemoryInfo !com.tech.ekvayu.models.DeviceInfo  NetworkInfo !com.tech.ekvayu.models.DeviceInfo  
ScreenInfo !com.tech.ekvayu.models.DeviceInfo  StorageInfo !com.tech.ekvayu.models.DeviceInfo  String !com.tech.ekvayu.models.DeviceInfo  
SystemInfo !com.tech.ekvayu.models.DeviceInfo  
TelephonyInfo !com.tech.ekvayu.models.DeviceInfo  	androidId !com.tech.ekvayu.models.DeviceInfo  androidVersion !com.tech.ekvayu.models.DeviceInfo  apiLevel !com.tech.ekvayu.models.DeviceInfo  appInfo !com.tech.ekvayu.models.DeviceInfo  batteryInfo !com.tech.ekvayu.models.DeviceInfo  brand !com.tech.ekvayu.models.DeviceInfo  buildNumber !com.tech.ekvayu.models.DeviceInfo  
deviceName !com.tech.ekvayu.models.DeviceInfo  manufacturer !com.tech.ekvayu.models.DeviceInfo  
memoryInfo !com.tech.ekvayu.models.DeviceInfo  model !com.tech.ekvayu.models.DeviceInfo  networkInfo !com.tech.ekvayu.models.DeviceInfo  product !com.tech.ekvayu.models.DeviceInfo  
screenInfo !com.tech.ekvayu.models.DeviceInfo  serialNumber !com.tech.ekvayu.models.DeviceInfo  storageInfo !com.tech.ekvayu.models.DeviceInfo  
systemInfo !com.tech.ekvayu.models.DeviceInfo  
telephonyInfo !com.tech.ekvayu.models.DeviceInfo  	timestamp !com.tech.ekvayu.models.DeviceInfo  Boolean !com.tech.ekvayu.models.MemoryInfo  Int !com.tech.ekvayu.models.MemoryInfo  Long !com.tech.ekvayu.models.MemoryInfo  String !com.tech.ekvayu.models.MemoryInfo  availableRAM !com.tech.ekvayu.models.MemoryInfo  format !com.tech.ekvayu.models.MemoryInfo  getAvailableRAMGB !com.tech.ekvayu.models.MemoryInfo  	getFORMAT !com.tech.ekvayu.models.MemoryInfo  	getFormat !com.tech.ekvayu.models.MemoryInfo  getRAMUsagePercentage !com.tech.ekvayu.models.MemoryInfo  
getTotalRAMGB !com.tech.ekvayu.models.MemoryInfo  getUsedRAMGB !com.tech.ekvayu.models.MemoryInfo  heapMax !com.tech.ekvayu.models.MemoryInfo  heapSize !com.tech.ekvayu.models.MemoryInfo  heapUsed !com.tech.ekvayu.models.MemoryInfo  	lowMemory !com.tech.ekvayu.models.MemoryInfo  totalRAM !com.tech.ekvayu.models.MemoryInfo  usedRAM !com.tech.ekvayu.models.MemoryInfo  Boolean "com.tech.ekvayu.models.NetworkInfo  String "com.tech.ekvayu.models.NetworkInfo  connectionType "com.tech.ekvayu.models.NetworkInfo  	ipAddress "com.tech.ekvayu.models.NetworkInfo  isConnected "com.tech.ekvayu.models.NetworkInfo  wifiEnabled "com.tech.ekvayu.models.NetworkInfo  Float !com.tech.ekvayu.models.ScreenInfo  Int !com.tech.ekvayu.models.ScreenInfo  String !com.tech.ekvayu.models.ScreenInfo  density !com.tech.ekvayu.models.ScreenInfo  
densityDpi !com.tech.ekvayu.models.ScreenInfo  getDensityCategory !com.tech.ekvayu.models.ScreenInfo  
getResolution !com.tech.ekvayu.models.ScreenInfo  height !com.tech.ekvayu.models.ScreenInfo  
scaledDensity !com.tech.ekvayu.models.ScreenInfo  width !com.tech.ekvayu.models.ScreenInfo  Int "com.tech.ekvayu.models.StorageInfo  Long "com.tech.ekvayu.models.StorageInfo  String "com.tech.ekvayu.models.StorageInfo  format "com.tech.ekvayu.models.StorageInfo  freeInternal "com.tech.ekvayu.models.StorageInfo  	getFORMAT "com.tech.ekvayu.models.StorageInfo  	getFormat "com.tech.ekvayu.models.StorageInfo  getFreeInternalGB "com.tech.ekvayu.models.StorageInfo  getStorageUsagePercentage "com.tech.ekvayu.models.StorageInfo  getTotalInternalGB "com.tech.ekvayu.models.StorageInfo  getUsedInternalGB "com.tech.ekvayu.models.StorageInfo  
totalInternal "com.tech.ekvayu.models.StorageInfo  usedInternal "com.tech.ekvayu.models.StorageInfo  String !com.tech.ekvayu.models.SystemInfo  
bootloader !com.tech.ekvayu.models.SystemInfo  hardware !com.tech.ekvayu.models.SystemInfo  host !com.tech.ekvayu.models.SystemInfo  id !com.tech.ekvayu.models.SystemInfo  
javaVmVersion !com.tech.ekvayu.models.SystemInfo  
kernelVersion !com.tech.ekvayu.models.SystemInfo  type !com.tech.ekvayu.models.SystemInfo  user !com.tech.ekvayu.models.SystemInfo  Boolean $com.tech.ekvayu.models.TelephonyInfo  String $com.tech.ekvayu.models.TelephonyInfo  carrierName $com.tech.ekvayu.models.TelephonyInfo  countryCode $com.tech.ekvayu.models.TelephonyInfo  
hasPermission $com.tech.ekvayu.models.TelephonyInfo  networkType $com.tech.ekvayu.models.TelephonyInfo  	phoneType $com.tech.ekvayu.models.TelephonyInfo  BufferedReader java.io  File java.io  FileOutputStream java.io  
FileReader java.io  IOException java.io  close java.io.BufferedReader  readLine java.io.BufferedReader  absolutePath java.io.File  exists java.io.File  	freeSpace java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  getFREESpace java.io.File  getFreeSpace java.io.File  getNAME java.io.File  getName java.io.File  
getTOTALSpace java.io.File  
getTotalSpace java.io.File  name java.io.File  setAbsolutePath java.io.File  setFreeSpace java.io.File  setName java.io.File  
setTotalSpace java.io.File  
totalSpace java.io.File  close java.io.FileOutputStream  getUSE java.io.FileOutputStream  getUse java.io.FileOutputStream  use java.io.FileOutputStream  write java.io.FileOutputStream  message java.io.IOException  printStackTrace java.io.IOException  close java.io.OutputStream  use java.io.OutputStream  write java.io.OutputStream  close java.io.Reader  readLine java.io.Reader  AccessibilityEvent 	java.lang  ActivityCompat 	java.lang  ActivityDashboardBinding 	java.lang  ActivityGraphFragment 	java.lang  ActivityMainBinding 	java.lang  ActivityManager 	java.lang  ActivityYahooAuthBinding 	java.lang  AlertDialog 	java.lang  	ApiClient 	java.lang  
ApiService 	java.lang  AppConstant 	java.lang  AppInfo 	java.lang  BatteryInfo 	java.lang  BatteryManager 	java.lang  BufferedReader 	java.lang  Build 	java.lang  Class 	java.lang  Color 	java.lang  
ColorTemplate 	java.lang  
CommonRequest 	java.lang  
CommonUtil 	java.lang  
ComponentName 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  DashboardMenuAdapter 	java.lang  DashboardMenuModel 	java.lang  Date 	java.lang  DeviceDetailsFragment 	java.lang  
DeviceInfo 	java.lang  DeviceInfoHelper 	java.lang  Dialog 	java.lang  Dispatchers 	java.lang  DisplayMetrics 	java.lang  DisputeListAdapter 	java.lang  DisputeMaillistFragment 	java.lang  DisputeRaiseRequest 	java.lang  	Exception 	java.lang  File 	java.lang  FileOutputStream 	java.lang  
FileReader 	java.lang  FirebaseAuth 	java.lang  FragmentActivityGraphBinding 	java.lang  FragmentDeviceDetailsBinding 	java.lang  FragmentDisputeMaillistBinding 	java.lang  FragmentHomeBinding 	java.lang  FragmentRaiseBottomSheetBinding 	java.lang  FragmentSpamMailBinding 	java.lang  FragmentSuggetionBottomBinding 	java.lang  !FragmentWarningBottomSheetBinding 	java.lang  GridLayoutManager 	java.lang  GsonConverterFactory 	java.lang  Handler 	java.lang  HomeFragment 	java.lang  Html 	java.lang  HttpLoggingInterceptor 	java.lang  Intent 	java.lang  ItemDashboardMenuBinding 	java.lang  ItemDisputeListBinding 	java.lang  ItemSpamMailBinding 	java.lang  LAYOUT_INFLATER_SERVICE 	java.lang  LayoutInflater 	java.lang  LayoutProgressBinding 	java.lang  LayoutProgressDialogBinding 	java.lang  LayoutSpamDetailBinding 	java.lang  LayoutValidationBinding 	java.lang  Locale 	java.lang  Log 	java.lang  Looper 	java.lang  Manifest 	java.lang  
MemoryInfo 	java.lang  
MultipartBody 	java.lang  NetworkCapabilities 	java.lang  NetworkInfo 	java.lang  NetworkInterface 	java.lang  NewGmailAccessibilityService 	java.lang  ObjectAnimator 	java.lang  OkHttpClient 	java.lang  PackageManager 	java.lang  PendingMailRequest 	java.lang  PieData 	java.lang  
PieDataSet 	java.lang  PieEntry 	java.lang  PixelFormat 	java.lang  R 	java.lang  RaiseBottomSheetFragment 	java.lang  Regex 	java.lang  Request 	java.lang  RequestBody 	java.lang  Retrofit 	java.lang  Runtime 	java.lang  
ScreenInfo 	java.lang  SecurityException 	java.lang  Settings 	java.lang  SharedPrefManager 	java.lang  SimpleDateFormat 	java.lang  Snackbar 	java.lang  SpamMailAdapter 	java.lang  SpamMailFragment 	java.lang  StorageInfo 	java.lang  
StringBuilder 	java.lang  SuggetionBottomFragment 	java.lang  System 	java.lang  
SystemInfo 	java.lang  TAG 	java.lang  
TelephonyInfo 	java.lang  TelephonyManager 	java.lang  	TextUtils 	java.lang  Toast 	java.lang  Uri 	java.lang  View 	java.lang  
ViewCompat 	java.lang  WINDOW_SERVICE 	java.lang  WarningBottomSheetFragment 	java.lang  Window 	java.lang  WindowInsetsCompat 	java.lang  
WindowManager 	java.lang  also 	java.lang  android 	java.lang  androidx 	java.lang  
appendLine 	java.lang  applicationContext 	java.lang  apply 	java.lang  arrayListOf 	java.lang  arrayOf 	java.lang  binding 	java.lang  buildString 	java.lang  checkAiResponse 	java.lang  com 	java.lang  contains 	java.lang  deviceInfoHelper 	java.lang  displayDeviceInfo 	java.lang  endsWith 	java.lang  equals 	java.lang  
extractEmails 	java.lang  filter 	java.lang  
filterIndexed 	java.lang  first 	java.lang  firstOrNull 	java.lang  format 	java.lang  formatBytes 	java.lang  	getOrNull 	java.lang  getValue 	java.lang  invoke 	java.lang  isBlank 	java.lang  
isNotEmpty 	java.lang  
isNullOrBlank 	java.lang  
isNullOrEmpty 	java.lang  iterator 	java.lang  java 	java.lang  joinToString 	java.lang  launch 	java.lang  lazy 	java.lang  let 	java.lang  listOf 	java.lang  	lowercase 	java.lang  map 	java.lang  mapOf 	java.lang  matches 	java.lang  
mutableListOf 	java.lang  mutableMapOf 	java.lang  mutableSetOf 	java.lang  provideDelegate 	java.lang  removePrefix 	java.lang  repeat 	java.lang  requireActivity 	java.lang  requireContext 	java.lang  set 	java.lang  setFormData 	java.lang  setResponsePopup 	java.lang  sharedPrefManager 	java.lang  showSnackbar 	java.lang  split 	java.lang  
startsWith 	java.lang  takeIf 	java.lang  to 	java.lang  toByteArray 	java.lang  toList 	java.lang  toMediaType 	java.lang  toMediaTypeOrNull 	java.lang  toRegex 	java.lang  
toRequestBody 	java.lang  toString 	java.lang  toTypedArray 	java.lang  trim 	java.lang  
trimIndent 	java.lang  until 	java.lang  
uploadFile 	java.lang  use 	java.lang  withContext 	java.lang  getLOCALIZEDMessage java.lang.Exception  getLocalizedMessage java.lang.Exception  localizedMessage java.lang.Exception  message java.lang.Exception  printStackTrace java.lang.Exception  setLocalizedMessage java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  
freeMemory java.lang.Runtime  
getRuntime java.lang.Runtime  	maxMemory java.lang.Runtime  totalMemory java.lang.Runtime  append java.lang.StringBuilder  
appendLine java.lang.StringBuilder  formatBytes java.lang.StringBuilder  
getAPPENDLine java.lang.StringBuilder  
getAppendLine java.lang.StringBuilder  getFORMATBytes java.lang.StringBuilder  getFormatBytes java.lang.StringBuilder  
getISNotEmpty java.lang.StringBuilder  
getIsNotEmpty java.lang.StringBuilder  	getREPEAT java.lang.StringBuilder  	getRepeat java.lang.StringBuilder  
isNotEmpty java.lang.StringBuilder  repeat java.lang.StringBuilder  toString java.lang.StringBuilder  getProperty java.lang.System  InetAddress java.net  NetworkInterface java.net  getHOSTAddress java.net.InetAddress  getHostAddress java.net.InetAddress  getISLoopbackAddress java.net.InetAddress  getIsLoopbackAddress java.net.InetAddress  hostAddress java.net.InetAddress  isLoopbackAddress java.net.InetAddress  setHostAddress java.net.InetAddress  setLoopbackAddress java.net.InetAddress  getINETAddresses java.net.NetworkInterface  getInetAddresses java.net.NetworkInterface  getNetworkInterfaces java.net.NetworkInterface  
inetAddresses java.net.NetworkInterface  setInetAddresses java.net.NetworkInterface  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  	ArrayList 	java.util  Date 	java.util  Enumeration 	java.util  Locale 	java.util  toString java.util.AbstractCollection  toString java.util.AbstractList  toString java.util.ArrayList  getITERATOR java.util.Enumeration  getIterator java.util.Enumeration  iterator java.util.Enumeration  
getDefault java.util.Locale  AccessibilityEvent kotlin  ActivityCompat kotlin  ActivityDashboardBinding kotlin  ActivityGraphFragment kotlin  ActivityMainBinding kotlin  ActivityManager kotlin  ActivityYahooAuthBinding kotlin  AlertDialog kotlin  Any kotlin  	ApiClient kotlin  
ApiService kotlin  AppConstant kotlin  AppInfo kotlin  Array kotlin  	ArrayList kotlin  BatteryInfo kotlin  BatteryManager kotlin  Boolean kotlin  BufferedReader kotlin  Build kotlin  	ByteArray kotlin  Char kotlin  CharSequence kotlin  Class kotlin  Color kotlin  
ColorTemplate kotlin  
CommonRequest kotlin  
CommonUtil kotlin  
ComponentName kotlin  Context kotlin  
ContextCompat kotlin  DashboardMenuAdapter kotlin  DashboardMenuModel kotlin  Date kotlin  DeviceDetailsFragment kotlin  
DeviceInfo kotlin  DeviceInfoHelper kotlin  Dialog kotlin  Dispatchers kotlin  DisplayMetrics kotlin  DisputeListAdapter kotlin  DisputeMaillistFragment kotlin  DisputeRaiseRequest kotlin  Double kotlin  	Exception kotlin  File kotlin  FileOutputStream kotlin  
FileReader kotlin  FirebaseAuth kotlin  Float kotlin  FragmentActivityGraphBinding kotlin  FragmentDeviceDetailsBinding kotlin  FragmentDisputeMaillistBinding kotlin  FragmentHomeBinding kotlin  FragmentRaiseBottomSheetBinding kotlin  FragmentSpamMailBinding kotlin  FragmentSuggetionBottomBinding kotlin  !FragmentWarningBottomSheetBinding kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  GridLayoutManager kotlin  GsonConverterFactory kotlin  Handler kotlin  HomeFragment kotlin  Html kotlin  HttpLoggingInterceptor kotlin  Int kotlin  IntArray kotlin  Intent kotlin  ItemDashboardMenuBinding kotlin  ItemDisputeListBinding kotlin  ItemSpamMailBinding kotlin  LAYOUT_INFLATER_SERVICE kotlin  LayoutInflater kotlin  LayoutProgressBinding kotlin  LayoutProgressDialogBinding kotlin  LayoutSpamDetailBinding kotlin  LayoutValidationBinding kotlin  Lazy kotlin  Locale kotlin  Log kotlin  Long kotlin  Looper kotlin  Manifest kotlin  
MemoryInfo kotlin  
MultipartBody kotlin  NetworkCapabilities kotlin  NetworkInfo kotlin  NetworkInterface kotlin  NewGmailAccessibilityService kotlin  Nothing kotlin  ObjectAnimator kotlin  OkHttpClient kotlin  PackageManager kotlin  Pair kotlin  PendingMailRequest kotlin  PieData kotlin  
PieDataSet kotlin  PieEntry kotlin  PixelFormat kotlin  R kotlin  RaiseBottomSheetFragment kotlin  Regex kotlin  Request kotlin  RequestBody kotlin  Retrofit kotlin  Runtime kotlin  
ScreenInfo kotlin  SecurityException kotlin  Settings kotlin  SharedPrefManager kotlin  SimpleDateFormat kotlin  Snackbar kotlin  SpamMailAdapter kotlin  SpamMailFragment kotlin  StorageInfo kotlin  String kotlin  
StringBuilder kotlin  SuggetionBottomFragment kotlin  Suppress kotlin  System kotlin  
SystemInfo kotlin  TAG kotlin  
TelephonyInfo kotlin  TelephonyManager kotlin  	TextUtils kotlin  	Throwable kotlin  Toast kotlin  Unit kotlin  Uri kotlin  View kotlin  
ViewCompat kotlin  WINDOW_SERVICE kotlin  WarningBottomSheetFragment kotlin  Window kotlin  WindowInsetsCompat kotlin  
WindowManager kotlin  also kotlin  android kotlin  androidx kotlin  
appendLine kotlin  applicationContext kotlin  apply kotlin  arrayListOf kotlin  arrayOf kotlin  binding kotlin  buildString kotlin  checkAiResponse kotlin  com kotlin  contains kotlin  deviceInfoHelper kotlin  displayDeviceInfo kotlin  endsWith kotlin  equals kotlin  
extractEmails kotlin  filter kotlin  
filterIndexed kotlin  first kotlin  firstOrNull kotlin  format kotlin  formatBytes kotlin  	getOrNull kotlin  getValue kotlin  invoke kotlin  isBlank kotlin  
isNotEmpty kotlin  
isNullOrBlank kotlin  
isNullOrEmpty kotlin  iterator kotlin  java kotlin  joinToString kotlin  launch kotlin  lazy kotlin  let kotlin  listOf kotlin  	lowercase kotlin  map kotlin  mapOf kotlin  matches kotlin  
mutableListOf kotlin  mutableMapOf kotlin  mutableSetOf kotlin  provideDelegate kotlin  removePrefix kotlin  repeat kotlin  requireActivity kotlin  requireContext kotlin  set kotlin  setFormData kotlin  setResponsePopup kotlin  sharedPrefManager kotlin  showSnackbar kotlin  split kotlin  
startsWith kotlin  takeIf kotlin  to kotlin  toByteArray kotlin  toList kotlin  toMediaType kotlin  toMediaTypeOrNull kotlin  toRegex kotlin  
toRequestBody kotlin  toString kotlin  toTypedArray kotlin  trim kotlin  
trimIndent kotlin  until kotlin  
uploadFile kotlin  use kotlin  withContext kotlin  	getFILTER kotlin.Array  getFILTERIndexed kotlin.Array  	getFilter kotlin.Array  getFilterIndexed kotlin.Array  getTOString kotlin.Float  getToString kotlin.Float  getTOString 
kotlin.Int  getToString 
kotlin.Int  getUNTIL 
kotlin.Int  getUntil 
kotlin.Int  	getTOList kotlin.IntArray  	getToList kotlin.IntArray  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getALSO 
kotlin.String  getAlso 
kotlin.String  getCONTAINS 
kotlin.String  getContains 
kotlin.String  getENDSWith 
kotlin.String  	getEQUALS 
kotlin.String  getEXTRACTEmail 
kotlin.String  getEXTRACTEmails 
kotlin.String  getEndsWith 
kotlin.String  	getEquals 
kotlin.String  getExtractEmail 
kotlin.String  getExtractEmails 
kotlin.String  getFIRSTOrNull 
kotlin.String  	getFORMAT 
kotlin.String  getFirstOrNull 
kotlin.String  	getFormat 
kotlin.String  
getISBlank 
kotlin.String  
getISNotEmpty 
kotlin.String  getISNullOrEmpty 
kotlin.String  getISValidEmail 
kotlin.String  
getIsBlank 
kotlin.String  
getIsNotEmpty 
kotlin.String  getIsNullOrEmpty 
kotlin.String  getIsValidEmail 
kotlin.String  getLET 
kotlin.String  getLOWERCASE 
kotlin.String  getLet 
kotlin.String  getLowercase 
kotlin.String  getMAP 
kotlin.String  
getMATCHES 
kotlin.String  getMap 
kotlin.String  
getMatches 
kotlin.String  getREMOVEPrefix 
kotlin.String  	getREPEAT 
kotlin.String  getRemovePrefix 
kotlin.String  	getRepeat 
kotlin.String  getSPLIT 
kotlin.String  
getSTARTSWith 
kotlin.String  getSplit 
kotlin.String  
getStartsWith 
kotlin.String  getTO 
kotlin.String  getTOByteArray 
kotlin.String  	getTOList 
kotlin.String  getTOMediaType 
kotlin.String  getTOMediaTypeOrNull 
kotlin.String  
getTORegex 
kotlin.String  getTORequestBody 
kotlin.String  getTOString 
kotlin.String  getTRIM 
kotlin.String  
getTRIMIndent 
kotlin.String  getTo 
kotlin.String  getToByteArray 
kotlin.String  	getToList 
kotlin.String  getToMediaType 
kotlin.String  getToMediaTypeOrNull 
kotlin.String  
getToRegex 
kotlin.String  getToRequestBody 
kotlin.String  getToString 
kotlin.String  getTrim 
kotlin.String  
getTrimIndent 
kotlin.String  isBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  isValidEmail 
kotlin.String  AccessibilityEvent kotlin.annotation  ActivityCompat kotlin.annotation  ActivityDashboardBinding kotlin.annotation  ActivityGraphFragment kotlin.annotation  ActivityMainBinding kotlin.annotation  ActivityManager kotlin.annotation  ActivityYahooAuthBinding kotlin.annotation  AlertDialog kotlin.annotation  	ApiClient kotlin.annotation  
ApiService kotlin.annotation  AppConstant kotlin.annotation  AppInfo kotlin.annotation  	ArrayList kotlin.annotation  BatteryInfo kotlin.annotation  BatteryManager kotlin.annotation  BufferedReader kotlin.annotation  Build kotlin.annotation  Class kotlin.annotation  Color kotlin.annotation  
ColorTemplate kotlin.annotation  
CommonRequest kotlin.annotation  
CommonUtil kotlin.annotation  
ComponentName kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  DashboardMenuAdapter kotlin.annotation  DashboardMenuModel kotlin.annotation  Date kotlin.annotation  DeviceDetailsFragment kotlin.annotation  
DeviceInfo kotlin.annotation  DeviceInfoHelper kotlin.annotation  Dialog kotlin.annotation  Dispatchers kotlin.annotation  DisplayMetrics kotlin.annotation  DisputeListAdapter kotlin.annotation  DisputeMaillistFragment kotlin.annotation  DisputeRaiseRequest kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  FileOutputStream kotlin.annotation  
FileReader kotlin.annotation  FirebaseAuth kotlin.annotation  FragmentActivityGraphBinding kotlin.annotation  FragmentDeviceDetailsBinding kotlin.annotation  FragmentDisputeMaillistBinding kotlin.annotation  FragmentHomeBinding kotlin.annotation  FragmentRaiseBottomSheetBinding kotlin.annotation  FragmentSpamMailBinding kotlin.annotation  FragmentSuggetionBottomBinding kotlin.annotation  !FragmentWarningBottomSheetBinding kotlin.annotation  GridLayoutManager kotlin.annotation  GsonConverterFactory kotlin.annotation  Handler kotlin.annotation  HomeFragment kotlin.annotation  Html kotlin.annotation  HttpLoggingInterceptor kotlin.annotation  Intent kotlin.annotation  ItemDashboardMenuBinding kotlin.annotation  ItemDisputeListBinding kotlin.annotation  ItemSpamMailBinding kotlin.annotation  LAYOUT_INFLATER_SERVICE kotlin.annotation  LayoutInflater kotlin.annotation  LayoutProgressBinding kotlin.annotation  LayoutProgressDialogBinding kotlin.annotation  LayoutSpamDetailBinding kotlin.annotation  LayoutValidationBinding kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  Manifest kotlin.annotation  
MemoryInfo kotlin.annotation  
MultipartBody kotlin.annotation  NetworkCapabilities kotlin.annotation  NetworkInfo kotlin.annotation  NetworkInterface kotlin.annotation  NewGmailAccessibilityService kotlin.annotation  ObjectAnimator kotlin.annotation  OkHttpClient kotlin.annotation  PackageManager kotlin.annotation  PendingMailRequest kotlin.annotation  PieData kotlin.annotation  
PieDataSet kotlin.annotation  PieEntry kotlin.annotation  PixelFormat kotlin.annotation  R kotlin.annotation  RaiseBottomSheetFragment kotlin.annotation  Regex kotlin.annotation  Request kotlin.annotation  RequestBody kotlin.annotation  Retrofit kotlin.annotation  Runtime kotlin.annotation  
ScreenInfo kotlin.annotation  SecurityException kotlin.annotation  Settings kotlin.annotation  SharedPrefManager kotlin.annotation  SimpleDateFormat kotlin.annotation  Snackbar kotlin.annotation  SpamMailAdapter kotlin.annotation  SpamMailFragment kotlin.annotation  StorageInfo kotlin.annotation  
StringBuilder kotlin.annotation  SuggetionBottomFragment kotlin.annotation  System kotlin.annotation  
SystemInfo kotlin.annotation  TAG kotlin.annotation  
TelephonyInfo kotlin.annotation  TelephonyManager kotlin.annotation  	TextUtils kotlin.annotation  Toast kotlin.annotation  Uri kotlin.annotation  View kotlin.annotation  
ViewCompat kotlin.annotation  WINDOW_SERVICE kotlin.annotation  WarningBottomSheetFragment kotlin.annotation  Window kotlin.annotation  WindowInsetsCompat kotlin.annotation  
WindowManager kotlin.annotation  also kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  
appendLine kotlin.annotation  applicationContext kotlin.annotation  apply kotlin.annotation  arrayListOf kotlin.annotation  arrayOf kotlin.annotation  binding kotlin.annotation  buildString kotlin.annotation  checkAiResponse kotlin.annotation  com kotlin.annotation  contains kotlin.annotation  deviceInfoHelper kotlin.annotation  displayDeviceInfo kotlin.annotation  endsWith kotlin.annotation  equals kotlin.annotation  
extractEmails kotlin.annotation  filter kotlin.annotation  
filterIndexed kotlin.annotation  first kotlin.annotation  firstOrNull kotlin.annotation  format kotlin.annotation  formatBytes kotlin.annotation  	getOrNull kotlin.annotation  getValue kotlin.annotation  invoke kotlin.annotation  isBlank kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrBlank kotlin.annotation  
isNullOrEmpty kotlin.annotation  iterator kotlin.annotation  java kotlin.annotation  joinToString kotlin.annotation  launch kotlin.annotation  lazy kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  	lowercase kotlin.annotation  map kotlin.annotation  mapOf kotlin.annotation  matches kotlin.annotation  
mutableListOf kotlin.annotation  mutableMapOf kotlin.annotation  mutableSetOf kotlin.annotation  provideDelegate kotlin.annotation  removePrefix kotlin.annotation  repeat kotlin.annotation  requireActivity kotlin.annotation  requireContext kotlin.annotation  set kotlin.annotation  setFormData kotlin.annotation  setResponsePopup kotlin.annotation  sharedPrefManager kotlin.annotation  showSnackbar kotlin.annotation  split kotlin.annotation  
startsWith kotlin.annotation  takeIf kotlin.annotation  to kotlin.annotation  toByteArray kotlin.annotation  toList kotlin.annotation  toMediaType kotlin.annotation  toMediaTypeOrNull kotlin.annotation  toRegex kotlin.annotation  
toRequestBody kotlin.annotation  toString kotlin.annotation  toTypedArray kotlin.annotation  trim kotlin.annotation  
trimIndent kotlin.annotation  until kotlin.annotation  
uploadFile kotlin.annotation  use kotlin.annotation  withContext kotlin.annotation  AccessibilityEvent kotlin.collections  ActivityCompat kotlin.collections  ActivityDashboardBinding kotlin.collections  ActivityGraphFragment kotlin.collections  ActivityMainBinding kotlin.collections  ActivityManager kotlin.collections  ActivityYahooAuthBinding kotlin.collections  AlertDialog kotlin.collections  	ApiClient kotlin.collections  
ApiService kotlin.collections  AppConstant kotlin.collections  AppInfo kotlin.collections  	ArrayList kotlin.collections  BatteryInfo kotlin.collections  BatteryManager kotlin.collections  BufferedReader kotlin.collections  Build kotlin.collections  Class kotlin.collections  Color kotlin.collections  
ColorTemplate kotlin.collections  
CommonRequest kotlin.collections  
CommonUtil kotlin.collections  
ComponentName kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  DashboardMenuAdapter kotlin.collections  DashboardMenuModel kotlin.collections  Date kotlin.collections  DeviceDetailsFragment kotlin.collections  
DeviceInfo kotlin.collections  DeviceInfoHelper kotlin.collections  Dialog kotlin.collections  Dispatchers kotlin.collections  DisplayMetrics kotlin.collections  DisputeListAdapter kotlin.collections  DisputeMaillistFragment kotlin.collections  DisputeRaiseRequest kotlin.collections  	Exception kotlin.collections  File kotlin.collections  FileOutputStream kotlin.collections  
FileReader kotlin.collections  FirebaseAuth kotlin.collections  FragmentActivityGraphBinding kotlin.collections  FragmentDeviceDetailsBinding kotlin.collections  FragmentDisputeMaillistBinding kotlin.collections  FragmentHomeBinding kotlin.collections  FragmentRaiseBottomSheetBinding kotlin.collections  FragmentSpamMailBinding kotlin.collections  FragmentSuggetionBottomBinding kotlin.collections  !FragmentWarningBottomSheetBinding kotlin.collections  GridLayoutManager kotlin.collections  GsonConverterFactory kotlin.collections  Handler kotlin.collections  HomeFragment kotlin.collections  Html kotlin.collections  HttpLoggingInterceptor kotlin.collections  Intent kotlin.collections  ItemDashboardMenuBinding kotlin.collections  ItemDisputeListBinding kotlin.collections  ItemSpamMailBinding kotlin.collections  LAYOUT_INFLATER_SERVICE kotlin.collections  LayoutInflater kotlin.collections  LayoutProgressBinding kotlin.collections  LayoutProgressDialogBinding kotlin.collections  LayoutSpamDetailBinding kotlin.collections  LayoutValidationBinding kotlin.collections  List kotlin.collections  Locale kotlin.collections  Log kotlin.collections  Looper kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  
MemoryInfo kotlin.collections  
MultipartBody kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  NetworkCapabilities kotlin.collections  NetworkInfo kotlin.collections  NetworkInterface kotlin.collections  NewGmailAccessibilityService kotlin.collections  ObjectAnimator kotlin.collections  OkHttpClient kotlin.collections  PackageManager kotlin.collections  PendingMailRequest kotlin.collections  PieData kotlin.collections  
PieDataSet kotlin.collections  PieEntry kotlin.collections  PixelFormat kotlin.collections  R kotlin.collections  RaiseBottomSheetFragment kotlin.collections  Regex kotlin.collections  Request kotlin.collections  RequestBody kotlin.collections  Retrofit kotlin.collections  Runtime kotlin.collections  
ScreenInfo kotlin.collections  SecurityException kotlin.collections  Settings kotlin.collections  SharedPrefManager kotlin.collections  SimpleDateFormat kotlin.collections  Snackbar kotlin.collections  SpamMailAdapter kotlin.collections  SpamMailFragment kotlin.collections  StorageInfo kotlin.collections  
StringBuilder kotlin.collections  SuggetionBottomFragment kotlin.collections  System kotlin.collections  
SystemInfo kotlin.collections  TAG kotlin.collections  
TelephonyInfo kotlin.collections  TelephonyManager kotlin.collections  	TextUtils kotlin.collections  Toast kotlin.collections  Uri kotlin.collections  View kotlin.collections  
ViewCompat kotlin.collections  WINDOW_SERVICE kotlin.collections  WarningBottomSheetFragment kotlin.collections  Window kotlin.collections  WindowInsetsCompat kotlin.collections  
WindowManager kotlin.collections  also kotlin.collections  android kotlin.collections  androidx kotlin.collections  
appendLine kotlin.collections  applicationContext kotlin.collections  apply kotlin.collections  arrayListOf kotlin.collections  arrayOf kotlin.collections  binding kotlin.collections  buildString kotlin.collections  checkAiResponse kotlin.collections  com kotlin.collections  contains kotlin.collections  deviceInfoHelper kotlin.collections  displayDeviceInfo kotlin.collections  endsWith kotlin.collections  equals kotlin.collections  
extractEmails kotlin.collections  filter kotlin.collections  
filterIndexed kotlin.collections  first kotlin.collections  firstOrNull kotlin.collections  format kotlin.collections  formatBytes kotlin.collections  	getOrNull kotlin.collections  getValue kotlin.collections  invoke kotlin.collections  isBlank kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrBlank kotlin.collections  
isNullOrEmpty kotlin.collections  iterator kotlin.collections  java kotlin.collections  joinToString kotlin.collections  launch kotlin.collections  lazy kotlin.collections  let kotlin.collections  listOf kotlin.collections  	lowercase kotlin.collections  map kotlin.collections  mapOf kotlin.collections  matches kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  provideDelegate kotlin.collections  removePrefix kotlin.collections  repeat kotlin.collections  requireActivity kotlin.collections  requireContext kotlin.collections  set kotlin.collections  setFormData kotlin.collections  setResponsePopup kotlin.collections  sharedPrefManager kotlin.collections  showSnackbar kotlin.collections  split kotlin.collections  
startsWith kotlin.collections  takeIf kotlin.collections  to kotlin.collections  toByteArray kotlin.collections  toList kotlin.collections  toMediaType kotlin.collections  toMediaTypeOrNull kotlin.collections  toRegex kotlin.collections  
toRequestBody kotlin.collections  toString kotlin.collections  toTypedArray kotlin.collections  trim kotlin.collections  
trimIndent kotlin.collections  until kotlin.collections  
uploadFile kotlin.collections  use kotlin.collections  withContext kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  	getFILTER kotlin.collections.List  getFIRSTOrNull kotlin.collections.List  	getFilter kotlin.collections.List  getFirstOrNull kotlin.collections.List  getGETOrNull kotlin.collections.List  getGetOrNull kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  getTOTypedArray kotlin.collections.List  getToTypedArray kotlin.collections.List  
isNotEmpty kotlin.collections.List  Entry kotlin.collections.Map  getMAP kotlin.collections.Map  getMap kotlin.collections.Map  
getISNotEmpty kotlin.collections.MutableList  
getIsNotEmpty kotlin.collections.MutableList  getJOINToString kotlin.collections.MutableList  getJoinToString kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  getFIRST kotlin.collections.MutableSet  getFirst kotlin.collections.MutableSet  
getISNotEmpty kotlin.collections.MutableSet  
getIsNotEmpty kotlin.collections.MutableSet  getJOINToString kotlin.collections.MutableSet  getJoinToString kotlin.collections.MutableSet  	getTOList kotlin.collections.MutableSet  	getToList kotlin.collections.MutableSet  
isNotEmpty kotlin.collections.MutableSet  AccessibilityEvent kotlin.comparisons  ActivityCompat kotlin.comparisons  ActivityDashboardBinding kotlin.comparisons  ActivityGraphFragment kotlin.comparisons  ActivityMainBinding kotlin.comparisons  ActivityManager kotlin.comparisons  ActivityYahooAuthBinding kotlin.comparisons  AlertDialog kotlin.comparisons  	ApiClient kotlin.comparisons  
ApiService kotlin.comparisons  AppConstant kotlin.comparisons  AppInfo kotlin.comparisons  	ArrayList kotlin.comparisons  BatteryInfo kotlin.comparisons  BatteryManager kotlin.comparisons  BufferedReader kotlin.comparisons  Build kotlin.comparisons  Class kotlin.comparisons  Color kotlin.comparisons  
ColorTemplate kotlin.comparisons  
CommonRequest kotlin.comparisons  
CommonUtil kotlin.comparisons  
ComponentName kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  DashboardMenuAdapter kotlin.comparisons  DashboardMenuModel kotlin.comparisons  Date kotlin.comparisons  DeviceDetailsFragment kotlin.comparisons  
DeviceInfo kotlin.comparisons  DeviceInfoHelper kotlin.comparisons  Dialog kotlin.comparisons  Dispatchers kotlin.comparisons  DisplayMetrics kotlin.comparisons  DisputeListAdapter kotlin.comparisons  DisputeMaillistFragment kotlin.comparisons  DisputeRaiseRequest kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  FileOutputStream kotlin.comparisons  
FileReader kotlin.comparisons  FirebaseAuth kotlin.comparisons  FragmentActivityGraphBinding kotlin.comparisons  FragmentDeviceDetailsBinding kotlin.comparisons  FragmentDisputeMaillistBinding kotlin.comparisons  FragmentHomeBinding kotlin.comparisons  FragmentRaiseBottomSheetBinding kotlin.comparisons  FragmentSpamMailBinding kotlin.comparisons  FragmentSuggetionBottomBinding kotlin.comparisons  !FragmentWarningBottomSheetBinding kotlin.comparisons  GridLayoutManager kotlin.comparisons  GsonConverterFactory kotlin.comparisons  Handler kotlin.comparisons  HomeFragment kotlin.comparisons  Html kotlin.comparisons  HttpLoggingInterceptor kotlin.comparisons  Intent kotlin.comparisons  ItemDashboardMenuBinding kotlin.comparisons  ItemDisputeListBinding kotlin.comparisons  ItemSpamMailBinding kotlin.comparisons  LAYOUT_INFLATER_SERVICE kotlin.comparisons  LayoutInflater kotlin.comparisons  LayoutProgressBinding kotlin.comparisons  LayoutProgressDialogBinding kotlin.comparisons  LayoutSpamDetailBinding kotlin.comparisons  LayoutValidationBinding kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  Manifest kotlin.comparisons  
MemoryInfo kotlin.comparisons  
MultipartBody kotlin.comparisons  NetworkCapabilities kotlin.comparisons  NetworkInfo kotlin.comparisons  NetworkInterface kotlin.comparisons  NewGmailAccessibilityService kotlin.comparisons  ObjectAnimator kotlin.comparisons  OkHttpClient kotlin.comparisons  PackageManager kotlin.comparisons  PendingMailRequest kotlin.comparisons  PieData kotlin.comparisons  
PieDataSet kotlin.comparisons  PieEntry kotlin.comparisons  PixelFormat kotlin.comparisons  R kotlin.comparisons  RaiseBottomSheetFragment kotlin.comparisons  Regex kotlin.comparisons  Request kotlin.comparisons  RequestBody kotlin.comparisons  Retrofit kotlin.comparisons  Runtime kotlin.comparisons  
ScreenInfo kotlin.comparisons  SecurityException kotlin.comparisons  Settings kotlin.comparisons  SharedPrefManager kotlin.comparisons  SimpleDateFormat kotlin.comparisons  Snackbar kotlin.comparisons  SpamMailAdapter kotlin.comparisons  SpamMailFragment kotlin.comparisons  StorageInfo kotlin.comparisons  
StringBuilder kotlin.comparisons  SuggetionBottomFragment kotlin.comparisons  System kotlin.comparisons  
SystemInfo kotlin.comparisons  TAG kotlin.comparisons  
TelephonyInfo kotlin.comparisons  TelephonyManager kotlin.comparisons  	TextUtils kotlin.comparisons  Toast kotlin.comparisons  Uri kotlin.comparisons  View kotlin.comparisons  
ViewCompat kotlin.comparisons  WINDOW_SERVICE kotlin.comparisons  WarningBottomSheetFragment kotlin.comparisons  Window kotlin.comparisons  WindowInsetsCompat kotlin.comparisons  
WindowManager kotlin.comparisons  also kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  
appendLine kotlin.comparisons  applicationContext kotlin.comparisons  apply kotlin.comparisons  arrayListOf kotlin.comparisons  arrayOf kotlin.comparisons  binding kotlin.comparisons  buildString kotlin.comparisons  checkAiResponse kotlin.comparisons  com kotlin.comparisons  contains kotlin.comparisons  deviceInfoHelper kotlin.comparisons  displayDeviceInfo kotlin.comparisons  endsWith kotlin.comparisons  equals kotlin.comparisons  
extractEmails kotlin.comparisons  filter kotlin.comparisons  
filterIndexed kotlin.comparisons  first kotlin.comparisons  firstOrNull kotlin.comparisons  format kotlin.comparisons  formatBytes kotlin.comparisons  	getOrNull kotlin.comparisons  getValue kotlin.comparisons  invoke kotlin.comparisons  isBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrBlank kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  iterator kotlin.comparisons  java kotlin.comparisons  joinToString kotlin.comparisons  launch kotlin.comparisons  lazy kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  	lowercase kotlin.comparisons  map kotlin.comparisons  mapOf kotlin.comparisons  matches kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableMapOf kotlin.comparisons  mutableSetOf kotlin.comparisons  provideDelegate kotlin.comparisons  removePrefix kotlin.comparisons  repeat kotlin.comparisons  requireActivity kotlin.comparisons  requireContext kotlin.comparisons  set kotlin.comparisons  setFormData kotlin.comparisons  setResponsePopup kotlin.comparisons  sharedPrefManager kotlin.comparisons  showSnackbar kotlin.comparisons  split kotlin.comparisons  
startsWith kotlin.comparisons  takeIf kotlin.comparisons  to kotlin.comparisons  toByteArray kotlin.comparisons  toList kotlin.comparisons  toMediaType kotlin.comparisons  toMediaTypeOrNull kotlin.comparisons  toRegex kotlin.comparisons  
toRequestBody kotlin.comparisons  toString kotlin.comparisons  toTypedArray kotlin.comparisons  trim kotlin.comparisons  
trimIndent kotlin.comparisons  until kotlin.comparisons  
uploadFile kotlin.comparisons  use kotlin.comparisons  withContext kotlin.comparisons  SuspendFunction1 kotlin.coroutines  AccessibilityEvent 	kotlin.io  ActivityCompat 	kotlin.io  ActivityDashboardBinding 	kotlin.io  ActivityGraphFragment 	kotlin.io  ActivityMainBinding 	kotlin.io  ActivityManager 	kotlin.io  ActivityYahooAuthBinding 	kotlin.io  AlertDialog 	kotlin.io  	ApiClient 	kotlin.io  
ApiService 	kotlin.io  AppConstant 	kotlin.io  AppInfo 	kotlin.io  	ArrayList 	kotlin.io  BatteryInfo 	kotlin.io  BatteryManager 	kotlin.io  BufferedReader 	kotlin.io  Build 	kotlin.io  Class 	kotlin.io  Color 	kotlin.io  
ColorTemplate 	kotlin.io  
CommonRequest 	kotlin.io  
CommonUtil 	kotlin.io  
ComponentName 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  DashboardMenuAdapter 	kotlin.io  DashboardMenuModel 	kotlin.io  Date 	kotlin.io  DeviceDetailsFragment 	kotlin.io  
DeviceInfo 	kotlin.io  DeviceInfoHelper 	kotlin.io  Dialog 	kotlin.io  Dispatchers 	kotlin.io  DisplayMetrics 	kotlin.io  DisputeListAdapter 	kotlin.io  DisputeMaillistFragment 	kotlin.io  DisputeRaiseRequest 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  FileOutputStream 	kotlin.io  
FileReader 	kotlin.io  FirebaseAuth 	kotlin.io  FragmentActivityGraphBinding 	kotlin.io  FragmentDeviceDetailsBinding 	kotlin.io  FragmentDisputeMaillistBinding 	kotlin.io  FragmentHomeBinding 	kotlin.io  FragmentRaiseBottomSheetBinding 	kotlin.io  FragmentSpamMailBinding 	kotlin.io  FragmentSuggetionBottomBinding 	kotlin.io  !FragmentWarningBottomSheetBinding 	kotlin.io  GridLayoutManager 	kotlin.io  GsonConverterFactory 	kotlin.io  Handler 	kotlin.io  HomeFragment 	kotlin.io  Html 	kotlin.io  HttpLoggingInterceptor 	kotlin.io  Intent 	kotlin.io  ItemDashboardMenuBinding 	kotlin.io  ItemDisputeListBinding 	kotlin.io  ItemSpamMailBinding 	kotlin.io  LAYOUT_INFLATER_SERVICE 	kotlin.io  LayoutInflater 	kotlin.io  LayoutProgressBinding 	kotlin.io  LayoutProgressDialogBinding 	kotlin.io  LayoutSpamDetailBinding 	kotlin.io  LayoutValidationBinding 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  Manifest 	kotlin.io  
MemoryInfo 	kotlin.io  
MultipartBody 	kotlin.io  NetworkCapabilities 	kotlin.io  NetworkInfo 	kotlin.io  NetworkInterface 	kotlin.io  NewGmailAccessibilityService 	kotlin.io  ObjectAnimator 	kotlin.io  OkHttpClient 	kotlin.io  PackageManager 	kotlin.io  PendingMailRequest 	kotlin.io  PieData 	kotlin.io  
PieDataSet 	kotlin.io  PieEntry 	kotlin.io  PixelFormat 	kotlin.io  R 	kotlin.io  RaiseBottomSheetFragment 	kotlin.io  Regex 	kotlin.io  Request 	kotlin.io  RequestBody 	kotlin.io  Retrofit 	kotlin.io  Runtime 	kotlin.io  
ScreenInfo 	kotlin.io  SecurityException 	kotlin.io  Settings 	kotlin.io  SharedPrefManager 	kotlin.io  SimpleDateFormat 	kotlin.io  Snackbar 	kotlin.io  SpamMailAdapter 	kotlin.io  SpamMailFragment 	kotlin.io  StorageInfo 	kotlin.io  
StringBuilder 	kotlin.io  SuggetionBottomFragment 	kotlin.io  System 	kotlin.io  
SystemInfo 	kotlin.io  TAG 	kotlin.io  
TelephonyInfo 	kotlin.io  TelephonyManager 	kotlin.io  	TextUtils 	kotlin.io  Toast 	kotlin.io  Uri 	kotlin.io  View 	kotlin.io  
ViewCompat 	kotlin.io  WINDOW_SERVICE 	kotlin.io  WarningBottomSheetFragment 	kotlin.io  Window 	kotlin.io  WindowInsetsCompat 	kotlin.io  
WindowManager 	kotlin.io  also 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  
appendLine 	kotlin.io  applicationContext 	kotlin.io  apply 	kotlin.io  arrayListOf 	kotlin.io  arrayOf 	kotlin.io  binding 	kotlin.io  buildString 	kotlin.io  checkAiResponse 	kotlin.io  com 	kotlin.io  contains 	kotlin.io  deviceInfoHelper 	kotlin.io  displayDeviceInfo 	kotlin.io  endsWith 	kotlin.io  equals 	kotlin.io  
extractEmails 	kotlin.io  filter 	kotlin.io  
filterIndexed 	kotlin.io  first 	kotlin.io  firstOrNull 	kotlin.io  format 	kotlin.io  formatBytes 	kotlin.io  	getOrNull 	kotlin.io  getValue 	kotlin.io  invoke 	kotlin.io  isBlank 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrBlank 	kotlin.io  
isNullOrEmpty 	kotlin.io  iterator 	kotlin.io  java 	kotlin.io  joinToString 	kotlin.io  launch 	kotlin.io  lazy 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  	lowercase 	kotlin.io  map 	kotlin.io  mapOf 	kotlin.io  matches 	kotlin.io  
mutableListOf 	kotlin.io  mutableMapOf 	kotlin.io  mutableSetOf 	kotlin.io  provideDelegate 	kotlin.io  removePrefix 	kotlin.io  repeat 	kotlin.io  requireActivity 	kotlin.io  requireContext 	kotlin.io  set 	kotlin.io  setFormData 	kotlin.io  setResponsePopup 	kotlin.io  sharedPrefManager 	kotlin.io  showSnackbar 	kotlin.io  split 	kotlin.io  
startsWith 	kotlin.io  takeIf 	kotlin.io  to 	kotlin.io  toByteArray 	kotlin.io  toList 	kotlin.io  toMediaType 	kotlin.io  toMediaTypeOrNull 	kotlin.io  toRegex 	kotlin.io  
toRequestBody 	kotlin.io  toString 	kotlin.io  toTypedArray 	kotlin.io  trim 	kotlin.io  
trimIndent 	kotlin.io  until 	kotlin.io  
uploadFile 	kotlin.io  use 	kotlin.io  withContext 	kotlin.io  AccessibilityEvent 
kotlin.jvm  ActivityCompat 
kotlin.jvm  ActivityDashboardBinding 
kotlin.jvm  ActivityGraphFragment 
kotlin.jvm  ActivityMainBinding 
kotlin.jvm  ActivityManager 
kotlin.jvm  ActivityYahooAuthBinding 
kotlin.jvm  AlertDialog 
kotlin.jvm  	ApiClient 
kotlin.jvm  
ApiService 
kotlin.jvm  AppConstant 
kotlin.jvm  AppInfo 
kotlin.jvm  	ArrayList 
kotlin.jvm  BatteryInfo 
kotlin.jvm  BatteryManager 
kotlin.jvm  BufferedReader 
kotlin.jvm  Build 
kotlin.jvm  Class 
kotlin.jvm  Color 
kotlin.jvm  
ColorTemplate 
kotlin.jvm  
CommonRequest 
kotlin.jvm  
CommonUtil 
kotlin.jvm  
ComponentName 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  DashboardMenuAdapter 
kotlin.jvm  DashboardMenuModel 
kotlin.jvm  Date 
kotlin.jvm  DeviceDetailsFragment 
kotlin.jvm  
DeviceInfo 
kotlin.jvm  DeviceInfoHelper 
kotlin.jvm  Dialog 
kotlin.jvm  Dispatchers 
kotlin.jvm  DisplayMetrics 
kotlin.jvm  DisputeListAdapter 
kotlin.jvm  DisputeMaillistFragment 
kotlin.jvm  DisputeRaiseRequest 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  FileOutputStream 
kotlin.jvm  
FileReader 
kotlin.jvm  FirebaseAuth 
kotlin.jvm  FragmentActivityGraphBinding 
kotlin.jvm  FragmentDeviceDetailsBinding 
kotlin.jvm  FragmentDisputeMaillistBinding 
kotlin.jvm  FragmentHomeBinding 
kotlin.jvm  FragmentRaiseBottomSheetBinding 
kotlin.jvm  FragmentSpamMailBinding 
kotlin.jvm  FragmentSuggetionBottomBinding 
kotlin.jvm  !FragmentWarningBottomSheetBinding 
kotlin.jvm  GridLayoutManager 
kotlin.jvm  GsonConverterFactory 
kotlin.jvm  Handler 
kotlin.jvm  HomeFragment 
kotlin.jvm  Html 
kotlin.jvm  HttpLoggingInterceptor 
kotlin.jvm  Intent 
kotlin.jvm  ItemDashboardMenuBinding 
kotlin.jvm  ItemDisputeListBinding 
kotlin.jvm  ItemSpamMailBinding 
kotlin.jvm  LAYOUT_INFLATER_SERVICE 
kotlin.jvm  LayoutInflater 
kotlin.jvm  LayoutProgressBinding 
kotlin.jvm  LayoutProgressDialogBinding 
kotlin.jvm  LayoutSpamDetailBinding 
kotlin.jvm  LayoutValidationBinding 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  Manifest 
kotlin.jvm  
MemoryInfo 
kotlin.jvm  
MultipartBody 
kotlin.jvm  NetworkCapabilities 
kotlin.jvm  NetworkInfo 
kotlin.jvm  NetworkInterface 
kotlin.jvm  NewGmailAccessibilityService 
kotlin.jvm  ObjectAnimator 
kotlin.jvm  OkHttpClient 
kotlin.jvm  PackageManager 
kotlin.jvm  PendingMailRequest 
kotlin.jvm  PieData 
kotlin.jvm  
PieDataSet 
kotlin.jvm  PieEntry 
kotlin.jvm  PixelFormat 
kotlin.jvm  R 
kotlin.jvm  RaiseBottomSheetFragment 
kotlin.jvm  Regex 
kotlin.jvm  Request 
kotlin.jvm  RequestBody 
kotlin.jvm  Retrofit 
kotlin.jvm  Runtime 
kotlin.jvm  
ScreenInfo 
kotlin.jvm  SecurityException 
kotlin.jvm  Settings 
kotlin.jvm  SharedPrefManager 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  Snackbar 
kotlin.jvm  SpamMailAdapter 
kotlin.jvm  SpamMailFragment 
kotlin.jvm  StorageInfo 
kotlin.jvm  
StringBuilder 
kotlin.jvm  SuggetionBottomFragment 
kotlin.jvm  System 
kotlin.jvm  
SystemInfo 
kotlin.jvm  TAG 
kotlin.jvm  
TelephonyInfo 
kotlin.jvm  TelephonyManager 
kotlin.jvm  	TextUtils 
kotlin.jvm  Toast 
kotlin.jvm  Uri 
kotlin.jvm  View 
kotlin.jvm  
ViewCompat 
kotlin.jvm  WINDOW_SERVICE 
kotlin.jvm  WarningBottomSheetFragment 
kotlin.jvm  Window 
kotlin.jvm  WindowInsetsCompat 
kotlin.jvm  
WindowManager 
kotlin.jvm  also 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  
appendLine 
kotlin.jvm  applicationContext 
kotlin.jvm  apply 
kotlin.jvm  arrayListOf 
kotlin.jvm  arrayOf 
kotlin.jvm  binding 
kotlin.jvm  buildString 
kotlin.jvm  checkAiResponse 
kotlin.jvm  com 
kotlin.jvm  contains 
kotlin.jvm  deviceInfoHelper 
kotlin.jvm  displayDeviceInfo 
kotlin.jvm  endsWith 
kotlin.jvm  equals 
kotlin.jvm  
extractEmails 
kotlin.jvm  filter 
kotlin.jvm  
filterIndexed 
kotlin.jvm  first 
kotlin.jvm  firstOrNull 
kotlin.jvm  format 
kotlin.jvm  formatBytes 
kotlin.jvm  	getOrNull 
kotlin.jvm  getValue 
kotlin.jvm  invoke 
kotlin.jvm  isBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrBlank 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  iterator 
kotlin.jvm  java 
kotlin.jvm  joinToString 
kotlin.jvm  launch 
kotlin.jvm  lazy 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  	lowercase 
kotlin.jvm  map 
kotlin.jvm  mapOf 
kotlin.jvm  matches 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  mutableSetOf 
kotlin.jvm  provideDelegate 
kotlin.jvm  removePrefix 
kotlin.jvm  repeat 
kotlin.jvm  requireActivity 
kotlin.jvm  requireContext 
kotlin.jvm  set 
kotlin.jvm  setFormData 
kotlin.jvm  setResponsePopup 
kotlin.jvm  sharedPrefManager 
kotlin.jvm  showSnackbar 
kotlin.jvm  split 
kotlin.jvm  
startsWith 
kotlin.jvm  takeIf 
kotlin.jvm  to 
kotlin.jvm  toByteArray 
kotlin.jvm  toList 
kotlin.jvm  toMediaType 
kotlin.jvm  toMediaTypeOrNull 
kotlin.jvm  toRegex 
kotlin.jvm  
toRequestBody 
kotlin.jvm  toString 
kotlin.jvm  toTypedArray 
kotlin.jvm  trim 
kotlin.jvm  
trimIndent 
kotlin.jvm  until 
kotlin.jvm  
uploadFile 
kotlin.jvm  use 
kotlin.jvm  withContext 
kotlin.jvm  AccessibilityEvent 
kotlin.ranges  ActivityCompat 
kotlin.ranges  ActivityDashboardBinding 
kotlin.ranges  ActivityGraphFragment 
kotlin.ranges  ActivityMainBinding 
kotlin.ranges  ActivityManager 
kotlin.ranges  ActivityYahooAuthBinding 
kotlin.ranges  AlertDialog 
kotlin.ranges  	ApiClient 
kotlin.ranges  
ApiService 
kotlin.ranges  AppConstant 
kotlin.ranges  AppInfo 
kotlin.ranges  	ArrayList 
kotlin.ranges  BatteryInfo 
kotlin.ranges  BatteryManager 
kotlin.ranges  BufferedReader 
kotlin.ranges  Build 
kotlin.ranges  Class 
kotlin.ranges  Color 
kotlin.ranges  
ColorTemplate 
kotlin.ranges  
CommonRequest 
kotlin.ranges  
CommonUtil 
kotlin.ranges  
ComponentName 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  DashboardMenuAdapter 
kotlin.ranges  DashboardMenuModel 
kotlin.ranges  Date 
kotlin.ranges  DeviceDetailsFragment 
kotlin.ranges  
DeviceInfo 
kotlin.ranges  DeviceInfoHelper 
kotlin.ranges  Dialog 
kotlin.ranges  Dispatchers 
kotlin.ranges  DisplayMetrics 
kotlin.ranges  DisputeListAdapter 
kotlin.ranges  DisputeMaillistFragment 
kotlin.ranges  DisputeRaiseRequest 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  FileOutputStream 
kotlin.ranges  
FileReader 
kotlin.ranges  FirebaseAuth 
kotlin.ranges  FragmentActivityGraphBinding 
kotlin.ranges  FragmentDeviceDetailsBinding 
kotlin.ranges  FragmentDisputeMaillistBinding 
kotlin.ranges  FragmentHomeBinding 
kotlin.ranges  FragmentRaiseBottomSheetBinding 
kotlin.ranges  FragmentSpamMailBinding 
kotlin.ranges  FragmentSuggetionBottomBinding 
kotlin.ranges  !FragmentWarningBottomSheetBinding 
kotlin.ranges  GridLayoutManager 
kotlin.ranges  GsonConverterFactory 
kotlin.ranges  Handler 
kotlin.ranges  HomeFragment 
kotlin.ranges  Html 
kotlin.ranges  HttpLoggingInterceptor 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  ItemDashboardMenuBinding 
kotlin.ranges  ItemDisputeListBinding 
kotlin.ranges  ItemSpamMailBinding 
kotlin.ranges  LAYOUT_INFLATER_SERVICE 
kotlin.ranges  LayoutInflater 
kotlin.ranges  LayoutProgressBinding 
kotlin.ranges  LayoutProgressDialogBinding 
kotlin.ranges  LayoutSpamDetailBinding 
kotlin.ranges  LayoutValidationBinding 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  Manifest 
kotlin.ranges  
MemoryInfo 
kotlin.ranges  
MultipartBody 
kotlin.ranges  NetworkCapabilities 
kotlin.ranges  NetworkInfo 
kotlin.ranges  NetworkInterface 
kotlin.ranges  NewGmailAccessibilityService 
kotlin.ranges  ObjectAnimator 
kotlin.ranges  OkHttpClient 
kotlin.ranges  PackageManager 
kotlin.ranges  PendingMailRequest 
kotlin.ranges  PieData 
kotlin.ranges  
PieDataSet 
kotlin.ranges  PieEntry 
kotlin.ranges  PixelFormat 
kotlin.ranges  R 
kotlin.ranges  RaiseBottomSheetFragment 
kotlin.ranges  Regex 
kotlin.ranges  Request 
kotlin.ranges  RequestBody 
kotlin.ranges  Retrofit 
kotlin.ranges  Runtime 
kotlin.ranges  
ScreenInfo 
kotlin.ranges  SecurityException 
kotlin.ranges  Settings 
kotlin.ranges  SharedPrefManager 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  Snackbar 
kotlin.ranges  SpamMailAdapter 
kotlin.ranges  SpamMailFragment 
kotlin.ranges  StorageInfo 
kotlin.ranges  
StringBuilder 
kotlin.ranges  SuggetionBottomFragment 
kotlin.ranges  System 
kotlin.ranges  
SystemInfo 
kotlin.ranges  TAG 
kotlin.ranges  
TelephonyInfo 
kotlin.ranges  TelephonyManager 
kotlin.ranges  	TextUtils 
kotlin.ranges  Toast 
kotlin.ranges  Uri 
kotlin.ranges  View 
kotlin.ranges  
ViewCompat 
kotlin.ranges  WINDOW_SERVICE 
kotlin.ranges  WarningBottomSheetFragment 
kotlin.ranges  Window 
kotlin.ranges  WindowInsetsCompat 
kotlin.ranges  
WindowManager 
kotlin.ranges  also 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  
appendLine 
kotlin.ranges  applicationContext 
kotlin.ranges  apply 
kotlin.ranges  arrayListOf 
kotlin.ranges  arrayOf 
kotlin.ranges  binding 
kotlin.ranges  buildString 
kotlin.ranges  checkAiResponse 
kotlin.ranges  com 
kotlin.ranges  contains 
kotlin.ranges  deviceInfoHelper 
kotlin.ranges  displayDeviceInfo 
kotlin.ranges  endsWith 
kotlin.ranges  equals 
kotlin.ranges  
extractEmails 
kotlin.ranges  filter 
kotlin.ranges  
filterIndexed 
kotlin.ranges  first 
kotlin.ranges  firstOrNull 
kotlin.ranges  format 
kotlin.ranges  formatBytes 
kotlin.ranges  	getOrNull 
kotlin.ranges  getValue 
kotlin.ranges  invoke 
kotlin.ranges  isBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrBlank 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  iterator 
kotlin.ranges  java 
kotlin.ranges  joinToString 
kotlin.ranges  launch 
kotlin.ranges  lazy 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  	lowercase 
kotlin.ranges  map 
kotlin.ranges  mapOf 
kotlin.ranges  matches 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  mutableSetOf 
kotlin.ranges  provideDelegate 
kotlin.ranges  removePrefix 
kotlin.ranges  repeat 
kotlin.ranges  requireActivity 
kotlin.ranges  requireContext 
kotlin.ranges  set 
kotlin.ranges  setFormData 
kotlin.ranges  setResponsePopup 
kotlin.ranges  sharedPrefManager 
kotlin.ranges  showSnackbar 
kotlin.ranges  split 
kotlin.ranges  
startsWith 
kotlin.ranges  takeIf 
kotlin.ranges  to 
kotlin.ranges  toByteArray 
kotlin.ranges  toList 
kotlin.ranges  toMediaType 
kotlin.ranges  toMediaTypeOrNull 
kotlin.ranges  toRegex 
kotlin.ranges  
toRequestBody 
kotlin.ranges  toString 
kotlin.ranges  toTypedArray 
kotlin.ranges  trim 
kotlin.ranges  
trimIndent 
kotlin.ranges  until 
kotlin.ranges  
uploadFile 
kotlin.ranges  use 
kotlin.ranges  withContext 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  AccessibilityEvent kotlin.sequences  ActivityCompat kotlin.sequences  ActivityDashboardBinding kotlin.sequences  ActivityGraphFragment kotlin.sequences  ActivityMainBinding kotlin.sequences  ActivityManager kotlin.sequences  ActivityYahooAuthBinding kotlin.sequences  AlertDialog kotlin.sequences  	ApiClient kotlin.sequences  
ApiService kotlin.sequences  AppConstant kotlin.sequences  AppInfo kotlin.sequences  	ArrayList kotlin.sequences  BatteryInfo kotlin.sequences  BatteryManager kotlin.sequences  BufferedReader kotlin.sequences  Build kotlin.sequences  Class kotlin.sequences  Color kotlin.sequences  
ColorTemplate kotlin.sequences  
CommonRequest kotlin.sequences  
CommonUtil kotlin.sequences  
ComponentName kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  DashboardMenuAdapter kotlin.sequences  DashboardMenuModel kotlin.sequences  Date kotlin.sequences  DeviceDetailsFragment kotlin.sequences  
DeviceInfo kotlin.sequences  DeviceInfoHelper kotlin.sequences  Dialog kotlin.sequences  Dispatchers kotlin.sequences  DisplayMetrics kotlin.sequences  DisputeListAdapter kotlin.sequences  DisputeMaillistFragment kotlin.sequences  DisputeRaiseRequest kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  FileOutputStream kotlin.sequences  
FileReader kotlin.sequences  FirebaseAuth kotlin.sequences  FragmentActivityGraphBinding kotlin.sequences  FragmentDeviceDetailsBinding kotlin.sequences  FragmentDisputeMaillistBinding kotlin.sequences  FragmentHomeBinding kotlin.sequences  FragmentRaiseBottomSheetBinding kotlin.sequences  FragmentSpamMailBinding kotlin.sequences  FragmentSuggetionBottomBinding kotlin.sequences  !FragmentWarningBottomSheetBinding kotlin.sequences  GridLayoutManager kotlin.sequences  GsonConverterFactory kotlin.sequences  Handler kotlin.sequences  HomeFragment kotlin.sequences  Html kotlin.sequences  HttpLoggingInterceptor kotlin.sequences  Intent kotlin.sequences  ItemDashboardMenuBinding kotlin.sequences  ItemDisputeListBinding kotlin.sequences  ItemSpamMailBinding kotlin.sequences  LAYOUT_INFLATER_SERVICE kotlin.sequences  LayoutInflater kotlin.sequences  LayoutProgressBinding kotlin.sequences  LayoutProgressDialogBinding kotlin.sequences  LayoutSpamDetailBinding kotlin.sequences  LayoutValidationBinding kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  Manifest kotlin.sequences  
MemoryInfo kotlin.sequences  
MultipartBody kotlin.sequences  NetworkCapabilities kotlin.sequences  NetworkInfo kotlin.sequences  NetworkInterface kotlin.sequences  NewGmailAccessibilityService kotlin.sequences  ObjectAnimator kotlin.sequences  OkHttpClient kotlin.sequences  PackageManager kotlin.sequences  PendingMailRequest kotlin.sequences  PieData kotlin.sequences  
PieDataSet kotlin.sequences  PieEntry kotlin.sequences  PixelFormat kotlin.sequences  R kotlin.sequences  RaiseBottomSheetFragment kotlin.sequences  Regex kotlin.sequences  Request kotlin.sequences  RequestBody kotlin.sequences  Retrofit kotlin.sequences  Runtime kotlin.sequences  
ScreenInfo kotlin.sequences  SecurityException kotlin.sequences  Settings kotlin.sequences  SharedPrefManager kotlin.sequences  SimpleDateFormat kotlin.sequences  Snackbar kotlin.sequences  SpamMailAdapter kotlin.sequences  SpamMailFragment kotlin.sequences  StorageInfo kotlin.sequences  
StringBuilder kotlin.sequences  SuggetionBottomFragment kotlin.sequences  System kotlin.sequences  
SystemInfo kotlin.sequences  TAG kotlin.sequences  
TelephonyInfo kotlin.sequences  TelephonyManager kotlin.sequences  	TextUtils kotlin.sequences  Toast kotlin.sequences  Uri kotlin.sequences  View kotlin.sequences  
ViewCompat kotlin.sequences  WINDOW_SERVICE kotlin.sequences  WarningBottomSheetFragment kotlin.sequences  Window kotlin.sequences  WindowInsetsCompat kotlin.sequences  
WindowManager kotlin.sequences  also kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  
appendLine kotlin.sequences  applicationContext kotlin.sequences  apply kotlin.sequences  arrayListOf kotlin.sequences  arrayOf kotlin.sequences  binding kotlin.sequences  buildString kotlin.sequences  checkAiResponse kotlin.sequences  com kotlin.sequences  contains kotlin.sequences  deviceInfoHelper kotlin.sequences  displayDeviceInfo kotlin.sequences  endsWith kotlin.sequences  equals kotlin.sequences  
extractEmails kotlin.sequences  filter kotlin.sequences  
filterIndexed kotlin.sequences  first kotlin.sequences  firstOrNull kotlin.sequences  format kotlin.sequences  formatBytes kotlin.sequences  	getOrNull kotlin.sequences  getValue kotlin.sequences  invoke kotlin.sequences  isBlank kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrBlank kotlin.sequences  
isNullOrEmpty kotlin.sequences  iterator kotlin.sequences  java kotlin.sequences  joinToString kotlin.sequences  launch kotlin.sequences  lazy kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  	lowercase kotlin.sequences  map kotlin.sequences  mapOf kotlin.sequences  matches kotlin.sequences  
mutableListOf kotlin.sequences  mutableMapOf kotlin.sequences  mutableSetOf kotlin.sequences  provideDelegate kotlin.sequences  removePrefix kotlin.sequences  repeat kotlin.sequences  requireActivity kotlin.sequences  requireContext kotlin.sequences  set kotlin.sequences  setFormData kotlin.sequences  setResponsePopup kotlin.sequences  sharedPrefManager kotlin.sequences  showSnackbar kotlin.sequences  split kotlin.sequences  
startsWith kotlin.sequences  takeIf kotlin.sequences  to kotlin.sequences  toByteArray kotlin.sequences  toList kotlin.sequences  toMediaType kotlin.sequences  toMediaTypeOrNull kotlin.sequences  toRegex kotlin.sequences  
toRequestBody kotlin.sequences  toString kotlin.sequences  toTypedArray kotlin.sequences  trim kotlin.sequences  
trimIndent kotlin.sequences  until kotlin.sequences  
uploadFile kotlin.sequences  use kotlin.sequences  withContext kotlin.sequences  getMAP kotlin.sequences.Sequence  getMap kotlin.sequences.Sequence  	getTOList kotlin.sequences.Sequence  	getToList kotlin.sequences.Sequence  map kotlin.sequences.Sequence  toList kotlin.sequences.Sequence  AccessibilityEvent kotlin.text  ActivityCompat kotlin.text  ActivityDashboardBinding kotlin.text  ActivityGraphFragment kotlin.text  ActivityMainBinding kotlin.text  ActivityManager kotlin.text  ActivityYahooAuthBinding kotlin.text  AlertDialog kotlin.text  	ApiClient kotlin.text  
ApiService kotlin.text  AppConstant kotlin.text  AppInfo kotlin.text  	ArrayList kotlin.text  BatteryInfo kotlin.text  BatteryManager kotlin.text  BufferedReader kotlin.text  Build kotlin.text  Class kotlin.text  Color kotlin.text  
ColorTemplate kotlin.text  
CommonRequest kotlin.text  
CommonUtil kotlin.text  
ComponentName kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  DashboardMenuAdapter kotlin.text  DashboardMenuModel kotlin.text  Date kotlin.text  DeviceDetailsFragment kotlin.text  
DeviceInfo kotlin.text  DeviceInfoHelper kotlin.text  Dialog kotlin.text  Dispatchers kotlin.text  DisplayMetrics kotlin.text  DisputeListAdapter kotlin.text  DisputeMaillistFragment kotlin.text  DisputeRaiseRequest kotlin.text  	Exception kotlin.text  File kotlin.text  FileOutputStream kotlin.text  
FileReader kotlin.text  FirebaseAuth kotlin.text  FragmentActivityGraphBinding kotlin.text  FragmentDeviceDetailsBinding kotlin.text  FragmentDisputeMaillistBinding kotlin.text  FragmentHomeBinding kotlin.text  FragmentRaiseBottomSheetBinding kotlin.text  FragmentSpamMailBinding kotlin.text  FragmentSuggetionBottomBinding kotlin.text  !FragmentWarningBottomSheetBinding kotlin.text  GridLayoutManager kotlin.text  GsonConverterFactory kotlin.text  Handler kotlin.text  HomeFragment kotlin.text  Html kotlin.text  HttpLoggingInterceptor kotlin.text  Intent kotlin.text  ItemDashboardMenuBinding kotlin.text  ItemDisputeListBinding kotlin.text  ItemSpamMailBinding kotlin.text  LAYOUT_INFLATER_SERVICE kotlin.text  LayoutInflater kotlin.text  LayoutProgressBinding kotlin.text  LayoutProgressDialogBinding kotlin.text  LayoutSpamDetailBinding kotlin.text  LayoutValidationBinding kotlin.text  Locale kotlin.text  Log kotlin.text  Looper kotlin.text  Manifest kotlin.text  MatchResult kotlin.text  
MemoryInfo kotlin.text  
MultipartBody kotlin.text  NetworkCapabilities kotlin.text  NetworkInfo kotlin.text  NetworkInterface kotlin.text  NewGmailAccessibilityService kotlin.text  ObjectAnimator kotlin.text  OkHttpClient kotlin.text  PackageManager kotlin.text  PendingMailRequest kotlin.text  PieData kotlin.text  
PieDataSet kotlin.text  PieEntry kotlin.text  PixelFormat kotlin.text  R kotlin.text  RaiseBottomSheetFragment kotlin.text  Regex kotlin.text  Request kotlin.text  RequestBody kotlin.text  Retrofit kotlin.text  Runtime kotlin.text  
ScreenInfo kotlin.text  SecurityException kotlin.text  Settings kotlin.text  SharedPrefManager kotlin.text  SimpleDateFormat kotlin.text  Snackbar kotlin.text  SpamMailAdapter kotlin.text  SpamMailFragment kotlin.text  StorageInfo kotlin.text  
StringBuilder kotlin.text  SuggetionBottomFragment kotlin.text  System kotlin.text  
SystemInfo kotlin.text  TAG kotlin.text  
TelephonyInfo kotlin.text  TelephonyManager kotlin.text  	TextUtils kotlin.text  Toast kotlin.text  Uri kotlin.text  View kotlin.text  
ViewCompat kotlin.text  WINDOW_SERVICE kotlin.text  WarningBottomSheetFragment kotlin.text  Window kotlin.text  WindowInsetsCompat kotlin.text  
WindowManager kotlin.text  also kotlin.text  android kotlin.text  androidx kotlin.text  
appendLine kotlin.text  applicationContext kotlin.text  apply kotlin.text  arrayListOf kotlin.text  arrayOf kotlin.text  binding kotlin.text  buildString kotlin.text  checkAiResponse kotlin.text  com kotlin.text  contains kotlin.text  deviceInfoHelper kotlin.text  displayDeviceInfo kotlin.text  endsWith kotlin.text  equals kotlin.text  
extractEmails kotlin.text  filter kotlin.text  
filterIndexed kotlin.text  first kotlin.text  firstOrNull kotlin.text  format kotlin.text  formatBytes kotlin.text  	getOrNull kotlin.text  getValue kotlin.text  invoke kotlin.text  isBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  
isNullOrEmpty kotlin.text  iterator kotlin.text  java kotlin.text  joinToString kotlin.text  launch kotlin.text  lazy kotlin.text  let kotlin.text  listOf kotlin.text  	lowercase kotlin.text  map kotlin.text  mapOf kotlin.text  matches kotlin.text  
mutableListOf kotlin.text  mutableMapOf kotlin.text  mutableSetOf kotlin.text  provideDelegate kotlin.text  removePrefix kotlin.text  repeat kotlin.text  requireActivity kotlin.text  requireContext kotlin.text  set kotlin.text  setFormData kotlin.text  setResponsePopup kotlin.text  sharedPrefManager kotlin.text  showSnackbar kotlin.text  split kotlin.text  
startsWith kotlin.text  takeIf kotlin.text  to kotlin.text  toByteArray kotlin.text  toList kotlin.text  toMediaType kotlin.text  toMediaTypeOrNull kotlin.text  toRegex kotlin.text  
toRequestBody kotlin.text  toString kotlin.text  toTypedArray kotlin.text  trim kotlin.text  
trimIndent kotlin.text  until kotlin.text  
uploadFile kotlin.text  use kotlin.text  withContext kotlin.text  equals kotlin.text.MatchResult  getLET kotlin.text.MatchResult  getLet kotlin.text.MatchResult  let kotlin.text.MatchResult  value kotlin.text.MatchResult  find kotlin.text.Regex  findAll kotlin.text.Regex  invoke kotlin.text.Regex.Companion  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Dispatchers !kotlinx.coroutines.CoroutineScope  deviceInfoHelper !kotlinx.coroutines.CoroutineScope  displayDeviceInfo !kotlinx.coroutines.CoroutineScope  getDEVICEInfoHelper !kotlinx.coroutines.CoroutineScope  getDISPLAYDeviceInfo !kotlinx.coroutines.CoroutineScope  getDeviceInfoHelper !kotlinx.coroutines.CoroutineScope  getDisplayDeviceInfo !kotlinx.coroutines.CoroutineScope  getWITHContext !kotlinx.coroutines.CoroutineScope  getWithContext !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  Call okhttp3  Callback okhttp3  	MediaType okhttp3  
MultipartBody okhttp3  OkHttpClient okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  ResponseBody okhttp3  enqueue okhttp3.Call  	Companion okhttp3.MediaType  toMediaType okhttp3.MediaType.Companion  toMediaTypeOrNull okhttp3.MediaType.Companion  Part okhttp3.MultipartBody  createFormData okhttp3.MultipartBody.Part  createFormData $okhttp3.MultipartBody.Part.Companion  Builder okhttp3.OkHttpClient  newCall okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  Builder okhttp3.OkHttpClient.Companion  invoke okhttp3.OkHttpClient.Companion  Builder okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  get okhttp3.Request.Builder  post okhttp3.Request.Builder  url okhttp3.Request.Builder  	Companion okhttp3.RequestBody  create okhttp3.RequestBody  create okhttp3.RequestBody.Companion  
toRequestBody okhttp3.RequestBody.Companion  body okhttp3.Response  code okhttp3.Response  isSuccessful okhttp3.Response  string okhttp3.ResponseBody  HttpLoggingInterceptor okhttp3.logging  Level &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  BODY ,okhttp3.logging.HttpLoggingInterceptor.Level  Call 	retrofit2  Callback 	retrofit2  Response 	retrofit2  Retrofit 	retrofit2  enqueue retrofit2.Call  body retrofit2.Response  getISSuccessful retrofit2.Response  getIsSuccessful retrofit2.Response  isSuccessful retrofit2.Response  message retrofit2.Response  
setSuccessful retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  Body retrofit2.http  GET retrofit2.http  Headers retrofit2.http  	Multipart retrofit2.http  POST retrofit2.http  Part retrofit2.http   isAccessibilityPermissionGranted %com.tech.ekvayu.BaseClass.AppConstant  PermissionHelper com.tech.ekvayu.BaseClass  PermissionStates com.tech.ekvayu.BaseClass  isMailConfigured %com.tech.ekvayu.BaseClass.AppConstant  checkAndUpdateMailStatus .androidx.appcompat.app.AppCompatDialogFragment  checkAndUpdatePermissionStatus .androidx.appcompat.app.AppCompatDialogFragment  dismiss .androidx.appcompat.app.AppCompatDialogFragment  onResume .androidx.appcompat.app.AppCompatDialogFragment  checkAndUpdateMailStatus $androidx.fragment.app.DialogFragment  checkAndUpdatePermissionStatus $androidx.fragment.app.DialogFragment  dismiss $androidx.fragment.app.DialogFragment  onResume $androidx.fragment.app.DialogFragment  PermissionHelper androidx.fragment.app.Fragment  checkAndUpdateMailStatus androidx.fragment.app.Fragment  checkAndUpdatePermissionStatus androidx.fragment.app.Fragment  #checkPermissionsAndShowBottomSheets androidx.fragment.app.Fragment  dismiss androidx.fragment.app.Fragment  checkAndUpdateMailStatus Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  checkAndUpdatePermissionStatus Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  dismiss Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  onResume Acom.google.android.material.bottomsheet.BottomSheetDialogFragment  NewGmailAccessibilityService com.tech.ekvayu.BaseClass  getValue com.tech.ekvayu.BaseClass  
isNotEmpty com.tech.ekvayu.BaseClass  java com.tech.ekvayu.BaseClass  lazy com.tech.ekvayu.BaseClass  provideDelegate com.tech.ekvayu.BaseClass  AccessibilityService *com.tech.ekvayu.BaseClass.PermissionHelper  AppConstant *com.tech.ekvayu.BaseClass.PermissionHelper  Boolean *com.tech.ekvayu.BaseClass.PermissionHelper  Class *com.tech.ekvayu.BaseClass.PermissionHelper  
ComponentName *com.tech.ekvayu.BaseClass.PermissionHelper  Context *com.tech.ekvayu.BaseClass.PermissionHelper  NewGmailAccessibilityService *com.tech.ekvayu.BaseClass.PermissionHelper  PermissionStates *com.tech.ekvayu.BaseClass.PermissionHelper  Settings *com.tech.ekvayu.BaseClass.PermissionHelper  SharedPrefManager *com.tech.ekvayu.BaseClass.PermissionHelper  	TextUtils *com.tech.ekvayu.BaseClass.PermissionHelper  %checkAndUpdateAccessibilityPermission *com.tech.ekvayu.BaseClass.PermissionHelper  checkAndUpdateMailConfiguration *com.tech.ekvayu.BaseClass.PermissionHelper  equals *com.tech.ekvayu.BaseClass.PermissionHelper  	getEQUALS *com.tech.ekvayu.BaseClass.PermissionHelper  	getEquals *com.tech.ekvayu.BaseClass.PermissionHelper  getGETValue *com.tech.ekvayu.BaseClass.PermissionHelper  getGetValue *com.tech.ekvayu.BaseClass.PermissionHelper  
getISNotEmpty *com.tech.ekvayu.BaseClass.PermissionHelper  getISNullOrEmpty *com.tech.ekvayu.BaseClass.PermissionHelper  
getIsNotEmpty *com.tech.ekvayu.BaseClass.PermissionHelper  getIsNullOrEmpty *com.tech.ekvayu.BaseClass.PermissionHelper  getLAZY *com.tech.ekvayu.BaseClass.PermissionHelper  getLazy *com.tech.ekvayu.BaseClass.PermissionHelper  getPROVIDEDelegate *com.tech.ekvayu.BaseClass.PermissionHelper  getPermissionStates *com.tech.ekvayu.BaseClass.PermissionHelper  getProvideDelegate *com.tech.ekvayu.BaseClass.PermissionHelper  getValue *com.tech.ekvayu.BaseClass.PermissionHelper  isAccessibilityServiceEnabled *com.tech.ekvayu.BaseClass.PermissionHelper  
isNotEmpty *com.tech.ekvayu.BaseClass.PermissionHelper  
isNullOrEmpty *com.tech.ekvayu.BaseClass.PermissionHelper  java *com.tech.ekvayu.BaseClass.PermissionHelper  lazy *com.tech.ekvayu.BaseClass.PermissionHelper  provideDelegate *com.tech.ekvayu.BaseClass.PermissionHelper  sharedPrefManager *com.tech.ekvayu.BaseClass.PermissionHelper  Boolean *com.tech.ekvayu.BaseClass.PermissionStates  String *com.tech.ekvayu.BaseClass.PermissionStates  receiverMail *com.tech.ekvayu.BaseClass.PermissionStates  
getBoolean +com.tech.ekvayu.BaseClass.SharedPrefManager  
putBoolean +com.tech.ekvayu.BaseClass.SharedPrefManager  AppConstant 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  SharedPrefManager 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  checkAndUpdateMailStatus 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  dismiss 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  getGETValue 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  getGetValue 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  
getISNotEmpty 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  
getIsNotEmpty 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  getLAZY 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  getLazy 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  getPROVIDEDelegate 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  getProvideDelegate 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  getValue 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  
isNotEmpty 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  lazy 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  provideDelegate 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  sharedPrefManager 3com.tech.ekvayu.BottomSheet.SuggetionBottomFragment  AppConstant 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  SharedPrefManager 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  checkAndUpdatePermissionStatus 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  dismiss 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getGETValue 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getGetValue 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getLAZY 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getLazy 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getPROVIDEDelegate 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getProvideDelegate 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  getValue 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  lazy 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  provideDelegate 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  sharedPrefManager 6com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment  PermissionHelper com.tech.ekvayu.Fragments  PermissionHelper &com.tech.ekvayu.Fragments.HomeFragment  #checkPermissionsAndShowBottomSheets &com.tech.ekvayu.Fragments.HomeFragment  PermissionHelper 	java.lang  PermissionStates 	java.lang  PermissionHelper kotlin  PermissionStates kotlin  PermissionHelper kotlin.annotation  PermissionStates kotlin.annotation  PermissionHelper kotlin.collections  PermissionStates kotlin.collections  PermissionHelper kotlin.comparisons  PermissionStates kotlin.comparisons  PermissionHelper 	kotlin.io  PermissionStates 	kotlin.io  PermissionHelper 
kotlin.jvm  PermissionStates 
kotlin.jvm  PermissionHelper 
kotlin.ranges  PermissionStates 
kotlin.ranges  PermissionHelper kotlin.sequences  PermissionStates kotlin.sequences  PermissionHelper kotlin.text  PermissionStates kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       