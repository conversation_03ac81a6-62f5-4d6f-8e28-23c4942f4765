package com.tech.ekvayu.Activities


import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.BottomSheet.SuggetionBottomFragment
import com.tech.ekvayu.BottomSheet.ThemeSettingsBottomSheetFragment
import com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment
import com.tech.ekvayu.EkService.NewGmailAccessibilityService
import com.tech.ekvayu.Fragments.HomeFragment
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.ActivityDashboardBinding

class DashboardActivity : AppCompatActivity(){
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private lateinit var binding: ActivityDashboardBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding=ActivityDashboardBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.header.cvBack.setOnClickListener { onBackPressed() }

        // Theme icon click listener
        binding.header.cvTheme.setOnClickListener {
            val themeBottomSheet = ThemeSettingsBottomSheetFragment.newInstance()
            themeBottomSheet.show(supportFragmentManager, themeBottomSheet.tag)
        }

        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(R.id.fragmentContainer, HomeFragment())
                .commit()
        }

    }

    fun showFragment(fragment: androidx.fragment.app.Fragment) {
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragmentContainer, fragment)
            .addToBackStack(null)  // Add to back stack
            .commit()
    }

    override fun onBackPressed() {
        if (supportFragmentManager.backStackEntryCount > 0) {
            supportFragmentManager.popBackStack()
        } else {
            super.onBackPressed()
        }
    }



}