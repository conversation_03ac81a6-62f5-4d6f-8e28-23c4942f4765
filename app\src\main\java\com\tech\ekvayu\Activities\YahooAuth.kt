package com.tech.ekvayu.Activities

import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.OAuthCredential
import com.google.firebase.auth.OAuthProvider
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.databinding.ActivityYahooAuthBinding
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.IOException

class YahooAuth : AppCompatActivity() {

    private lateinit var binding: ActivityYahooAuthBinding
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityYahooAuthBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.btAuth.setOnClickListener {
            //signInWithYahoo()
        }

    }

    /*fun signInWithYahoo() {
        val provider = OAuthProvider.newBuilder("yahoo.com").apply {
            OAuthProvider.Builder.setScopes = listOf("openid", "email", "profile")
        }


        auth.startActivityForSignInWithProvider(this, provider.build())
            .addOnSuccessListener { authResult ->
                val user = authResult.user
                val credential = authResult.credential as? OAuthCredential
                val accessToken = credential?.accessToken
                val idToken = credential?.idToken
                Log.d("YahooAuth", "Yahoo sign-in success: ${user?.email}")
                Log.d("YahooAuth", "User UID: ${user?.uid}")
                Log.d("YahooAuth", "Display Name: ${user?.displayName}")
                Log.d("YahooAuth", "Profile Picture: ${user?.photoUrl}")
                Log.d("YahooAuth", "Access Token: $accessToken")
                Log.d("YahooAuth", "ID Token: $idToken")

                val  sharedPref = SharedPrefManager.getInstance(applicationContext)
                sharedPref.putString(AppConstant.authtoken, accessToken)
            *//*    val intent=Intent(applicationContext,MainActivity::class.java)
                startActivity(intent)
                *//*
                getMail(accessToken,"<EMAIL>")
            }
            .addOnFailureListener { e ->
                Log.e("YahooAuth", "Yahoo sign-in failed: ${e.message}")
            }
    }
*/
    fun getMail(accessToken: String?, emailId: String)
    {
        val url = "https://mail.yahooapis.com/ws/mail/v1/mbox/messages/$emailId/raw"

        val request = Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer $accessToken")
            .build()

        OkHttpClient().newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("YahooMailAPI", "Error fetching raw email", e)
               // callback(null)
            }

            override fun onResponse(call: Call, response: Response) {
                if (response.isSuccessful) {
                    Log.d("responseYahoo", "onResponse: "+response.body)
                  //  callback(response.body?.string())
                } else {
                  //  Log.e("YahooMailAPI", "Error: ${response.code}")
                  //  callback(null)
                    Log.d("responseYahoo", "onResponse: "+response.body)
                }
            }
        })
    }
}