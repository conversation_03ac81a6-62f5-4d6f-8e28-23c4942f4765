package com.tech.ekvayu.ActivityGraph

import com.google.gson.annotations.SerializedName

data class ActivityGraphResponse(
    @SerializedName("message" ) var message : String? = null,
    @SerializedName("STATUS"  ) var STATUS  : String? = null,
    @SerializedName("Code"    ) var Code    : Int?    = null,
    @SerializedName("data"    ) var data    : Data?   = Data()
)

data class Data (
    @SerializedName("total_disputes"         ) var totalDisputes        : Int? = null,
    @SerializedName("total_processed_emails" ) var totalProcessedEmails : Int? = null,
    @SerializedName("total_spam_emails"      ) var totalSpamEmails      : Int? = null
)