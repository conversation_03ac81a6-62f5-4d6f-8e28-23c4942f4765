package com.tech.ekvayu.Adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.ItemDashboardMenuBinding
import com.tech.ekvayu.models.DashboardMenuModel


class DashboardMenuAdapter(private val items: List<DashboardMenuModel>, private val listener: onClickEventListner) : RecyclerView.Adapter<DashboardMenuAdapter.MenuViewHolder>() {

    private var selectedPosition = -1

    inner class MenuViewHolder(val binding: ItemDashboardMenuBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuViewHolder {
        val binding = ItemDashboardMenuBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MenuViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>uViewHolder, position: Int) {
        val item = items[position]
        holder.binding.tvMenuName.text = item.title
        holder.binding.tvDescripton.text = item.desc
        holder.binding.ivIcon.setImageResource(item.iconResId)

        if (selectedPosition == position) {
            Log.d("GetClicked", "onBindViewHolder: clicked")
            holder.binding.cvMain.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.app_color))
        } else {
            holder.binding.cvMain.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.white))
        }

        holder.binding.cvMain.setOnClickListener {
            val previousPosition = selectedPosition
            selectedPosition = position
            notifyItemChanged(previousPosition)
            notifyItemChanged(position)

            listener.onMenuClick(item)
        }

    }

    override fun getItemCount() = items.size


    interface  onClickEventListner{
        fun onMenuClick(item: DashboardMenuModel)
    }


}
