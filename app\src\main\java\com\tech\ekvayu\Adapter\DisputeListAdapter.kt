package com.tech.ekvayu.Adapter

import android.R
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.Dispute.Results
import com.tech.ekvayu.databinding.ItemDisputeListBinding



class DisputeListAdapter(private val items: List<Results>, val listener: OnDisputeMailListClickListener) : RecyclerView.Adapter<DisputeListAdapter.MenuViewHolder>() {

    inner class MenuViewHolder(val binding: ItemDisputeListBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuViewHolder {
        val binding = ItemDisputeListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MenuViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MenuViewHolder, position: Int) {
        val item = items[position]


        holder.binding.tvSender.text = "Sender"+":"+item.sendersEmail
        holder.binding.tvReceiver.text = "Receiver"+":"+item.recieversEmail
      //  holder.binding.tvStatus.text = item.status

        if (item.status=="safe")
        {
           // holder.binding.cvStatus.setCardBackgroundColor(ContextCompat.getColor(holder.itemView.context, R.color.holo_green_dark))
            holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.holo_green_dark))
            holder.binding.tvStatus.text="Safe"
        }
        else if (item.status=="unsafe")
        {
            holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.holo_red_light))
            holder.binding.tvStatus.text="Unsafe"
        }
        else
        {
            holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.darker_gray))
            holder.binding.tvStatus.text="Pending"
        }


        holder.binding.root.setOnClickListener {
            listener.onDisputeClicked(item)
        }
    }

    override fun getItemCount() = items.size

    interface  OnDisputeMailListClickListener{
        fun onDisputeClicked(item: Results)
    }
}
