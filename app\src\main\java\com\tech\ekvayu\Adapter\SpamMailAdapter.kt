package com.tech.ekvayu.Adapter

import android.R
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tech.ekvayu.Response.Results
import com.tech.ekvayu.databinding.ItemSpamMailBinding


class SpamMailAdapter(private val items: List<Results>,  val listener: OnSpamMailClickListener) : RecyclerView.Adapter<SpamMailAdapter.MenuViewHolder>() {

    inner class MenuViewHolder(val binding: ItemSpamMailBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuViewHolder {
        val binding = ItemSpamMailBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MenuViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MenuViewHolder, position: Int) {
        val item = items[position]


        holder.binding.tvSender.text = "Sender"+":"+item.sendersEmail
        holder.binding.tvReceiver.text = "Receiver"+":"+item.recieversEmail


        if (item.status=="unsafe")
        {
            holder.binding.tvStatus.text = "Unsafe"
            holder.binding.tvStatus.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.holo_red_light))
        }

        holder.binding.root.setOnClickListener {
            listener.onSpamClicked(item)
        }
    }

    override fun getItemCount() = items.size

    interface  OnSpamMailClickListener{
        fun onSpamClicked(item: Results)
    }
}
