package com.tech.ekvayu.ApiConfig


import android.content.Context
import android.widget.Toast
import com.tech.ekvayu.BaseClass.CommonUtil
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory


class ApiClient {

    companion object {
        //QA URL
      //  private const val BASE_URL = "http://43.204.32.124:8001/"
       //  Production URL
      //  private const val BASE_URL = "http://192.168.0.2:10101/"
        // AWS Production URL

     //   private const val BASE_URL = "http:// 43.204.32.124:8006"

        /* 3.109.178.115:8006   backend
         3.109.178.115:8007   frontend*/

      //  newQA
        private const val BASE_URL = "http://3.109.178.115:6060"



        private fun createLoggingInterceptor(): HttpLoggingInterceptor {
            val loggingInterceptor = HttpLoggingInterceptor()
            loggingInterceptor.level = HttpLoggingInterceptor.Level.BODY
            return loggingInterceptor
        }

        private fun createOkHttpClient(): OkHttpClient {
            return OkHttpClient.Builder()
                .addInterceptor(createLoggingInterceptor())
                .build()
        }

        fun getRetrofitInstance(context: Context): Retrofit? {
            if (CommonUtil.isNetworkAvailable(context)) {
                return Retrofit.Builder()
                    .baseUrl(BASE_URL)
                    .client(createOkHttpClient())
                    .addConverterFactory(GsonConverterFactory.create())
                    .build()
            } else {
                Toast.makeText(context, "No internet connection available", Toast.LENGTH_SHORT).show()
                return null
            }
        }


    }


}