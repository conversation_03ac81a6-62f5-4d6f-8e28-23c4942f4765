package com.tech.ekvayu.BaseClass

import android.accessibilityservice.AccessibilityService
import android.app.Dialog
import android.content.ComponentName
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.provider.Settings
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.Window
import androidx.core.content.ContextCompat
import com.google.android.material.snackbar.Snackbar
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.LayoutProgressDialogBinding

object CommonUtil {

    private var progressDialog: Dialog? = null
    fun showProgressDialog(context: Context, message: String = "Please wait...") {
        if (progressDialog?.isShowing == true) return

        val binding = LayoutProgressDialogBinding.inflate(LayoutInflater.from(context))

        progressDialog?.window?.setBackgroundDrawableResource(R.drawable.bg_dialog)
        progressDialog?.window?.setDimAmount(0.3f)

        progressDialog = Dialog(context).apply {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            setCancelable(false)
            setContentView(binding.root)
            binding.tvMessage.text = message
            show()
        }
    }


    fun hideProgressDialog() {
        progressDialog?.takeIf { it.isShowing }?.dismiss()
        progressDialog = null
    }

    fun showSnackbar(view: View, message: String) {
        Snackbar.make(view, message, Snackbar.LENGTH_LONG)
            .setAction("OK") {  }
            .setBackgroundTint(ContextCompat.getColor(view.context,R.color.app_color))
            .show()
    }


    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            return capabilities != null && capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } else {
            val activeNetworkInfo = connectivityManager.activeNetworkInfo
            return activeNetworkInfo != null && activeNetworkInfo.isConnected
        }
    }
     fun isAccessibilityServiceEnabled(context: Context, service: Class<out AccessibilityService>): Boolean {
        val componentName = ComponentName(context, service)
        val enabledServices = Settings.Secure.getString(context.contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)

        if (enabledServices.isNullOrEmpty()) {
            return false
        }

        val colonSplitter = TextUtils.SimpleStringSplitter(':')
        colonSplitter.setString(enabledServices)  // This will not throw NullPointerException now

        while (colonSplitter.hasNext()) {
            if (colonSplitter.next().equals(componentName.flattenToString(), ignoreCase = true)) {
                return true
            }
        }
        return false
    }


}