package com.tech.ekvayu.BaseClass

import android.content.Context
import android.util.Log

/**
 * Fully automatic email detection helper that programmatically determines receiver email
 * in all conditions without manual configuration
 */
object EmailDetectionHelper {

    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    
    /**
     * Fully automatic receiver email detection for all scenarios
     * Programmatically determines receiver email without any manual configuration
     *
     * @param fromEmail The sender's email address
     * @param toEmails List of recipient emails extracted from the email
     * @param ccEmails List of CC emails
     * @param bccEmails List of BCC emails
     * @param context Android context for additional detection methods
     * @return The appropriate receiver email to use for processing
     */
    fun determineReceiverEmail(
        fromEmail: String,
        toEmails: List<String>,
        ccEmails: List<String> = emptyList(),
        bccEmails: List<String> = emptyList(),
        context: Context? = null
    ): String {

        Log.d("EmailDetection", """
            === AUTOMATIC EMAIL DETECTION ===
            fromEmail: $fromEmail
            toEmails: ${toEmails.joinToString(", ")}
            ccEmails: ${ccEmails.joinToString(", ")}
            bccEmails: ${bccEmails.joinToString(", ")}
        """.trimIndent())

        // Get stored user primary email (learned from previous emails)
        val learnedUserEmail = sharedPrefManager.getString(AppConstant.userPrimaryEmail, "")

        // PRIORITY 1: External email with clear recipients (Learning Phase)
        if (toEmails.isNotEmpty() && !isSelfEmail(fromEmail, toEmails)) {
            val receiverEmail = selectBestReceiverFromList(toEmails)
            Log.d("EmailDetection", "✅ PRIORITY 1: External email - Using: $receiverEmail")

            // Learn and store this as user's primary email if it's better than current
            if (shouldUpdateLearnedEmail(learnedUserEmail, receiverEmail)) {
                sharedPrefManager.putString(AppConstant.userPrimaryEmail, receiverEmail)
                Log.d("EmailDetection", "📚 LEARNED: Updated primary email to: $receiverEmail")
            }

            return receiverEmail
        }

        // PRIORITY 2: Use learned email from previous external emails
        if (learnedUserEmail.isNotEmpty() && learnedUserEmail.isValidEmail()) {
            Log.d("EmailDetection", "✅ PRIORITY 2: Using learned email: $learnedUserEmail")
            return learnedUserEmail
        }

        // PRIORITY 3: Self-email scenario - extract user email intelligently
        if (toEmails.isNotEmpty() && isSelfEmail(fromEmail, toEmails)) {
            val userEmail = extractUserEmailFromSelfScenario(fromEmail, toEmails)
            if (userEmail.isNotEmpty()) {
                Log.d("EmailDetection", "✅ PRIORITY 3: Self-email detected: $userEmail")

                // Learn this email for future use
                sharedPrefManager.putString(AppConstant.userPrimaryEmail, userEmail)
                return userEmail
            }
        }

        // PRIORITY 4: Analyze CC/BCC for potential user email
        val allRecipients = toEmails + ccEmails + bccEmails
        val potentialUserEmail = findMostLikelyUserEmail(allRecipients, fromEmail)

        if (potentialUserEmail.isNotEmpty()) {
            Log.d("EmailDetection", "✅ PRIORITY 4: Found potential user email: $potentialUserEmail")

            // Learn this email
            sharedPrefManager.putString(AppConstant.userPrimaryEmail, potentialUserEmail)
            return potentialUserEmail
        }

        // PRIORITY 5: Use sender email if it looks like a user email (not system/noreply)
        if (fromEmail.isValidEmail() && isLikelyUserEmail(fromEmail)) {
            Log.d("EmailDetection", "✅ PRIORITY 5: Using sender as user email: $fromEmail")

            // Learn this email
            sharedPrefManager.putString(AppConstant.userPrimaryEmail, fromEmail)
            return fromEmail
        }

        // PRIORITY 6: Try to get user's Google account email (if available)
        context?.let { ctx ->
            val googleAccountEmail = tryGetGoogleAccountEmail(ctx)
            if (googleAccountEmail.isNotEmpty()) {
                Log.d("EmailDetection", "✅ PRIORITY 6: Using Google account email: $googleAccountEmail")

                // Learn this email
                sharedPrefManager.putString(AppConstant.userPrimaryEmail, googleAccountEmail)
                return googleAccountEmail
            }
        }

        // FINAL FALLBACK: Use default email
        Log.d("EmailDetection", "⚠️ FALLBACK: Using default email: ${AppConstant.defaultReceiverEmail}")
        return AppConstant.defaultReceiverEmail
    }
    
    /**
     * Select the best receiver email from a list of recipients
     */
    private fun selectBestReceiverFromList(toEmails: List<String>): String {
        // Prefer non-system emails (avoid noreply, system, etc.)
        val userEmails = toEmails.filter { isLikelyUserEmail(it) }

        return if (userEmails.isNotEmpty()) {
            userEmails.first()
        } else {
            toEmails.first()
        }
    }

    /**
     * Check if we should update the learned email with a new one
     */
    private fun shouldUpdateLearnedEmail(currentLearned: String, newEmail: String): Boolean {
        if (currentLearned.isEmpty()) return true

        // Update if new email seems more like a user email
        if (isLikelyUserEmail(newEmail) && !isLikelyUserEmail(currentLearned)) {
            return true
        }

        // Keep the current one if it's already good
        return false
    }

    /**
     * Extract user email from self-email scenario
     */
    private fun extractUserEmailFromSelfScenario(fromEmail: String, toEmails: List<String>): String {
        // In self-email, sender and receiver are the same
        // Use the sender email if it's valid
        return if (fromEmail.isValidEmail() && isLikelyUserEmail(fromEmail)) {
            fromEmail
        } else {
            // Try to find the best email from TO list
            toEmails.find { it.isValidEmail() && isLikelyUserEmail(it) } ?: ""
        }
    }

    /**
     * Find the most likely user email from all recipients
     */
    private fun findMostLikelyUserEmail(allRecipients: List<String>, excludeEmail: String): String {
        return allRecipients
            .filter { it.isValidEmail() && !it.equals(excludeEmail, ignoreCase = true) }
            .filter { isLikelyUserEmail(it) }
            .firstOrNull() ?: ""
    }

    /**
     * Check if an email looks like a user email (not system/noreply/automated)
     */
    private fun isLikelyUserEmail(email: String): Boolean {
        if (!email.isValidEmail()) return false

        val lowerEmail = email.lowercase()

        // Exclude system/automated emails
        val systemPatterns = listOf(
            "noreply", "no-reply", "donotreply", "do-not-reply",
            "system", "admin", "administrator", "support",
            "notification", "notify", "alerts", "automated",
            "mailer", "daemon", "postmaster", "webmaster"
        )

        val isSystemEmail = systemPatterns.any { pattern ->
            lowerEmail.contains(pattern)
        }

        return !isSystemEmail
    }

    /**
     * Try to get user's Google account email from the device
     */
    private fun tryGetGoogleAccountEmail(context: Context): String {
        return try {
            // This would require additional permissions and implementation
            // For now, return empty string as this is an advanced feature
            ""
        } catch (e: Exception) {
            Log.w("EmailDetection", "Could not get Google account email: ${e.message}")
            ""
        }
    }

    /**
     * Check if this is a self-email scenario (sender sending to themselves)
     */
    private fun isSelfEmail(fromEmail: String, toEmails: List<String>): Boolean {
        if (toEmails.isEmpty()) return false

        // Check if sender is in the recipient list
        val isSelfSending = toEmails.any { it.equals(fromEmail, ignoreCase = true) }

        // Check if all recipients are the same as sender
        val allSameAsSender = toEmails.all { it.equals(fromEmail, ignoreCase = true) }

        Log.d("EmailDetection", "Self-email check: isSelfSending=$isSelfSending, allSameAsSender=$allSameAsSender")

        return isSelfSending || allSameAsSender
    }
    
    /**
     * Get the current learned user email (for debugging)
     */
    fun getLearnedUserEmail(): String {
        return sharedPrefManager.getString(AppConstant.userPrimaryEmail, "")
    }

    /**
     * Clear learned user email (useful for testing or reset)
     */
    fun clearLearnedUserEmail() {
        sharedPrefManager.putString(AppConstant.userPrimaryEmail, "")
        Log.d("EmailDetection", "Cleared learned user email")
    }

    /**
     * Extension function to validate email format
     */
    private fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }

    /**
     * Get automatic detection statistics for debugging
     */
    fun getAutomaticDetectionStats(): AutomaticDetectionStats {
        val learnedEmail = getLearnedUserEmail()
        val currentReceiverMail = sharedPrefManager.getString(AppConstant.receiverMail, "")

        return AutomaticDetectionStats(
            learnedUserEmail = learnedEmail,
            currentReceiverMail = currentReceiverMail,
            defaultReceiverEmail = AppConstant.defaultReceiverEmail,
            hasLearnedEmail = learnedEmail.isNotEmpty(),
            isUsingDefault = currentReceiverMail == AppConstant.defaultReceiverEmail,
            isUsingLearned = currentReceiverMail == learnedEmail
        )
    }
}

/**
 * Data class for automatic email detection statistics
 */
data class AutomaticDetectionStats(
    val learnedUserEmail: String,
    val currentReceiverMail: String,
    val defaultReceiverEmail: String,
    val hasLearnedEmail: Boolean,
    val isUsingDefault: Boolean,
    val isUsingLearned: Boolean
)
