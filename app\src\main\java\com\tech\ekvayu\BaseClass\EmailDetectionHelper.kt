package com.tech.ekvayu.BaseClass

import android.util.Log

/**
 * Helper class to intelligently detect receiver email based on different scenarios
 */
object EmailDetectionHelper {
    
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    
    /**
     * Smart receiver email detection that handles both self-testing and external email scenarios
     * 
     * @param fromEmail The sender's email address
     * @param toEmails List of recipient emails extracted from the email
     * @param ccEmails List of CC emails
     * @param bccEmails List of BCC emails
     * @return The appropriate receiver email to use for processing
     */
    fun determineReceiverEmail(
        fromEmail: String,
        toEmails: List<String>,
        ccEmails: List<String> = emptyList(),
        bccEmails: List<String> = emptyList()
    ): String {
        
        Log.d("EmailDetection", """
            Determining receiver email:
            fromEmail: $fromEmail
            toEmails: ${toEmails.joinToString(", ")}
            ccEmails: ${ccEmails.joinToString(", ")}
            bccEmails: ${bccEmails.joinToString(", ")}
        """.trimIndent())
        
        // Get stored user primary email (if any)
        val userPrimaryEmail = sharedPrefManager.getString(AppConstant.userPrimaryEmail, "")
        
        // Scenario 1: External email with clear recipients
        if (toEmails.isNotEmpty() && !isSelfEmail(fromEmail, toEmails)) {
            val receiverEmail = toEmails.first()
            Log.d("EmailDetection", "Scenario 1: External email - Using first TO email: $receiverEmail")
            
            // Store this as user's primary email if not already stored
            if (userPrimaryEmail.isEmpty()) {
                sharedPrefManager.putString(AppConstant.userPrimaryEmail, receiverEmail)
                Log.d("EmailDetection", "Stored user primary email: $receiverEmail")
            }
            
            return receiverEmail
        }
        
        // Scenario 2: Self-testing (sender = receiver) or no clear recipients
        if (toEmails.isEmpty() || isSelfEmail(fromEmail, toEmails)) {
            Log.d("EmailDetection", "Scenario 2: Self-testing or unclear recipients")
            
            // Sub-scenario 2a: Use stored primary email if available
            if (userPrimaryEmail.isNotEmpty() && userPrimaryEmail.isValidEmail()) {
                Log.d("EmailDetection", "Using stored primary email: $userPrimaryEmail")
                return userPrimaryEmail
            }
            
            // Sub-scenario 2b: Use sender email if it's valid and different from default
            if (fromEmail.isValidEmail() && fromEmail != AppConstant.defaultReceiverEmail) {
                Log.d("EmailDetection", "Using sender email as receiver: $fromEmail")
                
                // Store this as user's primary email
                sharedPrefManager.putString(AppConstant.userPrimaryEmail, fromEmail)
                return fromEmail
            }
            
            // Sub-scenario 2c: Fallback to default email
            Log.d("EmailDetection", "Using default receiver email: ${AppConstant.defaultReceiverEmail}")
            return AppConstant.defaultReceiverEmail
        }
        
        // Scenario 3: Mixed scenario - check CC/BCC for user email
        val allRecipients = toEmails + ccEmails + bccEmails
        val potentialUserEmail = allRecipients.find { email ->
            email.isValidEmail() && email != fromEmail
        }
        
        if (potentialUserEmail != null) {
            Log.d("EmailDetection", "Scenario 3: Found potential user email in recipients: $potentialUserEmail")
            
            // Store as primary email if not already stored
            if (userPrimaryEmail.isEmpty()) {
                sharedPrefManager.putString(AppConstant.userPrimaryEmail, potentialUserEmail)
            }
            
            return potentialUserEmail
        }
        
        // Final fallback
        Log.d("EmailDetection", "Final fallback: Using default receiver email")
        return AppConstant.defaultReceiverEmail
    }
    
    /**
     * Check if this is a self-email scenario (sender sending to themselves)
     */
    private fun isSelfEmail(fromEmail: String, toEmails: List<String>): Boolean {
        if (toEmails.isEmpty()) return false
        
        // Check if sender is in the recipient list
        val isSelfSending = toEmails.any { it.equals(fromEmail, ignoreCase = true) }
        
        // Check if all recipients are the same as sender
        val allSameAsSender = toEmails.all { it.equals(fromEmail, ignoreCase = true) }
        
        Log.d("EmailDetection", "Self-email check: isSelfSending=$isSelfSending, allSameAsSender=$allSameAsSender")
        
        return isSelfSending || allSameAsSender
    }
    
    /**
     * Update user's primary email (useful when user changes their main email)
     */
    fun updateUserPrimaryEmail(email: String) {
        if (email.isValidEmail()) {
            sharedPrefManager.putString(AppConstant.userPrimaryEmail, email)
            Log.d("EmailDetection", "Updated user primary email to: $email")
        }
    }
    
    /**
     * Get the current user's primary email
     */
    fun getUserPrimaryEmail(): String {
        return sharedPrefManager.getString(AppConstant.userPrimaryEmail, "")
    }
    
    /**
     * Clear stored user email (useful for logout or reset)
     */
    fun clearUserPrimaryEmail() {
        sharedPrefManager.putString(AppConstant.userPrimaryEmail, "")
        Log.d("EmailDetection", "Cleared user primary email")
    }
    
    /**
     * Extension function to validate email format
     */
    private fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }
    
    /**
     * Get email detection statistics for debugging
     */
    fun getDetectionStats(): EmailDetectionStats {
        val userPrimaryEmail = getUserPrimaryEmail()
        val receiverMail = sharedPrefManager.getString(AppConstant.receiverMail, "")
        
        return EmailDetectionStats(
            userPrimaryEmail = userPrimaryEmail,
            currentReceiverMail = receiverMail,
            defaultReceiverEmail = AppConstant.defaultReceiverEmail,
            hasUserPrimaryEmail = userPrimaryEmail.isNotEmpty(),
            isUsingDefault = receiverMail == AppConstant.defaultReceiverEmail
        )
    }
}

/**
 * Data class for email detection statistics
 */
data class EmailDetectionStats(
    val userPrimaryEmail: String,
    val currentReceiverMail: String,
    val defaultReceiverEmail: String,
    val hasUserPrimaryEmail: Boolean,
    val isUsingDefault: Boolean
)
