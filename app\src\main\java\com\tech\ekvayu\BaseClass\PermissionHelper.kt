package com.tech.ekvayu.BaseClass

import android.accessibilityservice.AccessibilityService
import android.content.ComponentName
import android.content.Context
import android.provider.Settings
import android.text.TextUtils
import com.tech.ekvayu.EkService.NewGmailAccessibilityService

/**
 * Helper class to manage permission states and provide centralized permission checking
 */
object PermissionHelper {
    
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    
    /**
     * Check if accessibility service is enabled and update SharedPreferences
     */
    fun checkAndUpdateAccessibilityPermission(context: Context): Boolean {
        val isEnabled = isAccessibilityServiceEnabled(context, NewGmailAccessibilityService::class.java)
        sharedPrefManager.putBoolean(AppConstant.isAccessibilityPermissionGranted, isEnabled)
        return isEnabled
    }
    
    /**
     * Check if mail is configured and update SharedPreferences
     */
    fun checkAndUpdateMailConfiguration(): Boolean {
        val receiverMail = sharedPrefManager.getString(AppConstant.receiverMail, "")
        val isConfigured = receiverMail.isNotEmpty()
        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, isConfigured)
        return isConfigured
    }
    
    /**
     * Get current permission states from SharedPreferences
     */
    fun getPermissionStates(): PermissionStates {
        return PermissionStates(
            isAccessibilityPermissionGranted = sharedPrefManager.getBoolean(AppConstant.isAccessibilityPermissionGranted, false),
            isMailConfigured = sharedPrefManager.getBoolean(AppConstant.isMailConfigured, false),
            receiverMail = sharedPrefManager.getString(AppConstant.receiverMail, "")
        )
    }
    
    /**
     * Check if both permissions are granted
     */
    fun areAllPermissionsGranted(context: Context): Boolean {
        val accessibilityEnabled = checkAndUpdateAccessibilityPermission(context)
        val mailConfigured = checkAndUpdateMailConfiguration()
        return accessibilityEnabled && mailConfigured
    }
    
    /**
     * Reset all permission states (useful for testing or logout)
     */
    fun resetPermissionStates() {
        sharedPrefManager.putBoolean(AppConstant.isAccessibilityPermissionGranted, false)
        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, false)
        sharedPrefManager.putString(AppConstant.receiverMail, "")
    }
    
    private fun isAccessibilityServiceEnabled(context: Context, service: Class<out AccessibilityService>): Boolean {
        val componentName = ComponentName(context, service)
        val enabledServices = Settings.Secure.getString(context.contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)

        if (enabledServices.isNullOrEmpty()) {
            return false
        }

        val colonSplitter = TextUtils.SimpleStringSplitter(':')
        colonSplitter.setString(enabledServices)

        while (colonSplitter.hasNext()) {
            if (colonSplitter.next().equals(componentName.flattenToString(), ignoreCase = true)) {
                return true
            }
        }
        return false
    }
}

/**
 * Data class to hold permission states
 */
data class PermissionStates(
    val isAccessibilityPermissionGranted: Boolean,
    val isMailConfigured: Boolean,
    val receiverMail: String
)
