package com.tech.ekvayu.BaseClass;

import android.content.Context;
import android.content.SharedPreferences;

public class SharedPrefManager {

    private static final String PREF_NAME = "app_prefs";
    private static SharedPrefManager instance;
    private final SharedPreferences preferences;

    // Private constructor to ensure singleton instance
    private SharedPrefManager(Context context) {
        preferences = context.getApplicationContext().getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    // Initialize the SharedPrefManager instance in the Application class
    public static synchronized void initInstance(Context context) {
        if (instance == null) {
            instance = new SharedPrefManager(context.getApplicationContext()); // Use application context
        }
    }

    // Provide global access to the SharedPrefManager instance
    public static SharedPrefManager getInstance() {
        if (instance == null) {
            throw new IllegalStateException("SharedPrefManager is not initialized. Call initInstance() first.");
        }
        return instance;
    }

    // SharedPreferences put and get methods
    public void putString(String key, String value) {
        preferences.edit().putString(key, value).apply();
    }

    public String getString(String key, String defaultValue) {
        return preferences.getString(key, defaultValue);
    }

    public void putInt(String key, int value) {
        preferences.edit().putInt(key, value).apply();
    }

    public int getInt(String key, int defaultValue) {
        return preferences.getInt(key, defaultValue);
    }

    public void putBoolean(String key, boolean value) {
        preferences.edit().putBoolean(key, value).apply();
    }

    public boolean getBoolean(String key, boolean defaultValue) {
        return preferences.getBoolean(key, defaultValue);
    }

    public void remove(String key) {
        preferences.edit().remove(key).apply();
    }

    public void clear() {
        preferences.edit().clear().apply();
    }
}
