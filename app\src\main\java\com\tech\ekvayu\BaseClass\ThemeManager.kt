package com.tech.ekvayu.BaseClass

import android.content.Context
import android.content.res.Configuration
import androidx.appcompat.app.AppCompatDelegate

/**
 * Theme Manager for handling Dark Mode and Light Mode
 */
object ThemeManager {
    
    private const val THEME_PREF_KEY = "app_theme_mode"
    private const val THEME_LIGHT = "light"
    private const val THEME_DARK = "dark"
    private const val THEME_SYSTEM = "system"
    
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    
    enum class ThemeMode {
        LIGHT,
        DARK,
        SYSTEM
    }
    
    /**
     * Apply the selected theme
     */
    fun applyTheme(themeMode: ThemeMode) {
        when (themeMode) {
            ThemeMode.LIGHT -> {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
                saveThemePreference(THEME_LIGHT)
            }
            ThemeMode.DARK -> {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
                saveThemePreference(THEME_DARK)
            }
            ThemeMode.SYSTEM -> {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)
                saveThemePreference(THEME_SYSTEM)
            }
        }
    }
    
    /**
     * Get the current theme mode
     */
    fun getCurrentTheme(): ThemeMode {
        val savedTheme = sharedPrefManager.getString(THEME_PREF_KEY, THEME_SYSTEM)
        return when (savedTheme) {
            THEME_LIGHT -> ThemeMode.LIGHT
            THEME_DARK -> ThemeMode.DARK
            else -> ThemeMode.SYSTEM
        }
    }
    
    /**
     * Initialize theme on app startup
     */
    fun initializeTheme() {
        val currentTheme = getCurrentTheme()
        applyTheme(currentTheme)
    }
    
    /**
     * Check if current theme is dark mode
     */
    fun isDarkMode(context: Context): Boolean {
        return when (getCurrentTheme()) {
            ThemeMode.DARK -> true
            ThemeMode.LIGHT -> false
            ThemeMode.SYSTEM -> {
                val nightModeFlags = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
                nightModeFlags == Configuration.UI_MODE_NIGHT_YES
            }
        }
    }
    
    /**
     * Get theme display name for UI
     */
    fun getThemeDisplayName(themeMode: ThemeMode): String {
        return when (themeMode) {
            ThemeMode.LIGHT -> "Light Mode"
            ThemeMode.DARK -> "Dark Mode"
            ThemeMode.SYSTEM -> "System Default"
        }
    }
    
    /**
     * Get next theme in cycle (for toggle functionality)
     */
    fun getNextTheme(): ThemeMode {
        return when (getCurrentTheme()) {
            ThemeMode.LIGHT -> ThemeMode.DARK
            ThemeMode.DARK -> ThemeMode.SYSTEM
            ThemeMode.SYSTEM -> ThemeMode.LIGHT
        }
    }
    
    /**
     * Toggle between themes
     */
    fun toggleTheme() {
        val nextTheme = getNextTheme()
        applyTheme(nextTheme)
    }
    
    /**
     * Save theme preference
     */
    private fun saveThemePreference(theme: String) {
        sharedPrefManager.putString(THEME_PREF_KEY, theme)
    }
    
    /**
     * Get all available themes
     */
    fun getAllThemes(): List<ThemeMode> {
        return listOf(ThemeMode.LIGHT, ThemeMode.DARK, ThemeMode.SYSTEM)
    }
}
