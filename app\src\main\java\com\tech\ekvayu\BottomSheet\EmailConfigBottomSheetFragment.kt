package com.tech.ekvayu.BottomSheet

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.tech.ekvayu.BaseClass.EmailDetectionHelper
import com.tech.ekvayu.databinding.FragmentEmailConfigBottomSheetBinding

/**
 * Bottom sheet for manual email configuration
 * Allows users to set their primary email address manually
 */
class EmailConfigBottomSheetFragment : BottomSheetDialogFragment() {

    private var _binding: FragmentEmailConfigBottomSheetBinding? = null
    private val binding get() = _binding!!

    companion object {
        private const val TAG = "EmailConfigBottomSheet"
        
        fun newInstance(): EmailConfigBottomSheetFragment {
            return EmailConfigBottomSheetFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentEmailConfigBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupUI()
        loadCurrentEmail()
    }

    private fun setupUI() {
        // Email input validation
        binding.etPrimaryEmail.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            
            override fun afterTextChanged(s: Editable?) {
                val email = s.toString().trim()
                val isValid = isValidEmail(email)
                
                binding.btnSaveEmail.isEnabled = isValid
                
                if (email.isNotEmpty() && !isValid) {
                    binding.tilPrimaryEmail.error = "Please enter a valid email address"
                } else {
                    binding.tilPrimaryEmail.error = null
                }
            }
        })

        // Save button click
        binding.btnSaveEmail.setOnClickListener {
            saveEmail()
        }

        // Cancel button click
        binding.btnCancel.setOnClickListener {
            dismiss()
        }

        // Show current detection stats
        binding.btnShowStats.setOnClickListener {
            showDetectionStats()
        }
    }

    private fun loadCurrentEmail() {
        val currentEmail = EmailDetectionHelper.getUserPrimaryEmail()
        if (currentEmail.isNotEmpty()) {
            binding.etPrimaryEmail.setText(currentEmail)
            binding.tvCurrentEmail.text = "Current: $currentEmail"
        } else {
            binding.tvCurrentEmail.text = "No primary email set"
        }
    }

    private fun saveEmail() {
        val email = binding.etPrimaryEmail.text.toString().trim()
        
        if (isValidEmail(email)) {
            EmailDetectionHelper.updateUserPrimaryEmail(email)
            
            Toast.makeText(
                requireContext(),
                "Primary email updated to: $email",
                Toast.LENGTH_SHORT
            ).show()
            
            Log.d(TAG, "User manually set primary email to: $email")
            dismiss()
        } else {
            Toast.makeText(
                requireContext(),
                "Please enter a valid email address",
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun showDetectionStats() {
        val stats = EmailDetectionHelper.getDetectionStats()
        
        val statsMessage = """
            Email Detection Statistics:
            
            Primary Email: ${if (stats.hasUserPrimaryEmail) stats.userPrimaryEmail else "Not set"}
            Current Receiver: ${stats.currentReceiverMail}
            Default Email: ${stats.defaultReceiverEmail}
            Using Default: ${if (stats.isUsingDefault) "Yes" else "No"}
        """.trimIndent()
        
        binding.tvStats.text = statsMessage
        binding.tvStats.visibility = View.VISIBLE
        
        Log.d(TAG, "Email detection stats: $stats")
    }

    private fun isValidEmail(email: String): Boolean {
        return email.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
