package com.tech.ekvayu.BottomSheet

import android.content.ContentValues.TAG
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.google.android.gms.common.wrappers.Wrappers.packageManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.FragmentSuggetionBottomBinding


class SuggetionBottomFragment : BottomSheetDialogFragment() {
    private lateinit var binding: FragmentSuggetionBottomBinding
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        binding= FragmentSuggetionBottomBinding.inflate(inflater,container,false)

        binding.btGotoGmail.setOnClickListener {
            openGmailApp()
        }
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        // Check if mail is configured when fragment resumes
        checkAndUpdateMailStatus()
    }

    private fun checkAndUpdateMailStatus() {
        val receiverMail = sharedPrefManager.getString(AppConstant.receiverMail, "")
        if (receiverMail.isNotEmpty()) {
            // Mail is configured, save to SharedPreferences and dismiss bottom sheet
            sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
            dismiss()
        } else {
            // Mail is not configured, update SharedPreferences
            sharedPrefManager.putBoolean(AppConstant.isMailConfigured, false)
        }
    }

    private fun openGmailApp() {
        try {
            val packageName = "com.google.android.gm"
            val launchIntent: Intent? =requireContext().packageManager.getLaunchIntentForPackage(packageName)

            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(launchIntent)
                Log.d(TAG, "Successfully attempted to start Gmail app.")
            } else {
                Toast.makeText(requireActivity(), "Gmail app not found on this device.", Toast.LENGTH_LONG).show()
                Log.w(TAG, "Gmail app (package: $packageName) not found or launch intent is null.")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error opening Gmail: ${e.message}", e)
            Toast.makeText(requireActivity(), "An error occurred: ${e.localizedMessage}", Toast.LENGTH_LONG).show()
        }
    }
}