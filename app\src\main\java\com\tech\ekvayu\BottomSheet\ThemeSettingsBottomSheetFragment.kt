package com.tech.ekvayu.BottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.tech.ekvayu.BaseClass.ThemeManager
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.FragmentThemeSettingsBottomSheetBinding

class ThemeSettingsBottomSheetFragment : BottomSheetDialogFragment() {
    
    private var _binding: FragmentThemeSettingsBottomSheetBinding? = null
    private val binding get() = _binding!!
    
    companion object {
        fun newInstance(): ThemeSettingsBottomSheetFragment {
            return ThemeSettingsBottomSheetFragment()
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentThemeSettingsBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupThemeOptions()
        setupClickListeners()
        updateCurrentThemeDisplay()
    }
    
    private fun setupThemeOptions() {
        val currentTheme = ThemeManager.getCurrentTheme()
        
        // Update radio buttons based on current theme
        when (currentTheme) {
            ThemeManager.ThemeMode.LIGHT -> binding.rbLight.isChecked = true
            ThemeManager.ThemeMode.DARK -> binding.rbDark.isChecked = true
            ThemeManager.ThemeMode.SYSTEM -> binding.rbSystem.isChecked = true
        }
    }
    
    private fun setupClickListeners() {
        binding.rgThemeOptions.setOnCheckedChangeListener { _, checkedId ->
            val selectedTheme = when (checkedId) {
                R.id.rbLight -> ThemeManager.ThemeMode.LIGHT
                R.id.rbDark -> ThemeManager.ThemeMode.DARK
                R.id.rbSystem -> ThemeManager.ThemeMode.SYSTEM
                else -> ThemeManager.ThemeMode.SYSTEM
            }
            
            applyTheme(selectedTheme)
        }
        
        binding.btnClose.setOnClickListener {
            dismiss()
        }
        
        binding.btnApply.setOnClickListener {
            val selectedTheme = when (binding.rgThemeOptions.checkedRadioButtonId) {
                R.id.rbLight -> ThemeManager.ThemeMode.LIGHT
                R.id.rbDark -> ThemeManager.ThemeMode.DARK
                R.id.rbSystem -> ThemeManager.ThemeMode.SYSTEM
                else -> ThemeManager.ThemeMode.SYSTEM
            }
            
            applyTheme(selectedTheme)
            dismiss()
        }
    }
    
    private fun applyTheme(themeMode: ThemeManager.ThemeMode) {
        ThemeManager.applyTheme(themeMode)
        
        val themeName = ThemeManager.getThemeDisplayName(themeMode)
        Toast.makeText(requireContext(), "Applied $themeName", Toast.LENGTH_SHORT).show()
        
        updateCurrentThemeDisplay()
    }
    
    private fun updateCurrentThemeDisplay() {
        val currentTheme = ThemeManager.getCurrentTheme()
        val themeName = ThemeManager.getThemeDisplayName(currentTheme)
        binding.tvCurrentTheme.text = "Current: $themeName"
        
        // Update theme preview
        val isDarkMode = ThemeManager.isDarkMode(requireContext())
        binding.tvThemePreview.text = if (isDarkMode) {
            "🌙 Dark Mode Active"
        } else {
            "☀️ Light Mode Active"
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
