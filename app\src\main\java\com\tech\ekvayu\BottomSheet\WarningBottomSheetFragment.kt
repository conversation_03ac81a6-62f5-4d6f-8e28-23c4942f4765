package com.tech.ekvayu.BottomSheet

import android.accessibilityservice.AccessibilityService
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.EkService.NewGmailAccessibilityService
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.FragmentWarningBottomSheetBinding


class WarningBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentWarningBottomSheetBinding
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding= FragmentWarningBottomSheetBinding.inflate(inflater, container, false)
        binding.btPermission.setOnClickListener {
            openAccessibilitySettings(requireActivity())
        }

        return binding.root
    }

    override fun onResume() {
        super.onResume()
        // Check if accessibility permission is granted when fragment resumes
        checkAndUpdatePermissionStatus()
    }



    private fun openAccessibilitySettings(context: Context) {
        val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
        context.startActivity(intent)
    }

    private fun checkAndUpdatePermissionStatus() {
        if (isAccessibilityServiceEnabled(requireActivity(), NewGmailAccessibilityService::class.java)) {
            // Permission is granted, save to SharedPreferences and dismiss bottom sheet
            sharedPrefManager.putBoolean(AppConstant.isAccessibilityPermissionGranted, true)
            dismiss()
        } else {
            // Permission is not granted, update SharedPreferences
            sharedPrefManager.putBoolean(AppConstant.isAccessibilityPermissionGranted, false)
        }
    }

    private fun isAccessibilityServiceEnabled(context: Context, service: Class<out AccessibilityService>): Boolean {
        val componentName = ComponentName(context, service)
        val enabledServices = Settings.Secure.getString(context.contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)

        if (enabledServices.isNullOrEmpty()) {
            return false
        }

        val colonSplitter = TextUtils.SimpleStringSplitter(':')
        colonSplitter.setString(enabledServices)

        while (colonSplitter.hasNext()) {
            if (colonSplitter.next().equals(componentName.flattenToString(), ignoreCase = true)) {
                return true
            }
        }
        return false
    }
}