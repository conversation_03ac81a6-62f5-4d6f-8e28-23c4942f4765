package com.tech.ekvayu.BottomSheet

import android.accessibilityservice.AccessibilityService
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.tech.ekvayu.EkService.NewGmailAccessibilityService
import com.tech.ekvayu.R
import com.tech.ekvayu.databinding.FragmentWarningBottomSheetBinding


class WarningBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentWarningBottomSheetBinding


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding= FragmentWarningBottomSheetBinding.inflate(inflater, container, false)
        binding.btPermission.setOnClickListener {
            if (isAccessibilityServiceEnabled(requireActivity(), NewGmailAccessibilityService::class.java)) {
            } else {
                openAccessibilitySettings(requireActivity())
            }
        }

        return binding.root
    }



    private fun openAccessibilitySettings(context: Context) {
        val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
        context.startActivity(intent)
    }

    private fun isAccessibilityServiceEnabled(context: Context, service: Class<out AccessibilityService>): Boolean {
        val componentName = ComponentName(context, service)
        val enabledServices = Settings.Secure.getString(context.contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)

        if (enabledServices.isNullOrEmpty()) {
            return false
        }

        val colonSplitter = TextUtils.SimpleStringSplitter(':')
        colonSplitter.setString(enabledServices)  // This will not throw NullPointerException now

        while (colonSplitter.hasNext()) {
            if (colonSplitter.next().equals(componentName.flattenToString(), ignoreCase = true)) {
                return true
            }
        }
        return false
    }



}