package com.tech.ekvayu.Dispute

import com.google.gson.annotations.SerializedName

data class DisputeMailistResponse(
    @SerializedName("count"    ) var count    : Int?               = null,
    @SerializedName("next"     ) var next     : String?            = null,
    @SerializedName("previous" ) var previous : String?            = null,
    @SerializedName("results"  ) var results  : ArrayList<Results> = arrayListOf()
)


data class Comments (

    @SerializedName("comment_type" ) var commentType : String? = null,
    @SerializedName("comment"      ) var comment     : String? = null,
    @SerializedName("created_at"   ) var createdAt   : String? = null

)



data class Results (

    @SerializedName("dispute_id"        ) var disputeId       : String?             = null,
    @SerializedName("msg_id"            ) var msgId           : String?             = null,
    @SerializedName("recievers_email"   ) var recieversEmail  : String?             = null,
    @SerializedName("senders_email"     ) var sendersEmail    : String?             = null,
    @SerializedName("subject"           ) var subject         : String?             = null,
    @SerializedName("status"            ) var status          : String?             = null,
    @SerializedName("counter"           ) var counter         : Int?                = null,
    @SerializedName("overall_ai_status" ) var overallAiStatus : String?             = null,
    @SerializedName("emaildetails_id"   ) var emaildetailsId  : Int?                = null,
    @SerializedName("comments"          ) var comments        : ArrayList<Comments> = arrayListOf()

)
