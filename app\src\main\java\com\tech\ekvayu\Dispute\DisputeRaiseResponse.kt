package com.tech.ekvayu.Dispute

import com.google.gson.annotations.SerializedName

data class DisputeRaiseResponse(
    @SerializedName("dispute_id"      ) var disputeId     : Int?    = null,
    @SerializedName("email"           ) var email         : String? = null,
    @SerializedName("hash_id"         ) var hashId        : String? = null,
    @SerializedName("status"          ) var status        : String? = null,
    @SerializedName("counter"         ) var counter       : Int?    = null,
    @SerializedName("user_comment"    ) var userComment   : String? = null,
    @SerializedName("dispute_info_id" ) var disputeInfoId : Int?    = null,
    @SerializedName("code"            ) var code          : Int?    = null,
    @SerializedName("message"         ) var message       : String? = null

)
