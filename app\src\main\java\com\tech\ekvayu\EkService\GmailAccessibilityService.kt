package com.tech.ekvayu.EkService


import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.EmailResponse
import com.tech.ekvayu.Response.HashResponse
import com.tech.ekvayu.Response.PedingMailRes
import com.tech.ekvayu.databinding.LayoutProgressBinding
import com.tech.ekvayu.databinding.LayoutValidationBinding
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.FileReader
import java.io.IOException
import kotlin.String


class GmailAccessibilityService : AccessibilityService() {

    private var currentPopupView: View? = null
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

  /*  override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
            if (event.packageName == "com.google.android.gm") {
                Handler(Looper.getMainLooper()).postDelayed({
                    val rootNode = rootInActiveWindow
                    if (rootNode != null) {
                      //  Log.d("getDescription", "onAccessibilityEvent: "+rootNode.text)
                        extractEmailDetails(rootNode)
                    }
                }, 500) // 0.5 -second delay
            }
        }
    }*/

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null || event.packageName != "com.google.android.gm") return

        when (event.eventType) {
            AccessibilityEvent.TYPE_VIEW_CLICKED,
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED,
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                Handler(Looper.getMainLooper()).postDelayed({
                    val rootNode = rootInActiveWindow
                    if (rootNode != null) {
                        Log.d("AccessDebug", "Root node found. Starting scan.")
                        extractEmailDetails(rootNode)
                    } else {
                        Log.e("AccessDebug", "rootInActiveWindow is NULL")
                    }
                }, 500)
            }
        }
    }



 /*   fun parseEmailData(rootNode: AccessibilityNodeInfo) {


        val emailBodyBuilder = StringBuilder()
        val toEmails = mutableSetOf<String>()
        val ccEmails = mutableSetOf<String>()
        val bccEmails = mutableSetOf<String>()
        var subject: String? = null
        var fromEmail: String? = null
        var senderName: String? = null
        var date: String? = null
        var attachments: String? = null


        fun traverse(node: AccessibilityNodeInfo?) {
            if (node == null) return

            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            val actualText = text ?: contentDesc ?: ""

            // === Subject ===
            if (subject == null && (viewId?.contains("subject", true) == true || actualText.startsWith("Subject", true))) {
                subject = actualText.removePrefix("Subject:").trim()
            }

            // === From (Name + Email) ===
            if (fromEmail == null && actualText.contains("•")) {
                val parts = actualText.split("•").map { it.trim() }
                if (parts.size == 2) {
                    senderName = parts[0]
                    fromEmail = parts[1].extractEmail()
                }
            }

            // === TO Emails (Strict) ===
            if (toEmails.isEmpty() && (actualText.startsWith("To:", true) || viewId?.contains("to", true) == true)) {
                toEmails.addAll(actualText.extractEmails())
            }

            // === CC Emails ===
            if (actualText.startsWith("Cc:", true)) {
                ccEmails.addAll(actualText.extractEmails())
            }

            // === BCC Emails ===
            if (actualText.startsWith("Bcc:", true)) {
                bccEmails.addAll(actualText.extractEmails())
            }

            val datePattern = Regex("""\b\d{1,2} ?[A-Za-z]{3,9} ?\d{4}( at \d{1,2}:\d{2} ?[APMapm]{2})?\b""")

            if (date == null) {
                val match = datePattern.find(actualText)
                if (match != null) {
                    date = match.value.trim()
                    Log.d("DateExtract", "Detected date: $date")
                }
            }
            // === Body ===
            if (actualText.length > 50 &&
                !actualText.contains("@") &&
                !actualText.startsWith("To", true) &&
                !actualText.startsWith("Cc", true) &&
                !actualText.startsWith("Bcc", true) &&
                !actualText.startsWith("Subject", true) &&
                !actualText.contains("Attachment", true)
            ) {
                emailBodyBuilder.appendLine(actualText)
            }

            val attachmentFilePattern = Regex("[\\w\\-\\s()]+\\.(pdf|docx?|xlsx?|pptx?|csv|txt|zip)", RegexOption.IGNORE_CASE)

            if (attachments == null) {
                val matches = attachmentFilePattern.findAll(actualText)
                    .map { it.value.trim() }
                    .toList()

                if (matches.isNotEmpty()) {
                    attachments = "Attachments found: ${matches.joinToString(", ")}"
                    Log.d("AttachmentScan", "Detected attachment(s): $attachments")
                }
            }

            // Recursively process child nodes
            for (i in 0 until node.childCount) {
                traverse(node.getChild(i))
            }
        }

        // Start traversal
        traverse(rootNode)

        Log.d("ParsedEmail", "Subject: $subject")
        Log.d("ParsedEmail", "From: $senderName <$fromEmail>")
        Log.d("ParsedEmail", "To: $toEmails")
        Log.d("ParsedEmail", "Cc: $ccEmails")
        Log.d("ParsedEmail", "Bcc: $bccEmails")
        Log.d("ParsedEmail", "Date: $date")
        Log.d("ParsedEmail", "Body: ${emailBodyBuilder.toString().trim()}")
        Log.d("ParsedEmail", "Attachments: $attachments")
    }
*/


    private fun extractEmailDetails(rootNode: AccessibilityNodeInfo) {
        var senderName: String? = null
        var fromEmail: String? = null

        var subject: String? = null
        var date: String? = null
        var attachments: String? = null
        val emailBodyBuilder = StringBuilder()

        val toEmails = mutableListOf<String>()
        val ccEmails = mutableListOf<String>()
        val bccEmails = mutableListOf<String>()

        val possibleNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, possibleNodes)

        for (node in possibleNodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            val actualText = text ?: contentDesc ?: continue

            // === Subject ===
            if (subject == null && (viewId?.contains("subject", true) == true || actualText.startsWith("Subject", true))) {
                subject = actualText.removePrefix("Subject:").trim()
            }

            // === From (Name + Email) ===
            if (fromEmail == null && actualText.contains("•")) {
                val parts = actualText.split("•").map { it.trim() }
                if (parts.size == 2) {
                    senderName = parts[0]
                    fromEmail = parts[1].extractEmail()
                }
            }

            // === TO Emails ===
            if ((viewId?.contains("to", true) == true || actualText.contains("To:", true)) && actualText.contains("@")) {
                toEmails.addAll(actualText.extractEmails())
            }

            if (actualText.isValidEmail() && !toEmails.contains(actualText)) {
                toEmails.add(actualText)
            }

            if (viewId?.endsWith("/to") == true) {
                toEmails.addAll(actualText.extractEmails())
            }

            // === Cc Emails ===
            if (actualText.startsWith("Cc:", true)) {
                ccEmails.addAll(actualText.extractEmails())
            }

            // === Bcc Emails ===
            if (actualText.startsWith("Bcc:", true)) {
                bccEmails.addAll(actualText.extractEmails())
            }

            // === Date Detection ===
            if (date == null && (
                        actualText.contains("AM") || actualText.contains("PM") ||
                                actualText.matches(Regex(".*\\d{1,2} [A-Za-z]+ \\d{4}.*"))
                        )
            ) {
                date = actualText
            }

            // === Body Text Extraction ===
            if (actualText.length > 50 &&
                !actualText.contains("@") &&
                !actualText.startsWith("To", true) &&
                !actualText.startsWith("Cc", true) &&
                !actualText.startsWith("Bcc", true)
            ) {
                emailBodyBuilder.appendLine(actualText)
            }

            // === Attachment Detection ===
            if (attachments == null && (
                        actualText.contains("Attachment", true) ||
                                actualText.contains(".pdf", true) ||
                                actualText.contains(".docx", true) ||
                                actualText.contains("Download", true))
            ) {
                attachments = "Attachments found"
            }
        }

        // Fallback: If To email is missing but From email is present, assume it's a self-sent email
        if (toEmails.isEmpty() && !fromEmail.isNullOrEmpty() && fromEmail.isValidEmail()) {
            toEmails.add(fromEmail)
            Log.d("FallbackToSelf", "To email was missing; used fromEmail as toEmail: $fromEmail")
        }

        // === Finalize Data ===
        subject = subject ?: "No Subject"
        fromEmail = fromEmail ?: "Unknown Sender"
        date = date ?: "Unknown Date"
        val body = if (emailBodyBuilder.isNotEmpty()) emailBodyBuilder.toString().trim() else "No Body Content"
        attachments = attachments ?: "No Attachments"
        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)

        Log.d("EmailDetails", """
        senderName: $senderName
        fromEmail: $fromEmail
        toEmail: ${toEmails.joinToString(", ")}
        cc: ${ccEmails.joinToString(", ")}
        bcc: ${bccEmails.joinToString(", ")}
        subject: $subject
        date: $date
        deviceId: $androidId
        body: $body
        attachments: $attachments
    """.trimIndent())

        val emailContent = """
        fromEmail: $fromEmail
        toEmail: ${toEmails.joinToString(", ")}
        cc: ${ccEmails.joinToString(", ")}
        bcc: ${bccEmails.joinToString(", ")}
        subject: $subject
        date: $date
        body: $body
        attachments: $attachments
        Content-Type: text/plain; charset=UTF-8 
    """.trimIndent()

        val file = File(applicationContext.filesDir, "email.eml")
        try {
            val fos = FileOutputStream(file)
            fos.write(emailContent.toByteArray())
            fos.close()
            val emlContent = readEmlFile(file.absolutePath)
            Log.d("getEmailContent", "EML File Content: $emlContent")
        } catch (e: IOException) {
            e.printStackTrace()
        }

        // Proceed to server upload if data is valid
        if (toEmails.isNotEmpty() && fromEmail != "Unknown Sender") {
            sharedPrefManager.putString(AppConstant.receiverMail, toEmails[0])
            getHashId(
                file,
                toEmails[0],
                fromEmail,
                ccEmails,
                bccEmails,
                subject,
                date,
                body,
                attachments
            )
        }
    }

    fun String.extractEmails(): List<String> {
        val emailRegex = "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}".toRegex()
        return emailRegex.findAll(this).map { it.value }.toList()
    }

    fun String.extractEmail(): String? = this.extractEmails().firstOrNull()

    fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+"))
    }


    private fun getHashId(emlContent:File,email: String, fromEmail: String, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String, attachments: String) {

        if (hasOverlayPermission(applicationContext)) {

            val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
            val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

            val tomail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), email.toString())
            val senderEmail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), fromEmail.toString())

            val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
            val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
            val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)

            val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
            val retrofit = ApiClient.getRetrofitInstance(applicationContext)
            val apiService = retrofit!!.create(ApiService::class.java)
            val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
            val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val binding = LayoutProgressBinding.inflate(layoutInflater)
            val view = binding.root
            view.setBackgroundResource(R.drawable.bg_alert_dialog)
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else
                    WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                PixelFormat.TRANSLUCENT
            )
            params.dimAmount = 0.5f
            windowManager.addView(view, params)


            apiService.getHashKey(tomail,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
                .enqueue(object : Callback<HashResponse> {
                    override fun onResponse(
                        call: Call<HashResponse>,
                        response: Response<HashResponse>
                    ) {
                        windowManager.removeView(view)

                        if (response.body()!!.Code.toString()=="1")
                        {
                            windowManager.removeView(view)
                            sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                            checkAiResponse(email,response.body()?.hashId.toString())
                        }

                        else
                        {
                            windowManager.removeView(view)
                            uploadFile(email,fromEmail,emlContent,cc,bcc,subject,date,body)
                        }
                    }

                    override fun onFailure(call: Call<HashResponse>, t: Throwable) {
                        windowManager.removeView(view)
                    }
                })
        }

        else
        {
            requestOverlayPermission(applicationContext)
        }
    }


/*
    private fun uploadFile(email: String, fromEmail: String, emlContent: File, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String) {

        val emailRequestBody = RequestBody.create("text/plain".toMediaTypeOrNull(), email)
        val senderEmail = RequestBody.create("text/plain".toMediaTypeOrNull(), fromEmail.toString())
        val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
        val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

        val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
        val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
        val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)


        val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
        val binding = LayoutProgressBinding.inflate(layoutInflater)
        val view = binding.root
        view.setBackgroundResource(R.drawable.bg_alert_dialog)
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            android.graphics.PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f
        windowManager.addView(view, params)


        apiService.uploadFile(emailRequestBody,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
            .enqueue(object : Callback<EmailResponse> {
                override fun onResponse(
                    call: Call<EmailResponse>,
                    response: Response<EmailResponse>
                ) {
                    windowManager.removeView(view)
                    if (response.body()!!.Code==1)
                    {
                        windowManager.removeView(view)
                        sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                      //  setResponsePopup(response.body()?.unsafeReasons,response.body()?.emailStatus.toString(),email,response.body()?.hashId.toString())
                        checkAiResponse(email,response.body()?.hashId.toString())
                    }
                    else
                    {
                        windowManager.removeView(view)
                    }
                }

                override fun onFailure(call: Call<EmailResponse?>, t: Throwable) {
                    windowManager.removeView(view)
                }

            })
    }
    */

    private fun uploadFile(email: String, fromEmail: String, emlContent: File, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String) {

        val emailRequestBody = RequestBody.create("text/plain".toMediaTypeOrNull(), email)
        val senderEmail = RequestBody.create("text/plain".toMediaTypeOrNull(), fromEmail.toString())
        val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
        val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

        val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
        val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
        val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)


        val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)


        apiService.uploadFile(emailRequestBody,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
            .enqueue(object : Callback<EmailResponse> {
                override fun onResponse(
                    call: Call<EmailResponse>,
                    response: Response<EmailResponse>
                ) {

                    if (response.body()!!.Code==1)
                    {
                        sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                        //  setResponsePopup(response.body()?.unsafeReasons,response.body()?.emailStatus.toString(),email,response.body()?.hashId.toString())
                        checkAiResponse(email,response.body()?.hashId.toString())
                    }
                    else
                    {
                        Toast.makeText(applicationContext, ""+response.body()!!.message, Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<EmailResponse?>, t: Throwable) {

                }

            })
    }



    /*   private fun checkAiResponse(email: String, hashId: String) {

           val retrofit = ApiClient.getRetrofitInstance(applicationContext)
           val apiService = retrofit!!.create(ApiService::class.java)

           val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
           val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
           val binding = LayoutProgressBinding.inflate(layoutInflater)
           val view = binding.root
           view.setBackgroundResource(R.drawable.bg_alert_dialog)
           val params = WindowManager.LayoutParams(
               WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
               if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                   WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
               else
                   WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
               android.graphics.PixelFormat.TRANSLUCENT
           )
           params.dimAmount = 0.5f
           windowManager.addView(view, params)

           val request = PendingMailRequest(email = email, hashId = hashId)

           apiService.pendingMailStatusAi(request)
               .enqueue(object : Callback<PedingMailRes> {
                   override fun onResponse(
                       call: Call<PedingMailRes>,
                       response: Response<PedingMailRes>
                   ) {
                       windowManager.removeView(view)
                       if (response.body()!!.data!!.code==1)
                       {
                           windowManager.removeView(view)
                           setResponsePopup(response.body()?.data?.unsafeReasons.toString(),response.body()?.data?.emlStatus.toString(),email,hashId)
                       }
                       else
                       {
                           windowManager.removeView(view)
                       }
                   }

                   override fun onFailure(call: Call<PedingMailRes?>, t: Throwable) {
                       windowManager.removeView(view)
                   }
               })
       }
   */

    private fun checkAiResponse(email: String, hashId: String) {
        // Get system services
        val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // Inflate loading layout
        val binding = LayoutProgressBinding.inflate(layoutInflater)
        val view = binding.root
       // view.setBackgroundResource(R.drawable.bg_alert_dialog)

        // Define layout params
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        // Show loading overlay
        windowManager.addView(view, params)

        val handler = Handler(Looper.getMainLooper())
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        var retryCount = 0
        val maxRetries = 10  // Optional: limit max retries to avoid infinite loop

        // Polling function
        fun pollStatus() {
            if (retryCount >= maxRetries) {
                windowManager.removeView(view)
                Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                return
            }

            val request = PendingMailRequest(email = email, hashId = hashId)

            apiService.pendingMailStatusAi(request)
                .enqueue(object : Callback<PedingMailRes> {
                    override fun onResponse(call: Call<PedingMailRes>, response: Response<PedingMailRes>) {
                        val data = response.body()?.data
                        val status = data?.emlStatus?.lowercase()?.trim()

                        Log.d("checkAiResponse", "Polling attempt $retryCount: status=$status")

                        if (status == "safe" || status == "unsafe") {
                            windowManager.removeView(view)
                            setResponsePopup(
                                unsafeReasons = data.unsafeReasons.toString(),
                                mailStatus = status,
                                email = email,
                                hashId = hashId
                            )
                        }
                        else if (retryCount==10 && status == "pending")
                        {
                            windowManager.removeView(view)
                            Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                        }

                        else {
                            retryCount++
                            handler.postDelayed({ pollStatus() }, 3000) // Retry after 3 seconds
                        }
                    }

                    override fun onFailure(call: Call<PedingMailRes>, t: Throwable) {
                        Log.e("checkAiResponse", "API call failed: ${t.message}")
                        retryCount++
                        handler.postDelayed({ pollStatus() }, 3000)
                    }
                })
        }

        // Start polling
        pollStatus()
    }



    private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {

        if (!hasOverlayPermission(applicationContext)) {
            requestOverlayPermission(applicationContext)
            return
        }

        val windowManager = applicationContext.getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = applicationContext.getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val binding = LayoutValidationBinding.inflate(layoutInflater)


        currentPopupView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        // Configure popup UI
        when (mailStatus) {
            "safe" -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
            }
            "pending" -> {
                binding.lvWarning.setAnimation(R.raw.wait)
                binding.btClose.visibility = View.VISIBLE
            }
            "unsafe" -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
            }
        }

        val view = binding.root
        view.setBackgroundResource(R.drawable.bg_alert_dialog)

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        binding.tvMessage.text = unsafeReasons
        binding.btClose.setOnClickListener {
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        windowManager.addView(view, params)
        currentPopupView = view // Track the current popup
    }

    private fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    private fun requestOverlayPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(context)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$context.packageName")
                )
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            }
        }
    }

    private fun readEmlFile(filePath: String): String? {
        val file = File(filePath)
        if (!file.exists()) {
            return null
        }
        val stringBuilder = StringBuilder()
        try {
            val reader = BufferedReader(FileReader(file))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                stringBuilder.append(line).append("\n")
            }
            reader.close()
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
        return stringBuilder.toString()
    }



    private fun findEmailNodes(node: AccessibilityNodeInfo, emailNodes: MutableList<AccessibilityNodeInfo>) {
        if (node.className == "android.widget.TextView" && node.text != null) {
            emailNodes.add(node)
        }
        for (i in 0 until node.childCount) {
            findEmailNodes(node.getChild(i), emailNodes)
        }
    }



    override fun onInterrupt() {

    }




}
