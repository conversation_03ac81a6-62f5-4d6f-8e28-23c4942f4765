package com.tech.ekvayu.EkService


import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.PendingMailRequest
import com.tech.ekvayu.Response.EmailResponse
import com.tech.ekvayu.Response.HashResponse
import com.tech.ekvayu.Response.PedingMailRes
import com.tech.ekvayu.databinding.LayoutProgressBinding
import com.tech.ekvayu.databinding.LayoutValidationBinding
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.FileReader
import java.io.IOException
import kotlin.String


class NewGmailAccessibilityService : AccessibilityService() {

    private var currentPopupView: View? = null
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return

        if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
            if (event.packageName == "com.google.android.gm") {
                Handler(Looper.getMainLooper()).postDelayed({
                    val rootNode = rootInActiveWindow
                    try {
                        val root = rootInActiveWindow
                        if (root != null) extractEmailDetails(root)

                    } catch (e: Exception) {
                        Log.e("AccessibilityCrash", "Error in service: ${e.message}")
                    }


                }, 500) // 0.5 -second delay
            }
        }
    }

    private fun extractEmailDetails(rootNode: AccessibilityNodeInfo) {
        var senderName: String? = null
        var fromEmail: String? = null

        var subject: String? = null
        var date: String? = null
        var attachments: String? = null
        val emailBodyBuilder = StringBuilder()

        val toEmails = mutableSetOf<String>() // 🔁 Use Set to avoid duplicates
        val ccEmails = mutableSetOf<String>()
        val bccEmails = mutableSetOf<String>()

        val possibleNodes = mutableListOf<AccessibilityNodeInfo>()
        findEmailNodes(rootNode, possibleNodes)

        for (node in possibleNodes) {
            val text = node.text?.toString()?.trim()
            val viewId = node.viewIdResourceName
            val contentDesc = node.contentDescription?.toString()?.trim()

            val actualText = text ?: contentDesc ?: continue

            // Debug
            Log.d("NodeScan", "Text: $text | ViewId: $viewId | Desc: $contentDesc")

            // Subject
            if (subject == null && (viewId?.contains("subject", true) == true || actualText.startsWith("Subject", true))) {
                subject = actualText.removePrefix("Subject:").trim()
            }

            // From (Name + Email)
            if (fromEmail == null && actualText.contains("•")) {
                val parts = actualText.split("•").map { it.trim() }
                if (parts.size == 2) {
                    senderName = parts[0]
                    fromEmail = parts[1].extractEmail()
                }
            }

            // 🔁 Handle "To: me" or "To: yourself"
            if (actualText.startsWith("To:", true)) {
                val extracted = actualText.extractEmails()
                if (extracted.isNotEmpty()) {
                    toEmails.addAll(extracted)
                } else if (actualText.contains("me", true) || actualText.contains("yourself", true)) {
                    // Fallback: Assume sending to self, so use fromEmail
                    if (fromEmail != null && fromEmail.isValidEmail()) {
                        toEmails.add(fromEmail)
                    }
                }
            }

            // 🔁 Extra fallback when "To:" is hidden
            if (viewId?.contains("to", true) == true && actualText.contains("@")) {
                toEmails.addAll(actualText.extractEmails())
            }

            // General email detection
            if (actualText.isValidEmail() && !toEmails.contains(actualText)) {
                toEmails.add(actualText)
            }

            // Cc & Bcc
            if (actualText.startsWith("Cc:", true)) {
                ccEmails.addAll(actualText.extractEmails())
            }

            if (actualText.startsWith("Bcc:", true)) {
                bccEmails.addAll(actualText.extractEmails())
            }

            // Date
            if (date == null && (actualText.contains("AM") || actualText.contains("PM")
                        || actualText.matches(Regex(".*\\d{1,2} [A-Za-z]+ \\d{4}.*")))
            ) {
                date = actualText
            }

            // Body
            if (actualText.length > 50 &&
                !actualText.contains("@") &&
                !actualText.startsWith("To", true) &&
                !actualText.startsWith("Cc", true) &&
                !actualText.startsWith("Bcc", true)
            ) {
                emailBodyBuilder.appendLine(actualText)
            }

            // Attachments
            if (attachments == null && (
                        actualText.contains("Attachment", true) ||
                                actualText.contains(".pdf", true) ||
                                actualText.contains(".docx", true) ||
                                actualText.contains("Download", true))
            ) {
                attachments = "Attachments found"
            }
        }

        // Fallbacks
        subject = subject ?: "No Subject"
        fromEmail = fromEmail ?: "Unknown Sender"
        date = date ?: "Unknown Date"
        val body = if (emailBodyBuilder.isNotEmpty()) emailBodyBuilder.toString().trim() else "No Body Content"
        attachments = attachments ?: "No Attachments"

        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)

        Log.d("EmailDetails", """
        senderName: $senderName
        fromEmail: $fromEmail
        toEmail: ${toEmails.joinToString(", ")}
        cc: ${ccEmails.joinToString(", ")}
        bcc: ${bccEmails.joinToString(", ")}
        subject: $subject
        date: $date
        deviceId: $androidId
        body: $body
        attachments: $attachments
    """.trimIndent())

        val emailContent = """
        From: $fromEmail
        To: ${toEmails.joinToString(", ")}
        Cc: ${ccEmails.joinToString(", ")}
        Bcc: ${bccEmails.joinToString(", ")}
        Subject: $subject
        Date: $date
        MIME-Version: 1.0
        Content-Type: text/plain; charset=UTF-8

        $body
    """.trimIndent()

        val file = File(applicationContext.filesDir, "email.eml")
        try {
            FileOutputStream(file).use { it.write(emailContent.toByteArray()) }
            readEmlFile(file.absolutePath)?.let {
                Log.d("getEmailContent", "saveEmailAsEml: $it")
            }
        } catch (e: IOException) {
            Log.e("FileWriteError", "Failed to write eml file", e)
        }


        // Final condition check
        if (toEmails.isNotEmpty() && fromEmail != "Unknown Sender") {
            getHashId(file, toEmails.first(), fromEmail, ccEmails.toList(), bccEmails.toList(), subject, date, body, attachments)
        }

        if (fromEmail != "Unknown Sender" && toEmails.isEmpty()){
            getHashId(file, "<EMAIL>", fromEmail, ccEmails.toList(), bccEmails.toList(), subject, date, body, attachments)
        }

    }

    fun String.extractEmails(): List<String> {
        return Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}").findAll(this).map { it.value }.toList()
    }

    fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"))
    }
    fun String.extractEmail(): String? = this.extractEmails().firstOrNull()

    private fun getHashId(emlContent:File,email: String, fromEmail: String, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String, attachments: String) {

        if (hasOverlayPermission(applicationContext)) {

            val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
            val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

            val tomail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), email.toString())
            val senderEmail = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), fromEmail.toString())

            val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
            val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
            val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)

            val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
            val retrofit = ApiClient.getRetrofitInstance(applicationContext)
            val apiService = retrofit!!.create(ApiService::class.java)
            val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
            val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
            val binding = LayoutProgressBinding.inflate(layoutInflater)
            val view = binding.root
            view.setBackgroundResource(R.drawable.bg_alert_dialog)
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else
                    WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                android.graphics.PixelFormat.TRANSLUCENT
            )
            params.dimAmount = 0.5f
            windowManager.addView(view, params)


            apiService.getHashKey(tomail,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
                .enqueue(object : Callback<HashResponse> {
                    override fun onResponse(
                        call: Call<HashResponse>,
                        response: Response<HashResponse>
                    ) {
                        windowManager.removeView(view)

                        if (response.body()!!.Code.toString()=="1")
                        {
                            windowManager.removeView(view)
                            sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                            sharedPrefManager.putString(AppConstant.receiverMail,email)
                            // Mark mail as configured when we successfully process an email
                            sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                            checkAiResponse(email,response.body()?.hashId.toString())
                        }

                        else
                        {
                            windowManager.removeView(view)
                            uploadFile(email,fromEmail,emlContent,cc,bcc,subject,date,body)
                        }
                    }

                    override fun onFailure(call: Call<HashResponse>, t: Throwable) {
                        windowManager.removeView(view)
                    }
                })
        }

        else
        {
            requestOverlayPermission(applicationContext)
        }
    }


    private fun uploadFile(email: String, fromEmail: String, emlContent: File, cc: List<String>, bcc: List<String>, subject: String, date: String, body: String) {

        val emailRequestBody = RequestBody.create("text/plain".toMediaTypeOrNull(), email)
        val senderEmail = RequestBody.create("text/plain".toMediaTypeOrNull(), fromEmail.toString())
        val fileRequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), emlContent)
        val bodyReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), body)

        val ccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), cc.toString())
        val bccReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), bcc.toString())
        val subjectReqBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), subject)


        val filePart = MultipartBody.Part.createFormData("file", emlContent.name, fileRequestBody)
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)


        apiService.uploadFile(emailRequestBody,senderEmail,bodyReqBody,ccReqBody,bccReqBody,subjectReqBody)
            .enqueue(object : Callback<EmailResponse> {
                override fun onResponse(
                    call: Call<EmailResponse>,
                    response: Response<EmailResponse>
                ) {

                    if (response.body()!!.Code==1)
                    {
                        sharedPrefManager.putString(AppConstant.hashId,response.body()?.hashId.toString())
                        sharedPrefManager.putString(AppConstant.receiverMail,email)
                        // Mark mail as configured when we successfully process an email
                        sharedPrefManager.putBoolean(AppConstant.isMailConfigured, true)
                        //  setResponsePopup(response.body()?.unsafeReasons,response.body()?.emailStatus.toString(),email,response.body()?.hashId.toString())
                        checkAiResponse(email,response.body()?.hashId.toString())
                    }
                    else
                    {
                        Toast.makeText(applicationContext, ""+response.body()!!.message, Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<EmailResponse?>, t: Throwable) {

                }

            })
    }



    /*   private fun checkAiResponse(email: String, hashId: String) {

           val retrofit = ApiClient.getRetrofitInstance(applicationContext)
           val apiService = retrofit!!.create(ApiService::class.java)

           val windowManager =getSystemService(WINDOW_SERVICE) as WindowManager
           val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as android.view.LayoutInflater
           val binding = LayoutProgressBinding.inflate(layoutInflater)
           val view = binding.root
           view.setBackgroundResource(R.drawable.bg_alert_dialog)
           val params = WindowManager.LayoutParams(
               WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT,
               if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                   WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
               else
                   WindowManager.LayoutParams.TYPE_PHONE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
               android.graphics.PixelFormat.TRANSLUCENT
           )
           params.dimAmount = 0.5f
           windowManager.addView(view, params)

           val request = PendingMailRequest(email = email, hashId = hashId)

           apiService.pendingMailStatusAi(request)
               .enqueue(object : Callback<PedingMailRes> {
                   override fun onResponse(
                       call: Call<PedingMailRes>,
                       response: Response<PedingMailRes>
                   ) {
                       windowManager.removeView(view)
                       if (response.body()!!.data!!.code==1)
                       {
                           windowManager.removeView(view)
                           setResponsePopup(response.body()?.data?.unsafeReasons.toString(),response.body()?.data?.emlStatus.toString(),email,hashId)
                       }
                       else
                       {
                           windowManager.removeView(view)
                       }
                   }

                   override fun onFailure(call: Call<PedingMailRes?>, t: Throwable) {
                       windowManager.removeView(view)
                   }
               })
       }
   */

    private fun checkAiResponse(email: String, hashId: String) {
        // Get system services
        val windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // Inflate loading layout
        val binding = LayoutProgressBinding.inflate(layoutInflater)
        val view = binding.root
       // view.setBackgroundResource(R.drawable.bg_alert_dialog)

        // Define layout params
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        // Show loading overlay
        windowManager.addView(view, params)

        val handler = Handler(Looper.getMainLooper())
        val retrofit = ApiClient.getRetrofitInstance(applicationContext)
        val apiService = retrofit!!.create(ApiService::class.java)

        var retryCount = 0
        val maxRetries = 10  // Optional: limit max retries to avoid infinite loop

        // Polling function
        fun pollStatus() {
            if (retryCount >= maxRetries) {
                windowManager.removeView(view)
                Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                return
            }

            val request = PendingMailRequest(email = email, hashId = hashId)

            apiService.pendingMailStatusAi(request)
                .enqueue(object : Callback<PedingMailRes> {
                    override fun onResponse(call: Call<PedingMailRes>, response: Response<PedingMailRes>) {
                        val data = response.body()?.data
                        val status = data?.emlStatus?.lowercase()?.trim()

                        Log.d("checkAiResponse", "Polling attempt $retryCount: status=$status")

                        if (status == "safe" || status == "unsafe") {
                            windowManager.removeView(view)
                            setResponsePopup(
                                unsafeReasons = data.unsafeReasons.toString(),
                                mailStatus = status,
                                email = email,
                                hashId = hashId
                            )
                        }
                        else if (retryCount==10 && status == "pending")
                        {
                            windowManager.removeView(view)
                            Toast.makeText(applicationContext, "Unable to get a response. Try again later.", Toast.LENGTH_LONG).show()
                        }

                        else {
                            retryCount++
                            handler.postDelayed({ pollStatus() }, 3000) // Retry after 3 seconds
                        }
                    }

                    override fun onFailure(call: Call<PedingMailRes>, t: Throwable) {
                        Log.e("checkAiResponse", "API call failed: ${t.message}")
                        retryCount++
                        handler.postDelayed({ pollStatus() }, 3000)
                    }
                })
        }

        // Start polling
        pollStatus()
    }



    private fun setResponsePopup(unsafeReasons: String?, mailStatus: String, email: String, hashId: String) {

        if (!hasOverlayPermission(applicationContext)) {
            requestOverlayPermission(applicationContext)
            return
        }

        val windowManager = applicationContext.getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater = applicationContext.getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val binding = LayoutValidationBinding.inflate(layoutInflater)


        currentPopupView?.let {
            try {
                windowManager.removeView(it)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        // Configure popup UI
        when (mailStatus) {
            "safe" -> {
                binding.lvWarning.setAnimation(R.raw.safe)
                binding.btClose.visibility = View.VISIBLE
            }
            "pending" -> {
                binding.lvWarning.setAnimation(R.raw.wait)
                binding.btClose.visibility = View.VISIBLE
            }
            "unsafe" -> {
                binding.lvWarning.setAnimation(R.raw.unsafe)
                binding.btClose.visibility = View.VISIBLE
            }
        }

        val view = binding.root
        view.setBackgroundResource(R.drawable.bg_alert_dialog)

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_DIM_BEHIND,
            PixelFormat.TRANSLUCENT
        )
        params.dimAmount = 0.5f

        binding.tvMessage.text = unsafeReasons
        binding.btClose.setOnClickListener {
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            currentPopupView = null
        }

        windowManager.addView(view, params)
        currentPopupView = view // Track the current popup
    }

    private fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    private fun requestOverlayPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(context)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$context.packageName")
                )
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            }
        }
    }

    private fun readEmlFile(filePath: String): String? {
        val file = File(filePath)
        if (!file.exists()) {
            return null
        }
        val stringBuilder = StringBuilder()
        try {
            val reader = BufferedReader(FileReader(file))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                stringBuilder.append(line).append("\n")
            }
            reader.close()
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
        return stringBuilder.toString()
    }

 /*   fun String.isValidEmail(): Boolean {
        return this.matches(Regex("[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+"))
    }*/

    // Extract all emails from a string
 /*   private fun String.extractEmails(): List<String> {
        val regex = "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}".toRegex()
        return regex.findAll(this).map { it.value }.toList()
    }*/

    private fun findEmailNodes(node: AccessibilityNodeInfo?, emailNodes: MutableList<AccessibilityNodeInfo>) {
        if (node == null) return

        if (node.className == "android.widget.TextView" && node.text != null) {
            emailNodes.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findEmailNodes(child, emailNodes)
            }
        }
    }


   /* private fun String.extractEmail(): String? {
        val regex = "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}".toRegex()
        return regex.find(this)?.value
    }

*/

    override fun onInterrupt() {

    }




}
