
package com.tech.ekvayu.EkService
import android.accessibilityservice.AccessibilityService
import android.view.accessibility.AccessibilityEvent


class OutlookAccessibilityService : AccessibilityService() {


    private var userName: String? = null
    private var fromEmail: String? = null
    private var toEmail: String? = null
    private var body: String? = null
    private var date: String? = null


    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return
       if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
           val emailDetails = splitOutlookEmailData(event.text.toString())
           if (emailDetails["Body"] != null) {
               date = emailDetails["Date"]
               body = emailDetails["Body"]
               userName = emailDetails["UserName"]
               fromEmail = emailDetails["FromEmail"]
           } else {
               val mailOnmailClick = splitMailData(event.text.toString())
               userName = mailOnmailClick["UserName"]
               fromEmail = mailOnmailClick["Email"]
            //   Log.d("UserData", "Basic mail details found: UserName=$userName, FromEmail=$fromEmail")
           }
        /*   Log.d("UserData", "userName: $userName")
           Log.d("UserData", "date: $date")
           Log.d("UserData", "body: $body")
           Log.d("UserData", "fromEmail: $fromEmail")*/

           if (fromEmail!=null)
           {
              // CommonUtils(applicationContext).emlFile(userName,date,body,fromEmail)
           }
       }

    }

    fun splitOutlookEmailData(eventText: String): Map<String, String> {
        val emailDetails = mutableMapOf<String, String>()
        val parts = eventText.split(",", limit = 4)

        if (parts.isNotEmpty()) {
            emailDetails["UserName"] = parts[0].trim()
        }

        if (parts.size > 1) {
            val secondPart = parts[1].trim()
            val datePattern = Regex("\\b(Feb|Jan|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \\d{1,2}\\b")
            val dateMatch = datePattern.find(secondPart)
            if (dateMatch != null) {
                emailDetails["Date"] = dateMatch.value.trim()
            }
        }

        if (parts.size > 2) {
            emailDetails["Body"] = parts[2].trim()
        }

        val emailPattern = Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")
        val emailMatch = emailPattern.find(eventText)
        emailMatch?.let {
            emailDetails["FromEmail"] = it.value
        }

        return emailDetails
    }

    fun splitMailData(eventText: String): Map<String, String> {
        val mailDetails = mutableMapOf<String, String>()
        val lines = eventText.split("\n").map { it.trim() }

        if (lines.isNotEmpty()) {
            mailDetails["UserName"] = lines[0]
        }

        if (lines.size > 1) {
            val dateTimePattern = Regex("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3}")
            val dateTimeMatch = dateTimePattern.find(lines[1])
            dateTimeMatch?.let {
                mailDetails["DateTime"] = it.value
            }
        }

        val emailPattern = Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")
        val emailMatch = emailPattern.find(eventText)
        emailMatch?.let {
            mailDetails["Email"] = it.value
        }

        return mailDetails
    }

    override fun onInterrupt() {

    }
}
