package com.tech.ekvayu.EkService

import android.accessibilityservice.AccessibilityService
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.SharedPrefManager
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import java.io.IOException


class YahooAccessibilityService : AccessibilityService() {

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return
        if (event.packageName == "com.yahoo.mobile.client.android.mail") {
                if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
                if (event.text.toString() != "[Back]") {
                    val profileString = event.text.toString()

                    Log.d("getStringggg", "onAccessibilityEvent: "+profileString)
                    val profileParams = profileString.split(",").map { it.trim() }

                    val fromMail = "<EMAIL>"
                    val toMail = "<EMAIL>"

                    val senderName = profileParams.getOrNull(0) ?: ""
                    val subject = profileParams.getOrNull(1) ?: ""
                    val date = profileParams.getOrNull(2) ?: ""
                    val image = profileParams.getOrNull(3) ?: ""
                    val logo = profileParams.getOrNull(4) ?: ""
                    val emailBody = profileParams.getOrNull(5) ?: ""
                   // Log.d("getAllData", "fromMail " + fromMail + " toMail " + toMail + " secondName  " + senderName + "  subject " + subject + " date " + date + "  image " + image + " logo " + logo + " emailBody " + emailBody)
                    //CommonUtils(applicationContext).emlFile(senderName,date,emailBody,fromMail)

                /*    val accessToken = sharedPref.getString(AppConstant.authtoken, "")
                    val mailID = sharedPref.getString(AppConstant.mailId, "")
                    getUserInfo(accessToken,mailID)
                    findMessageIdFromAPI(emailBody,accessToken)*/


                    val source = event.source ?: return
                    val emailTitle = source.text?.toString() ?: return
                  //  Log.d("YahooService", "Clicked on: $emailTitle")

                }
            }
        }
    }



    fun getUserInfo(accessToken: String, mailID: String) {
        val url = "https://api.login.yahoo.com/openid/v1/userinfo"

        val request = Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer $accessToken")
            .addHeader("Content-Type", "application/json")
            .get()
            .build()

        val client = OkHttpClient()
        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("YahooAPI", "Failed to get user info: ${e.message}")
            }

            override fun onResponse(call: Call, response: Response) {
                if (response.isSuccessful) {
                    val responseBody = response.body?.string()
                    Log.d("UserInfo", "User Info: $responseBody")
                } else {
                    Log.e("UserInfo", "Failed: ${response.code}, ${response.body?.string()}")
                }
            }
        })

    }

    fun findMessageIdFromAPI(emailDetails: String, accessToken: String) {
        val url = "https://api.login.yahoo.com/ws/mail/v1.1/jsonrpc"

        val requestBody = """
        {
            "method": "Message.list",
            "params": {
                "fid": "Inbox",
                "numInfo": true,
                "startMid": 0,
                "numMsgs": 50
            }
        }
    """.trimIndent()

        val request = Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer $accessToken")
            .addHeader("Content-Type", "application/json")
            .post(requestBody.toRequestBody("application/json".toMediaType()))
            .build()

        val client = OkHttpClient()
        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("YahooAPI", "Failed to fetch emails: ${e.message}")
            }

            override fun onResponse(call: Call, response: Response) {
                if (response.isSuccessful) {
                    val responseBody = response.body?.string()
                    Log.d("YahooAPI", "Emails: $responseBody")

                } else {
                    Log.e("YahooAPI", "Failed to fetch emails: ${response.code}, ${response.body?.string()}")
                }
            }
        })
    }

    override fun onInterrupt() {
    }
}
