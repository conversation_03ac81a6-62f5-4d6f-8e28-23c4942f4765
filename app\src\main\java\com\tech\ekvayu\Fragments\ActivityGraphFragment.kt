package com.tech.ekvayu.Fragments

import android.graphics.Color
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.github.mikephil.charting.utils.ColorTemplate
import com.tech.ekvayu.ActivityGraph.ActivityGraphResponse
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.Request.CommonRequest
import com.tech.ekvayu.databinding.FragmentActivityGraphBinding
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response


class ActivityGraphFragment : Fragment() {
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private lateinit var binding: FragmentActivityGraphBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding= FragmentActivityGraphBinding.inflate(inflater, container, false)

        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
        getAcityGraph(sharedPrefManager.getString(AppConstant.receiverMail,""))

        return binding.root
    }

    private fun getAcityGraph(email: String) {
        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        val apiService = retrofit!!.create(ApiService::class.java)

        val request = CommonRequest(emailId = email)
        CommonUtil.showProgressDialog(requireContext(), "Please wait...")
        apiService.getActivtyGraph(request)
            .enqueue(object : Callback<ActivityGraphResponse> {
                override fun onResponse(
                    call: Call<ActivityGraphResponse>,
                    response: Response<ActivityGraphResponse>
                ) {
                    CommonUtil.hideProgressDialog()
                    if (response.isSuccessful) {

                        val total_disputes=response.body()?.data?.totalDisputes?.toFloat()
                        val total_processed_emails=response.body()?.data?.totalProcessedEmails?.toFloat()
                        val total_spam_emails=response.body()?.data?.totalSpamEmails?.toFloat()

                        val dataMap = mapOf("Dispute Mail" to total_disputes, "Spam Mail" to total_spam_emails, "Process Mail" to total_processed_emails)
                        val entries = dataMap.map { PieEntry(it.value!!, it.key) }

                        binding.tvDispute.text=total_disputes.toString()
                        binding.tvSpam.text=total_spam_emails.toString()
                        binding.tvProcessed.text=total_processed_emails.toString()

                        val dataSet = PieDataSet(entries, "Issue Types")
                        dataSet.colors = ColorTemplate.MATERIAL_COLORS.toList()
                        dataSet.valueTextSize = 16f
                        dataSet.valueTextColor = Color.WHITE

                        val pieData = PieData(dataSet)
                        binding.pieChart.data = pieData

                        binding.pieChart.description.isEnabled = false
                        binding.pieChart.centerText = "Activity"
                        binding.pieChart.setEntryLabelColor(Color.BLACK)
                        binding.pieChart.setDrawEntryLabels(false)
                        binding.pieChart.animateY(1000)
                        binding.pieChart.invalidate()

                    }
                    else
                    {
                        Toast.makeText(requireContext(), response.message(), Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<ActivityGraphResponse?>, t: Throwable) {
                    CommonUtil.hideProgressDialog()
                }

            })
    }


}