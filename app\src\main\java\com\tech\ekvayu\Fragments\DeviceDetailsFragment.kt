package com.tech.ekvayu.Fragments

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.tech.ekvayu.databinding.FragmentDeviceDetailsBinding
import com.tech.ekvayu.models.DeviceInfo
import com.tech.ekvayu.BaseClass.DeviceInfoHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class DeviceDetailsFragment : Fragment() {

    private lateinit var binding: FragmentDeviceDetailsBinding
    private val REQUEST_PERMISSIONS = 101
    private lateinit var deviceInfoHelper: DeviceInfoHelper

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentDeviceDetailsBinding.inflate(inflater, container, false)
        deviceInfoHelper = DeviceInfoHelper(requireContext())
        checkAndRequestPermissions()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        loadDeviceInfo()
        checkAndRequestPermissions()
    }

    private fun checkAndRequestPermissions() {
        val permissions = arrayOf(
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_WIFI_STATE,
            Manifest.permission.ACCESS_NETWORK_STATE
        )

        val permissionsToRequest = permissions.filter {
            ContextCompat.checkSelfPermission(requireContext(), it) != PackageManager.PERMISSION_GRANTED
        }

        if (permissionsToRequest.isNotEmpty()) {
            ActivityCompat.requestPermissions(requireActivity(), permissionsToRequest.toTypedArray(), REQUEST_PERMISSIONS)
        } else {
            loadDeviceInfo()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == REQUEST_PERMISSIONS) {
            val deniedPermissions = permissions.filterIndexed { index, _ ->
                grantResults[index] != PackageManager.PERMISSION_GRANTED
            }

            if (deniedPermissions.isNotEmpty()) {
                Toast.makeText(
                    context,
                    "Some permissions denied. Device info may be limited.",
                    Toast.LENGTH_LONG
                ).show()
            }

            loadDeviceInfo()
        }
    }

    private fun loadDeviceInfo() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val deviceInfo = deviceInfoHelper.getDeviceInfo()
                withContext(Dispatchers.Main) {
                    displayDeviceInfo(deviceInfo)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                 //  binding.tvDetails.text = "Error loading device information: ${e.message}"
                }
            }
        }
    }

    private fun displayDeviceInfo(deviceInfo: DeviceInfo) {

        Log.d("DeviceInfoooo", "displayDeviceInfo: "+deviceInfo.deviceName+"  "+deviceInfo.memoryInfo)

        //Device Details

        binding.tvDeviceName.text=deviceInfo.deviceName
        binding.tvManufacturer.text=deviceInfo.manufacturer
        binding.tvModelName.text=deviceInfo.model
        binding.tvBradName.text=deviceInfo.brand
        binding.tvProductName.text=deviceInfo.product

        //Android Version

        binding.tvAndroidVersion.text=deviceInfo.androidVersion
        binding.tvApiLevel.text=deviceInfo.apiLevel.toString()
        binding.tvBuildNumber.text=deviceInfo.buildNumber.toString()
        binding.tvSerialNumber.text=deviceInfo.serialNumber.toString()
        binding.tvAndroidId.text=deviceInfo.androidId.toString()

        //Screen Info

        binding.tvResolution.text=deviceInfo.screenInfo.getResolution()
        binding.tvDensity.text=deviceInfo.screenInfo.densityDpi.toString()
        binding.tvDensityFactor.text=deviceInfo.screenInfo.density.toString()
        binding.tvScaleDensity.text=deviceInfo.screenInfo.scaledDensity.toString()

         //App Info
        binding.tvPackage.text=deviceInfo.appInfo.packageName
        binding.tvVersion.text=deviceInfo.appInfo.versionName
        binding.tvTargetSdk.text=deviceInfo.appInfo.targetSdkVersion.toString()
        binding.tvMinSdk.text=deviceInfo.appInfo.minSdkVersion.toString()





        val deviceInfoText = buildString {
            appendLine("📱 DEVICE INFORMATION")
            appendLine("=".repeat(50))
            appendLine()

            appendLine("🔧 BASIC INFO")
            appendLine("Device Name: ${deviceInfo.deviceName}")
            appendLine("Manufacturer: ${deviceInfo.manufacturer}")
            appendLine("Model: ${deviceInfo.model}")
            appendLine("Brand: ${deviceInfo.brand}")
            appendLine("Product: ${deviceInfo.product}")
            appendLine()

            appendLine("🤖 ANDROID INFO")
            appendLine("Android Version: ${deviceInfo.androidVersion}")
            appendLine("API Level: ${deviceInfo.apiLevel}")
            appendLine("Build Number: ${deviceInfo.buildNumber}")
            appendLine("Serial Number: ${deviceInfo.serialNumber}")
            appendLine("Android ID: ${deviceInfo.androidId}")
            appendLine()

            appendLine("📺 SCREEN INFO")
            appendLine("Resolution: ${deviceInfo.screenInfo.getResolution()}")
            appendLine("Density: ${deviceInfo.screenInfo.getDensityCategory()} (${deviceInfo.screenInfo.densityDpi} dpi)")
            appendLine("Density Factor: ${deviceInfo.screenInfo.density}")
            appendLine("Scaled Density: ${deviceInfo.screenInfo.scaledDensity}")
            appendLine()

            appendLine("💾 MEMORY INFO")
            appendLine("Total RAM: ${deviceInfo.memoryInfo.getTotalRAMGB()}")
            appendLine("Available RAM: ${deviceInfo.memoryInfo.getAvailableRAMGB()}")
            appendLine("Used RAM: ${deviceInfo.memoryInfo.getUsedRAMGB()} (${deviceInfo.memoryInfo.getRAMUsagePercentage()}%)")
            appendLine("Low Memory: ${if (deviceInfo.memoryInfo.lowMemory) "Yes" else "No"}")
            appendLine("Heap Size: ${formatBytes(deviceInfo.memoryInfo.heapSize)}")
            appendLine("Heap Used: ${formatBytes(deviceInfo.memoryInfo.heapUsed)}")
            appendLine("Heap Max: ${formatBytes(deviceInfo.memoryInfo.heapMax)}")
            appendLine()

            appendLine("💿 STORAGE INFO")
            appendLine("Total Internal: ${deviceInfo.storageInfo.getTotalInternalGB()}")
            appendLine("Free Internal: ${deviceInfo.storageInfo.getFreeInternalGB()}")
            appendLine("Used Internal: ${deviceInfo.storageInfo.getUsedInternalGB()} (${deviceInfo.storageInfo.getStorageUsagePercentage()}%)")
            appendLine()

            appendLine("🌐 NETWORK INFO")
            appendLine("Connected: ${if (deviceInfo.networkInfo.isConnected) "Yes" else "No"}")
            appendLine("Connection Type: ${deviceInfo.networkInfo.connectionType}")
            appendLine("WiFi Enabled: ${if (deviceInfo.networkInfo.wifiEnabled) "Yes" else "No"}")
            appendLine("IP Address: ${deviceInfo.networkInfo.ipAddress}")
            appendLine()

            appendLine("📞 TELEPHONY INFO")
            if (deviceInfo.telephonyInfo.hasPermission) {
                appendLine("Carrier: ${deviceInfo.telephonyInfo.carrierName}")
                appendLine("Country Code: ${deviceInfo.telephonyInfo.countryCode}")
                appendLine("Network Type: ${deviceInfo.telephonyInfo.networkType}")
                appendLine("Phone Type: ${deviceInfo.telephonyInfo.phoneType}")
            } else {
                appendLine("Permission required for telephony info")
            }
            appendLine()

            appendLine("🔋 BATTERY INFO")
            appendLine("Level: ${deviceInfo.batteryInfo.level}%")
            appendLine("Status: ${deviceInfo.batteryInfo.getBatteryStatus()}")
            appendLine("Health: ${deviceInfo.batteryInfo.getBatteryHealth()}")
            appendLine("Temperature: ${deviceInfo.batteryInfo.temperature}")
            appendLine("Voltage: ${deviceInfo.batteryInfo.voltage}")
            appendLine()

            appendLine("📱 APP INFO")
            appendLine("Package: ${deviceInfo.appInfo.packageName}")
            appendLine("Version: ${deviceInfo.appInfo.versionName} (${deviceInfo.appInfo.versionCode})")
            appendLine("Target SDK: ${deviceInfo.appInfo.targetSdkVersion}")
            appendLine("Min SDK: ${deviceInfo.appInfo.minSdkVersion}")
            appendLine()

            appendLine("⚙️ SYSTEM INFO")
            appendLine("Bootloader: ${deviceInfo.systemInfo.bootloader}")
            appendLine("Hardware: ${deviceInfo.systemInfo.hardware}")
            appendLine("Host: ${deviceInfo.systemInfo.host}")
            appendLine("Build ID: ${deviceInfo.systemInfo.id}")
            appendLine("Build Type: ${deviceInfo.systemInfo.type}")
            appendLine("Build User: ${deviceInfo.systemInfo.user}")
            appendLine("Kernel Version: ${deviceInfo.systemInfo.kernelVersion}")
            appendLine("Java VM Version: ${deviceInfo.systemInfo.javaVmVersion}")
            appendLine()

            appendLine("⏰ Generated: ${deviceInfo.timestamp}")
        }
/*
       binding.tvDetails.text = deviceInfoText*/
    }

    private fun formatBytes(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> "%.2f GB".format(bytes / (1024.0 * 1024.0 * 1024.0))
            bytes >= 1024 * 1024 -> "%.2f MB".format(bytes / (1024.0 * 1024.0))
            bytes >= 1024 -> "%.2f KB".format(bytes / 1024.0)
            else -> "$bytes B"
        }
    }

    private operator fun String.times(n: Int): String = this.repeat(n)
}
