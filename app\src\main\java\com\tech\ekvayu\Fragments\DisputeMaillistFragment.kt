package com.tech.ekvayu.Fragments

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.GridLayoutManager
import com.tech.ekvayu.Adapter.DisputeListAdapter
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.BottomSheet.RaiseBottomSheetFragment
import com.tech.ekvayu.Dispute.DisputeMailistResponse
import com.tech.ekvayu.Dispute.Results
import com.tech.ekvayu.databinding.FragmentDisputeMaillistBinding
import com.tech.ekvayu.databinding.LayoutSpamDetailBinding
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class DisputeMaillistFragment : Fragment(), DisputeListAdapter.OnDisputeMailListClickListener {

    private lateinit var binding: FragmentDisputeMaillistBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding= FragmentDisputeMaillistBinding.inflate(inflater, container, false)

        binding.btRaiseDispute.setOnClickListener {
            val bottomSheet = RaiseBottomSheetFragment()
            bottomSheet.show(requireActivity().supportFragmentManager, bottomSheet.tag)
        }
        getDisputelist()
        return binding.root
    }

  /*  private fun openDisputeForm() {
        val sharedPrefManager = SharedPrefManager.getInstance(requireContext())
        val hashId=sharedPrefManager.getString(AppConstant.hashId, "")
        val receiverMail=sharedPrefManager.getString(AppConstant.receiverMail, "")
        Log.d("hashIdReciverMail", "onCreateView: "+hashId+" = "+receiverMail)

        val binding = LayoutDisputeRaiseBinding.inflate(LayoutInflater.from(context))
        val retrofit = ApiClient.getRetrofitInstance()
        val apiService = retrofit.create(ApiService::class.java)
        val request = PendingMailRequest(email = receiverMail, hashId = hashId)

        apiService.pendingMailStatusAi(request)
            .enqueue(object : Callback<PedingMailRes> {
                override fun onResponse(
                    call: Call<PedingMailRes>,
                    response: Response<PedingMailRes>
                ) {
                    if (response.isSuccessful) {
                        binding.etMessageId.setText(response.body()?.data?.hashId).toString()
                        binding.etSenderMail.setText(response.body()?.data?.email).toString()
                        binding.etStatus.setText(response.body()?.data?.emlStatus).toString()
                        val disputeCounter=response.body()?.data?.dispute_counter.toString()
                        val htmlText = "<font color='#FF0000'>$disputeCounter</font> Of 3"
                        binding.etCounter.setText(Html.fromHtml(htmlText, Html.FROM_HTML_MODE_LEGACY))
                    }
                }
                override fun onFailure(call: Call<PedingMailRes?>, t: Throwable) {
                }
            })
        AlertDialog.Builder(requireActivity())
            .setView(binding.root)
            .setCancelable(true)
            .setPositiveButton("Close", null)
            .show()

        binding.btSubmit.setOnClickListener {

            binding.etReason.text?.let { it1 ->
                if (it1.isEmpty()) {
                    Toast.makeText(requireContext(), "Please Enter the reason", Toast.LENGTH_SHORT).show()
                }
                else
                {
                    val retrofit = ApiClient.getRetrofitInstance()
                    val apiService = retrofit.create(ApiService::class.java)
                    val request = DisputeRaiseRequest(email = receiverMail, hashId = hashId, userComment =binding.etReason.text.toString())

                    CommonProgress.showProgressDialog(requireContext(), "Please wait...")
                    apiService.disputeRaise(request)
                        .enqueue(object : Callback<DisputeRaiseResponse> {
                            override fun onResponse(call: Call<DisputeRaiseResponse?>, response: Response<DisputeRaiseResponse?>) {
                                CommonProgress.hideProgressDialog()
                                // Log.d("getbodyofdisputeRaise", "onResponse: "+response.body())
                                if (response.isSuccessful) {
                                    Toast.makeText(requireContext(), response.body()!!.disputeInfoId.toString(), Toast.LENGTH_SHORT).show()
                                }
                            }

                            override fun onFailure(call: Call<DisputeRaiseResponse?>, t: Throwable) {
                                CommonProgress.hideProgressDialog()
                            }

                        })
                }
            }
        }
    }
*/
    private fun getDisputelist() {
        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        val apiService = retrofit!!.create(ApiService::class.java)
        CommonUtil.showProgressDialog(requireContext(), "Please wait...")
        apiService.getDisputelist()
            .enqueue(object : Callback<DisputeMailistResponse> {
                override fun onResponse(call: Call<DisputeMailistResponse?>, response: Response<DisputeMailistResponse?>) {
                    CommonUtil.hideProgressDialog()
                    if (response.isSuccessful) {
                        val adapter = DisputeListAdapter(response.body()!!.results,this@DisputeMaillistFragment)
                        binding.rvDisputelist.adapter = adapter
                        binding.rvDisputelist.layoutManager = GridLayoutManager(requireActivity(), 1)
                    }
                    else
                    {
                        Toast.makeText(requireContext(), response.message(), Toast.LENGTH_SHORT).show()
                    }
                }
                override fun onFailure(call: Call<DisputeMailistResponse?>, t: Throwable) {
                    CommonUtil.hideProgressDialog()
                }
            })
    }

    override fun onDisputeClicked(item: com.tech.ekvayu.Dispute.Results) {
        showCustomEmailDialog(requireContext(),item)
    }

    private fun showCustomEmailDialog(context: Context, item: Results) {
        val binding = LayoutSpamDetailBinding.inflate(LayoutInflater.from(context))
        binding.tvSubject.text = item.subject
        binding.tvSender.text = "From: ${item.sendersEmail}"
        binding.tvReceiver.text = "To: ${item.recieversEmail}"
        binding.tvStatus.text = "Status: ${item.status}"
        binding.tvBody.text = item.overallAiStatus.toString()

        AlertDialog.Builder(context)
            .setView(binding.root)
            .setCancelable(true)
            .setPositiveButton("Close", null)
            .show()
    }

}