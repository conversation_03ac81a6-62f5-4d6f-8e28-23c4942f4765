package com.tech.ekvayu.Fragments

import android.accessibilityservice.AccessibilityService
import android.accounts.AccountManager
import android.animation.ObjectAnimator
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.GridLayoutManager
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.github.mikephil.charting.utils.ColorTemplate
import com.google.mlkit.common.sdkinternal.CommonUtils
import com.tech.ekvayu.Activities.DashboardActivity
import com.tech.ekvayu.ActivityGraph.ActivityGraphResponse
import com.tech.ekvayu.Adapter.DashboardMenuAdapter
import com.tech.ekvayu.Adapter.DashboardMenuAdapter.onClickEventListner
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.BaseClass.PermissionHelper
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.BottomSheet.EmailConfigBottomSheetFragment
import com.tech.ekvayu.BottomSheet.SuggetionBottomFragment
import com.tech.ekvayu.BottomSheet.WarningBottomSheetFragment
import com.tech.ekvayu.EkService.NewGmailAccessibilityService
import com.tech.ekvayu.R
import com.tech.ekvayu.Request.CommonRequest
import com.tech.ekvayu.databinding.FragmentHomeBinding
import com.tech.ekvayu.models.DashboardMenuModel
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class HomeFragment : Fragment(),onClickEventListner {
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }
    private lateinit var binding: FragmentHomeBinding
    private var receiverMail=""
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding=FragmentHomeBinding.inflate(inflater,container,false)

        receiverMail=  sharedPrefManager.getString(AppConstant.receiverMail,"")

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        menus()
        getAcityGraph(receiverMail)


    }

    private fun getAcityGraph(email: String) {
        val retrofit = ApiClient.getRetrofitInstance(requireContext())
          val apiService = retrofit!!.create(ApiService::class.java)

        val request = CommonRequest(emailId = email)
        CommonUtil.showProgressDialog(requireContext(), "Please wait...")
        apiService.getActivtyGraph(request)
            .enqueue(object : Callback<ActivityGraphResponse> {
                override fun onResponse(
                    call: Call<ActivityGraphResponse>,
                    response: Response<ActivityGraphResponse>
                ) {
                    CommonUtil.hideProgressDialog()
                    if (response.isSuccessful) {

                        Log.d("getBody", "onResponse: "+response.body())

                        val total_disputes=response.body()?.data?.totalDisputes?.toFloat()
                        val total_processed_emails=response.body()?.data?.totalProcessedEmails?.toFloat()
                        val total_spam_emails=response.body()?.data?.totalSpamEmails?.toFloat()

                        Log.d("gettotalalldata", "onResponse: "+total_disputes+" =  "+total_spam_emails+"  = "+total_processed_emails)

                       // val dataMap = mapOf("Dispute Mail" to total_disputes, "Spam Mail" to total_spam_emails, "Process Mail" to total_processed_emails)
                        val dataMap = mapOf("Dispute" to total_disputes, "Spam" to total_spam_emails, "Process" to total_processed_emails)
                        val entries = dataMap.map { PieEntry(it.value!!, it.key) }

                        // Set up dataset
                        val dataSet = PieDataSet(entries, "Issue Types")
                        dataSet.colors = ColorTemplate.MATERIAL_COLORS.toList()
                        dataSet.valueTextSize = 5f
                        dataSet.valueTextColor = Color.WHITE

                        // Set data to PieChart
                        val pieData = PieData(dataSet)
                        binding.pieChart.data = pieData

                        // Customize chart
                        binding. pieChart.description.isEnabled = false
                        binding. pieChart.centerText = "Activity"
                        binding. pieChart.setEntryLabelColor(Color.WHITE)
                        binding. pieChart.animateY(1000)
                        binding. pieChart.invalidate()
                        binding.pieChart.setDrawEntryLabels(false)
                        binding.pieChart.legend.textColor = Color.WHITE

                        //
                        binding.tvDispute.text="\uFE0F\uD83D\uDEE1\uFE0F "+" "+total_disputes+" Dispute Mails"
                        binding.tvSpam.text="❗  "+total_spam_emails+" Spam Mails"
                        binding.tvProcess.text="⚖\uFE0F "+" "+total_processed_emails+" Process Mails"
                    }
                    else
                    {
                        Toast.makeText(requireContext(), response.message(), Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<ActivityGraphResponse?>, t: Throwable) {
                    CommonUtil.hideProgressDialog()
                }

            })
    }

    private fun menus() {
        val menuItems = listOf(
            DashboardMenuModel("Details","View your device-specific information", R.drawable.details),
            DashboardMenuModel("Spam Mails","Review and handle detected spam emails", R.drawable.spam_mail),
            DashboardMenuModel("Dispute Mails","See emails you've raised disputes against", R.drawable.argument),
            DashboardMenuModel("Activity", "Track all recent actions taken on emails",R.drawable.process),
            DashboardMenuModel("Email Config", "Configure your primary email settings",R.drawable.details)
           // DashboardMenuModel("Dispute", "Manually raise a new dispute on suspicious emails",R.drawable.dispute)
        )

        val adapter = DashboardMenuAdapter(menuItems, this)
        binding.rvMenu.adapter = adapter
        binding.rvMenu.layoutManager = GridLayoutManager(requireActivity(), 2)
    }

    override fun onResume() {
        super.onResume()
        checkPermissionsAndShowBottomSheets()
    }

    private fun checkPermissionsAndShowBottomSheets() {
        // Use PermissionHelper to check and update all permission states
        val isServiceEnabled = PermissionHelper.checkAndUpdateAccessibilityPermission(requireContext())
        val isMailConfigured = PermissionHelper.checkAndUpdateMailConfiguration()
        val permissionStates = PermissionHelper.getPermissionStates()

        // Update current mail status
        receiverMail = permissionStates.receiverMail

        // Show bottom sheets based on conditions
        when {
            // Case 1: Accessibility permission not granted
            !isServiceEnabled -> {
                val bottomSheet_warning = WarningBottomSheetFragment()
                bottomSheet_warning.setCancelable(false)
                bottomSheet_warning.show(requireActivity().supportFragmentManager, bottomSheet_warning.tag)
            }
            // Case 2: Accessibility permission granted but mail not configured
            isServiceEnabled && !isMailConfigured -> {
                val bottomSheet_suggetion = SuggetionBottomFragment()
                bottomSheet_suggetion.setCancelable(false)
                bottomSheet_suggetion.show(requireActivity().supportFragmentManager, bottomSheet_suggetion.tag)
            }
            // Case 3: Both conditions met - no bottom sheet needed
            isServiceEnabled && isMailConfigured -> {
                // Both accessibility permission and mail are configured
                // No bottom sheet needed - user can use the app normally
            }
        }
    }

   override fun onMenuClick(item: DashboardMenuModel) {
        when(item.title){
            "Details"->{
                (activity as DashboardActivity).showFragment(DeviceDetailsFragment())
            }
            "Spam Mails"->{
                (activity as DashboardActivity).showFragment(SpamMailFragment())
            }
            "Dispute Mails"->{
                (activity as DashboardActivity).showFragment(DisputeMaillistFragment())
            }
            "Activity"-> {
                (activity as DashboardActivity).showFragment(ActivityGraphFragment())
            }
            "Email Config"-> {
                val emailConfigBottomSheet = EmailConfigBottomSheetFragment.newInstance()
                emailConfigBottomSheet.show(requireActivity().supportFragmentManager, emailConfigBottomSheet.tag)
            }
            else->{
                Toast.makeText(requireActivity(),"Something Went Wrong",Toast.LENGTH_SHORT).show()
            }
        }
    }



}