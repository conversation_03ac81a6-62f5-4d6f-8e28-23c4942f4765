package com.tech.ekvayu.Fragments

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.GridLayoutManager
import com.tech.ekvayu.Adapter.SpamMailAdapter
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.Request.CommonRequest
import com.tech.ekvayu.Response.Results
import com.tech.ekvayu.Response.SpamMailResponse
import com.tech.ekvayu.databinding.FragmentSpamMailBinding
import com.tech.ekvayu.databinding.LayoutSpamDetailBinding
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response


class SpamMailFragment : Fragment() , SpamMailAdapter.OnSpamMailClickListener{
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }


    private lateinit var binding: FragmentSpamMailBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        binding= FragmentSpamMailBinding.inflate(inflater, container, false)

        getSpamMail(sharedPrefManager.getString(AppConstant.receiverMail,""))
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getSpamMail(sharedPrefManager.getString(AppConstant.receiverMail,""))
    }


    private fun getSpamMail(email: String) {

        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        val apiService = retrofit!!.create(ApiService::class.java)

        val request = CommonRequest(emailId = email)


        CommonUtil.showProgressDialog(requireContext(), "Please wait...")
        apiService.getSpamMail(request)
            .enqueue(object : Callback<SpamMailResponse> {
                override fun onResponse(
                    call: Call<SpamMailResponse>,
                    response: Response<SpamMailResponse>
                ) {
                    CommonUtil.hideProgressDialog()
                    if (response.isSuccessful) {
                        val adapter = SpamMailAdapter(response.body()!!.results,this@SpamMailFragment)
                        binding.rvSpamMail.adapter = adapter
                        binding.rvSpamMail.layoutManager = GridLayoutManager(requireActivity(), 1)
                    }
                    else
                    {
                        Toast.makeText(requireContext(), response.message(), Toast.LENGTH_SHORT).show()
                    }
                }
                override fun onFailure(call: Call<SpamMailResponse?>, t: Throwable) {
                    CommonUtil.hideProgressDialog()
                }


            })
    }

    override fun onSpamClicked(item: Results) {
        showCustomEmailDialog(requireContext(),item)
    }

    private fun showCustomEmailDialog(context: Context, item: Results) {
        val binding = LayoutSpamDetailBinding.inflate(LayoutInflater.from(context))
        binding.tvSubject.text = item.subject
        binding.tvSender.text = "From: ${item.sendersEmail}"
        binding.tvReceiver.text = "To: ${item.recieversEmail}"
        binding.tvStatus.text = "Status: ${item.status}"
        binding.tvBody.text = item.emailBody.toString()

        AlertDialog.Builder(context)
            .setView(binding.root)
            .setCancelable(true)
            .setPositiveButton("Close", null)
            .show()
    }





}