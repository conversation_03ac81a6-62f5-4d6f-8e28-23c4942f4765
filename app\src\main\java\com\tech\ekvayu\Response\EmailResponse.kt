package com.tech.ekvayu.Response

import com.google.gson.annotations.SerializedName

data class EmailResponse(
    @SerializedName("message"        ) var message       : String? = null,
    @SerializedName("STATUS"         ) var STATUS        : String? = null,
    @SerializedName("Code"           ) var Code          : Int?    = null,
    @SerializedName("email_status"   ) var emailStatus   : String? = null,
    @SerializedName("hashId"         ) var hashId        : String? = null,
    @SerializedName("unsafe_reasons" ) var unsafeReasons : String? = null
)
