package com.tech.ekvayu.Response

import com.google.gson.annotations.SerializedName

data class SpamMailResponse(
    @SerializedName("count"    ) var count    : Int?               = null,
    @SerializedName("next"     ) var next     : String?            = null,
    @SerializedName("previous" ) var previous : String?            = null,
    @SerializedName("results"  ) var results  : ArrayList<Results> = arrayListOf()

)

data class Results (
    @SerializedName("u_id"             ) var uId            : String?  = null,
    @SerializedName("recievers_email"  ) var recieversEmail : String?  = null,
    @SerializedName("senders_email"    ) var sendersEmail   : String?  = null,
    @SerializedName("eml_file_name"    ) var emlFileName    : String?  = null,
    @SerializedName("plugin_id"        ) var pluginId       : String?  = null,
    @SerializedName("msg_id"           ) var msgId          : String?  = null,
    @SerializedName("status"           ) var status         : String?  = null,
    @SerializedName("subject"          ) var subject        : String?  = null,
    @SerializedName("urls"             ) var urls           : String?  = null,
    @SerializedName("create_time"      ) var createTime     : String?  = null,
    @SerializedName("bcc"              ) var bcc            : String?  = null,
    @SerializedName("cc"               ) var cc             : String?  = null,
    @SerializedName("attachments"      ) var attachments    : String?  = null,
    @SerializedName("ipv4"             ) var ipv4           : String?  = null,
    @SerializedName("browser"          ) var browser        : String?  = null,
    @SerializedName("email_body"       ) var emailBody      : String?  = null,
    @SerializedName("cdr_file"         ) var cdrFile        : String?  = null,
    @SerializedName("mail_received_at" ) var mailReceivedAt : String?  = null,
    @SerializedName("is_training_data" ) var isTrainingData : Boolean? = null
)
