package com.tech.ekvayu.models



data class DeviceInfo(
    val deviceName: String,
    val manufacturer: String,
    val model: String,
    val brand: String,
    val product: String,
    val androidVersion: String,
    val apiLevel: Int,
    val buildNumber: String,
    val serialNumber: String,
    val androidId: String,
    val screenInfo: ScreenInfo,
    val memoryInfo: MemoryInfo,
    val storageInfo: StorageInfo,
    val networkInfo: NetworkInfo,
    val telephonyInfo: TelephonyInfo,
    val batteryInfo: BatteryInfo,
    val systemInfo: SystemInfo,
    val appInfo: AppInfo,
    val timestamp: String
)

data class ScreenInfo(
    val width: Int,
    val height: Int,
    val density: Float,
    val densityDpi: Int,
    val scaledDensity: Float
) {
    fun getResolution(): String = "${width}x${height}"
    fun getDensityCategory(): String = when {
        densityDpi <= 120 -> "LDPI"
        densityDpi <= 160 -> "MDPI"
        densityDpi <= 240 -> "HDPI"
        densityDpi <= 320 -> "XHDPI"
        densityDpi <= 480 -> "XXHDPI"
        densityDpi <= 640 -> "XXXHDPI"
        else -> "ULTRA"
    }
}

data class MemoryInfo(
    val totalRAM: Long,
    val availableRAM: Long,
    val usedRAM: Long,
    val lowMemory: Boolean,
    val heapSize: Long,
    val heapFree: Long,
    val heapUsed: Long,
    val heapMax: Long
) {
    fun getTotalRAMGB(): String = "%.2f GB".format(totalRAM / (1024.0 * 1024.0 * 1024.0))
    fun getAvailableRAMGB(): String = "%.2f GB".format(availableRAM / (1024.0 * 1024.0 * 1024.0))
    fun getUsedRAMGB(): String = "%.2f GB".format(usedRAM / (1024.0 * 1024.0 * 1024.0))
    fun getRAMUsagePercentage(): Int = ((usedRAM.toDouble() / totalRAM.toDouble()) * 100).toInt()
}

data class StorageInfo(
    val totalInternal: Long,
    val freeInternal: Long,
    val usedInternal: Long
) {
    fun getTotalInternalGB(): String = "%.2f GB".format(totalInternal / (1024.0 * 1024.0 * 1024.0))
    fun getFreeInternalGB(): String = "%.2f GB".format(freeInternal / (1024.0 * 1024.0 * 1024.0))
    fun getUsedInternalGB(): String = "%.2f GB".format(usedInternal / (1024.0 * 1024.0 * 1024.0))
    fun getStorageUsagePercentage(): Int = ((usedInternal.toDouble() / totalInternal.toDouble()) * 100).toInt()
}

data class NetworkInfo(
    val isConnected: Boolean,
    val connectionType: String,
    val wifiEnabled: Boolean,
    val ipAddress: String
)

data class TelephonyInfo(
    val carrierName: String,
    val countryCode: String,
    val networkType: String,
    val phoneType: String,
    val hasPermission: Boolean
)

data class BatteryInfo(
    val level: Int,
    val isCharging: Boolean,
    val temperature: Int,
    val voltage: Int
) {
    fun getBatteryStatus(): String = if (isCharging) "Charging" else "Not Charging"
    fun getBatteryHealth(): String = when {
        level >= 80 -> "Excellent"
        level >= 60 -> "Good"
        level >= 40 -> "Fair"
        level >= 20 -> "Low"
        else -> "Critical"
    }
}

data class SystemInfo(
    val bootloader: String,
    val hardware: String,
    val fingerprint: String,
    val host: String,
    val id: String,
    val tags: String,
    val type: String,
    val user: String,
    val kernelVersion: String,
    val javaVmVersion: String
)

data class AppInfo(
    val packageName: String,
    val versionName: String,
    val versionCode: Long,
    val targetSdkVersion: Int,
    val minSdkVersion: Any
)
