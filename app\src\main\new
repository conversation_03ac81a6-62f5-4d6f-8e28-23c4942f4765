from googleapiclient.discovery import build
from google.oauth2.credentials import Credentials
import base64

# ... (Authentication code using your credentials) ...

# Create the Gmail API service
service = build('gmail', 'v1', credentials=creds)

# Get a list of messages (example: get the first message)
results = service.users().messages().list(userId='me', maxResults=1).execute()
messages = results.get('messages', [])

if messages:
    message_id = messages[0]['id']

    # Get the raw message content
    message = service.users().messages().get(userId='me', id=message_id, format='raw').execute()
    raw_message = message['raw']

    # Decode the raw content
    decoded_message = base64.urlsafe_b64decode(raw_message.encode('utf-8'))

    # Save to .eml file
    with open('message.eml', 'wb') as f:
        f.write(decoded_message)

    print("Message saved to message.eml")
else:
    print("No messages found.")