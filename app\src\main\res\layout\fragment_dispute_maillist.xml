<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".Fragments.DisputeMaillistFragment">


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDisputelist"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btRaiseDispute"
        android:layout_marginBottom="@dimen/_20sdp"
        />



    <androidx.appcompat.widget.AppCompatButton
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/btRaiseDispute"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:layout_margin="@dimen/_10sdp"
        android:elevation="@dimen/_10sdp"
        android:paddingStart="@dimen/_15sdp"
        android:paddingEnd="@dimen/_15sdp"
        android:textAllCaps="false"
        android:visibility="visible"
        android:background="@drawable/bg_button_app_color"
        android:fontFamily="@font/roboto_semi_medium"
        android:textSize="@dimen/_12sdp"
        android:textColor="@color/white"
        android:text="@string/raise_dispute"
        />

</androidx.constraintlayout.widget.ConstraintLayout>