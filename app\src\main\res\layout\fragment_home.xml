<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    tools:context=".Fragments.HomeFragment">


    <androidx.cardview.widget.CardView
        android:id="@+id/crTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_15sdp"
        android:layout_marginTop="@dimen/_30sdp"
        android:elevation="@dimen/_10sdp"
        app:cardBackgroundColor="@color/app_color"
        app:cardCornerRadius="@dimen/_20sdp"
        app:cardElevation="@dimen/_10sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/llMain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_10sdp"
            android:orientation="vertical"
            android:padding="@dimen/_10sdp">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvDispute"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_semi_regular"
                android:text="...."
                android:textColor="@color/white"
                android:textSize="@dimen/_10sdp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvSpam"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_semi_regular"
                android:text="..."
                android:textColor="@color/white"
                android:textSize="@dimen/_10sdp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvProcess"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_semi_regular"
                android:text="..."
                android:textColor="@color/grey"
                android:textSize="@dimen/_10sdp" />


        </androidx.appcompat.widget.LinearLayoutCompat>


        <com.github.mikephil.charting.charts.PieChart
            android:id="@+id/pieChart"
            android:layout_width="@dimen/_120sdp"
            android:layout_height="@dimen/_120sdp"
            android:layout_gravity="end"
            android:layout_marginVertical="@dimen/_10sdp"
            android:layout_marginHorizontal="@dimen/_15sdp"
            />

    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvMails"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/crTop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_20sdp"
        android:text="Mails"
        android:visibility="gone"
        android:fontFamily="@font/roboto_semi_bold"
        android:textSize="@dimen/_12sdp"
        android:textColor="@color/dark_grey"
        />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvMenu"
        app:layout_constraintTop_toBottomOf="@+id/tvMails"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btPermission"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginBottom="@dimen/_10sdp"
        android:layout_marginHorizontal="@dimen/_10sdp"
        />


    <androidx.appcompat.widget.AppCompatButton
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/btPermission"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:layout_marginHorizontal="@dimen/_20sdp"
        android:layout_margin="@dimen/_12sdp"
        android:elevation="@dimen/_10sdp"
        android:paddingStart="@dimen/_15sdp"
        android:paddingEnd="@dimen/_15sdp"
        android:textAllCaps="false"
        android:visibility="gone"
        android:background="@drawable/bg_button_app_color"
        android:fontFamily="@font/roboto_semi_medium"
        android:textSize="@dimen/_12sdp"
        android:textColor="@color/white"
        android:text="@string/permission"
        />



</androidx.constraintlayout.widget.ConstraintLayout>