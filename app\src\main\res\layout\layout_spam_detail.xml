<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.cardview.widget.CardView
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:id="@+id/cvMailItem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            app:cardElevation="@dimen/_10sdp"
            android:elevation="@dimen/_10sdp"
            app:cardCornerRadius="12dp"
            android:foreground="?attr/selectableItemBackground">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="12dp">

                <!-- Sender Email -->
                <TextView
                    android:id="@+id/tvSender"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Sender: "
                    android:textStyle="bold"
                    android:textColor="@android:color/black"
                    android:textSize="14sp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <!-- Receiver Email -->
                <TextView
                    android:id="@+id/tvReceiver"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Receiver: "
                    android:textColor="@android:color/darker_gray"
                    android:textSize="13sp"
                    app:layout_constraintTop_toBottomOf="@id/tvSender"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <!-- Subject -->
                <TextView
                    android:id="@+id/tvSubject"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Subject"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="@android:color/holo_blue_dark"
                    app:layout_constraintTop_toBottomOf="@id/tvReceiver"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginTop="6dp"/>

                <!-- Status -->
                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Status: Safe"
                    android:textStyle="bold"
                    android:textColor="@android:color/holo_red_dark"
                    app:layout_constraintTop_toBottomOf="@id/tvSubject"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginTop="6dp"/>

                <!-- Attachment -->
                <TextView
                    android:id="@+id/tvAttachment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Attachment: None"
                    android:textColor="@android:color/holo_orange_dark"
                    app:layout_constraintTop_toBottomOf="@id/tvStatus"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginTop="6dp"/>

                <!-- Email Body -->
                <TextView
                    android:id="@+id/tvBody"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Email Body"
                    android:textColor="@android:color/black"
                    android:textSize="13sp"
                    app:layout_constraintTop_toBottomOf="@id/tvAttachment"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginTop="6dp"/>


            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>
    </androidx.constraintlayout.widget.ConstraintLayout>





</ScrollView>