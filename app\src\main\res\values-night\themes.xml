<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme (Dark Mode) -->
    <style name="Base.Theme.Ekvayu" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary Colors -->
        <item name="colorPrimary">@color/app_color</item>
        <item name="colorPrimaryVariant">@color/app_color_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        <!-- Secondary Colors -->
        <item name="colorSecondary">@color/app_color_light</item>
        <item name="colorSecondaryVariant">@color/app_color</item>
        <item name="colorOnSecondary">@color/white</item>

        <!-- Background Colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <!-- Status Bar -->
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation Bar -->
        <item name="android:navigationBarColor">@color/background</item>
        <item name="android:windowLightNavigationBar">false</item>

        <!-- Window Background -->
        <item name="android:windowBackground">@color/background</item>

        <!-- Text Colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>

        <!-- Other Colors -->
        <item name="colorError">@color/red</item>
        <item name="colorOnError">@color/white</item>
    </style>

    <!-- Card Style for Dark Mode -->
    <style name="CardStyle">
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">8dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>

    <!-- Button Styles for Dark Mode -->
    <style name="ButtonPrimary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/app_color</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="ButtonSecondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/app_color</item>
        <item name="android:textColor">@color/app_color</item>
    </style>
</resources>