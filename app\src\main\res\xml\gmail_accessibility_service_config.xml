<?xml version="1.0" encoding="utf-8"?>
<!--
<accessibility-service
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:description="@string/accessibility_service_gmail"
    android:packageNames="com.google.android.gm"
    android:accessibilityEventTypes="typeViewClicked|typeViewFocused|typeViewTextChanged|typeWindowContentChanged"
    android:accessibilityFeedbackType="feedbackAllMask"
    android:notificationTimeout="100"
    android:canRetrieveWindowContent="true"
    android:settingsActivity="android.settings.AccessibilitySettings"
    android:accessibilityFlags="flagDefault|flagIncludeNotImportantViews|flagReportViewIds"/>
-->


<accessibility-service
xmlns:android="http://schemas.android.com/apk/res/android"
android:accessibilityEventTypes="typeAllMask"
android:accessibilityFeedbackType="feedbackGeneric"
android:notificationTimeout="100"
android:canRetrieveWindowContent="true"
android:packageNames="com.google.android.gm"
android:accessibilityFlags="flagDefault" />
